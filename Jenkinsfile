pipeline {
    agent any
    stages {
        stage('Init') {
            steps {
                echo 'Testing..'
                telegramSend(message: 'Building job: $PROJECT_NAME ... - Link: $BUILD_URL', chatId: -649539527)
            }
        }
        stage ('Deployments') {
            steps {
                echo 'Deploying to Production environment...'
                echo 'Copy project over SSH...'
                sshPublisher(publishers: [
                    sshPublisherDesc(
                        configName: 'swarm1',
                        transfers:
                            [sshTransfer(
                                cleanRemote: false,
                                excludes: '',
                                execCommand: "docker build -t registry.thinklabs.com.vn:5000/engsuitenewweb ./thinklabsdev/engsuitenewwebCI/ \
                                    && docker image push registry.thinklabs.com.vn:5000/engsuitenewweb \
                                    && docker service rm engsuitenew_web || true \
                                    && docker stack deploy -c ./thinklabsdev/engsuitenewwebCI/docker-compose.yml engsuitenew \
                                    && rm -rf ./thinklabsdev/engsuitenewwebCIB \
                                    && mv ./thinklabsdev/engsuitenewwebCI/ ./thinklabsdev/engsuitenewwebCIB",
                                execTimeout: 600000,
                                flatten: false,
                                makeEmptyDirs: false,
                                noDefaultExcludes: false,
                                patternSeparator: '[, ]+',
                                remoteDirectory: './thinklabsdev/engsuitenewwebCI',
                                remoteDirectorySDF: false,
                                removePrefix: '',
                                sourceFiles: '*, src/, server/, webpack/'
                            )],
                        usePromotionTimestamp: false,
                        useWorkspaceInPromotion: false,
                        verbose: false
                    )
                ])
                telegramSend(message: 'Build - $PROJECT_NAME – # $BUILD_NUMBER – STATUS: $BUILD_STATUS!', chatId: -649539527)
            }
        }
    }
}
