FROM node:18-alpine3.17 as build-step
RUN apk add --update --no-cache \
      autoconf \
      libtool \
      automake \
      nasm \
      gcc \
      make \
      g++ \
      zlib-dev \
      tzdata \
      build-base
WORKDIR /app
COPY ["package.json", "package-lock.json*", "yarn.lock*", "./"]
RUN yarn install --frozen-lockfile
COPY . .
RUN yarn run build

FROM nginx:1.25-alpine
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build-step /app/build /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
