import React, { useState } from "react";
import clsx from "clsx";

import "./ImageView.scss";

function ImageView({ className, src, srcDefault, ...props }) {
  const [isLoaded, setLoaded] = useState(false);
  
  function onLoadImage() {
    if (!isLoaded) setLoaded(true);
  }
  
  return <div className={clsx("image-view-container", { [className]: !!className })}>
    <img
      className="image-view__image"
      src={src} alt=""
      onLoad={onLoadImage}
    />
    {!isLoaded && <div className="image-view__image-default">
      <img src={srcDefault} alt="" />
    </div>}
  </div>;
}

export default ImageView;