.roleplay-instruction-detail-container {
  padding: 24px;

  .roleplay-instruction-detail-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .back-button {
      margin-right: 16px;
      font-size: 16px;
      padding: 0;
    }

    .roleplay-instruction-detail-title {
      font-size: 24px;
      font-weight: 600;
      margin: 0;
    }
  }

  .instruction-information-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .section-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 24px;
      color: #333;
    }

    .instruction-subtitle {
      font-size: 16px;
      font-weight: 600;
      margin-top: 24px;
      margin-bottom: 16px;
      color: #333;
    }

    .instruction-form {
      .persona-instruction-textarea,
      .conversation-instruction-textarea,
      .analyze-instruction-textarea {
        resize: vertical;
        min-height: 150px;
      }

      .persona-instruction-container,
      .conversation-instruction-container,
      .analyze-instruction-container {
        margin-top: 24px;
      }

      .variables-card {
        margin-bottom: 16px;
        border-radius: 8px;
        border: 1px solid #e8e8e8;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
          border-color: #d9d9d9;
        }

        .card-header {
          margin-bottom: 16px;

          .card-title {
            display: flex;
            align-items: center;
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;

            .card-icon {
              margin-right: 8px;
              font-size: 18px;
            }
          }

          .card-description {
            margin: 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
          }
        }

        .variables-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
          gap: 12px;

          .variable-button {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            transition: all 0.3s ease;

            &:hover {
              border-color: var(--primary-color);
              color: var(--primary-color);
              transform: translateY(-2px);
              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }
    }
  }

  .save-button-container {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    margin-top: 24px;
  }
}