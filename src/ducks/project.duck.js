import { getRecentProjects } from '@services/Project';
import { put, takeLatest, select } from 'redux-saga/effects';

export const actionTypes = {
  GetRecentProject: 'Project/GetRecentProject',
  SetRecentProject: 'Project/SetRecentProject',
};

const initialAuthState = {
  recentProjectList: undefined,
};

export const reducer = (state = initialAuthState, action) => {
  switch (action.type) {
    case actionTypes.SetRecentProject: {
      const { recentProjectList } = action.payload;
      return Object.assign({}, state, { recentProjectList });
    }
    default:
      return state;
  }
};

export const actions = {
  getRecentProject: () => ({ type: actionTypes.GetRecentProject }),
  setRecentProject: recentProjectList => ({ type: actionTypes.SetRecentProject, payload: { recentProjectList } }),
};

export function* saga() {
  yield takeLatest(actionTypes.GetRecentProject, function* getRecentProjectSaga() {
    const user = yield select(store => store.auth.user);
    const dataResponse = yield getRecentProjects({ userId: user._id });
    if (dataResponse) {
      yield put(actions.setRecentProject(dataResponse));
    }
  });
}
