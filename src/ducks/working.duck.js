
export const actionTypes = {
  SetWorking: 'Working/SetWorking',
};

const initialAuthState = {
  workingData: undefined,
};

export const reducer = (state = initialAuthState, action) => {
  switch (action.type) {
    case actionTypes.SetWorking: {
      const { workingData } = action.payload;
      return Object.assign({}, state, { workingData });
    }
    default:
      return state;
  }
};

export const actions = {
  setWorking: workingData => ({ type: actionTypes.SetWorking, payload: { workingData } }),
};

export function* saga() {
}
