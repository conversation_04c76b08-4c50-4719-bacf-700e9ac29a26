import { getAllFolder } from '@services/Folder';
import { select, put, takeLatest } from 'redux-saga/effects';

export const actionTypes = {
  GetMyFolder: 'Folder/GetMyFolder',
  SetMyFolder: 'Folder/SetMyFolder',
};

const initialAuthState = {
  myFolderList: undefined,
};

export const reducer = (state = initialAuthState, action) => {
  switch (action.type) {
    case actionTypes.SetMyFolder: {
      const { myFolderList } = action.payload;
      return Object.assign({}, state, { myFolderList });
    }
    default:
      return state;
  }
};

export const actions = {
  getMyFolder: () => ({ type: actionTypes.GetMyFolder }),
  setMyFolder: myFolderList => ({ type: actionTypes.SetMyFolder, payload: { myFolderList } }),
};

export function* saga() {
  yield takeLatest(actionTypes.GetMyFolder, function* getRoleSaga() {
    const user = yield select(store => store.auth.user);
    const dataResponse = yield getAllFolder({ ownerId: user?._id });
    if (dataResponse) {
      yield put(actions.setMyFolder(dataResponse));
    }
  });
}
