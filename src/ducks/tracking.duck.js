import { takeEvery, select, put } from "redux-saga/effects";
import { trackEvent } from "@src/ga";

export const TRACKING_EVENTS = {
  CUSTOM_VIEW: "custom_view",
  CUSTOM_CLICK: "custom_click",
};

export const TRACKING_ACTIONS = {
  VIEW_SCREEN: "view_screen",
  CLICK_NAVIGATION: "click_navigation",
  SELECT_LANGUAGE_RESULT: "select_language_result",
  CLICK_STUDY_WRITING: "click_study_writing",
  CLICK_STUDY_SPEAKING: "click_study_speaking",
  VIEW_USER_GUIDE: "view_user_guide",
  VIEW_ESSAY_LIST: "view_essay_list",
  CLICK_TEST_TYPE_SELECTION: "click_test_type_selection",
  SELECT_TEST_TYPE: "select_test_type",
  INPUT_TOPIC: "input_topic",
  CLICK_UPLOAD: "click_upload",
  UPLOAD_STATUS: "upload_status",
  INPUT_ESSAY: "input_essay",
  <PERSON><PERSON><PERSON><PERSON>: "submit",
  OPEN_OPTION: "open_option",
  <PERSON><PERSON><PERSON><PERSON>_SHARE: "click_share",
  CLICK_DELETE: "click_delete",
  CLICK_TAB: "click_tab",
  CLICK_DOWNLOAD: "click_download",
  VIEW_SPEECH_LIST: "view_speech_list",
  CLICK_CARD: "click_card",
  CLICK_SORT_LIST: "click_sort_list",
  GENERATE_IDEA: "generate_idea",
  FIND_VOCAB: "find_vocab",
};

export const TRACKING_SCREENS = {
  STUDY_HUB: "Study Hub",
  MY_ESSAYS: "My essays",
  MY_SPEECHES: "My speeches",
  PROFILE: "Profile",
};

export const STUDY_HUB_SCREENS = {
  STUDY_WRITING: "Study Writing",
  STUDY_SPEAKING: "Study Speaking",
  VIEW_RESULT: "View Result",
};

export const ACTION_STATUS = {
  SUCCESS: "Success",
  FAILED: "Failed",
}

export const PARAM_CATEGORIES = {
  UPLOAD_TOPIC: "upload_topic",
  UPLOAD_ESSAY: "upload_essay",
  UPLOAD_AUDIO: "upload_audio",
};
export const PARAM_TYPES = {
  CARD_ESSAY: "card_essay",
  CARD_SPEECH: "card_speech",
};

export const getStudyHubScreenFromUrl = (url) => {
  if (url.includes("study-hub/speaking")) return STUDY_HUB_SCREENS.STUDY_SPEAKING;
  if (url.includes("study-hub/writing")) return STUDY_HUB_SCREENS.STUDY_WRITING;
  return null;
};

export const getScreenFromUrl = (url) => {
  if (url.includes("study-hub/speaking/speeches")) return TRACKING_SCREENS.MY_SPEECHES;
  if (url.includes("study-hub/writing/essays")) return TRACKING_SCREENS.MY_ESSAYS;
  if (url.includes("study-hub")) return TRACKING_SCREENS.STUDY_HUB;
  if (url.includes("account")) return TRACKING_SCREENS.PROFILE;
  return null;
};

export const getCommonParams = (referred_url) => ({
  device_category: window.innerWidth < 768 ? "mobile" : "desktop",
  traffic_source: document.referrer || "direct",
  referred_url: referred_url || "",
  referred_screen: getScreenFromUrl(referred_url),
  url: window.location.href,
});

export const actionTypes = {
  CustomEvent: "Tracking/CustomEvent",
  CustomView: "Tracking/CustomView",
  CustomViewStudyHub: "Tracking/CustomViewStudyHub",
  SetReferer: "Tracking/SetReferer",
};

export const actions = {
  trackCustomEvent: (event, event_name, params) => ({
    type: actionTypes.CustomEvent,
    payload: { event, event_name, params },
  }),
  trackCustomView: (params) => ({
    type: actionTypes.CustomView,
    payload: { event: TRACKING_EVENTS.CUSTOM_VIEW, event_name: TRACKING_ACTIONS.VIEW_SCREEN, params },
  }),
  trackCustomViewStudyHub: (params) => ({
    type: actionTypes.CustomViewStudyHub,
    payload: { event: TRACKING_EVENTS.CUSTOM_VIEW, event_name: TRACKING_ACTIONS.VIEW_SCREEN, params },
  }),
  trackCustomClick: (event_name, params) => {
    return {
      type: actionTypes.CustomEvent,
      payload: {
        event: TRACKING_EVENTS.CUSTOM_CLICK, event_name, params: {
          param_view: getStudyHubScreenFromUrl(window.location.href),
          ...params,
        },
      },
    };
  },
  trackCustomClickLabel: (event_name, param_label) => {
    return {
      type: actionTypes.CustomEvent,
      payload: {
        event: TRACKING_EVENTS.CUSTOM_CLICK, event_name, params: {
          param_view: getStudyHubScreenFromUrl(window.location.href),
          param_label,
        },
      },
    };
  },
  trackCustomClickType: (event_name, param_type, param_category) => {
    return {
      type: actionTypes.CustomEvent,
      payload: {
        event: TRACKING_EVENTS.CUSTOM_CLICK, event_name, params: {
          param_view: getStudyHubScreenFromUrl(window.location.href),
          param_type,
          param_category,
        },
      },
    };
  },
  trackClickNavigation: (to_screen) => {
    return {
      type: actionTypes.CustomEvent,
      payload: {
        event: TRACKING_EVENTS.CUSTOM_CLICK, event_name: TRACKING_ACTIONS.CLICK_NAVIGATION, params: {
          param_view: getStudyHubScreenFromUrl(window.location.href),
          param_label: to_screen,
        },
      },
    };
  },
  trackClickAside: (linkTo) => {
    const toScreen = getStudyHubScreenFromUrl(linkTo);
    const event_name = toScreen === STUDY_HUB_SCREENS.STUDY_WRITING ? TRACKING_ACTIONS.CLICK_STUDY_WRITING : TRACKING_ACTIONS.CLICK_STUDY_SPEAKING;
    return {
      type: actionTypes.CustomEvent,
      payload: {
        event: TRACKING_EVENTS.CUSTOM_CLICK, event_name, params: {
          param_view: toScreen,
        },
      },
    };
  },
  trackSelectLanguageResult: (language, param_type) => {
    return {
      type: actionTypes.CustomEvent,
      payload: {
        event: TRACKING_EVENTS.CUSTOM_CLICK, event_name: TRACKING_ACTIONS.SELECT_LANGUAGE_RESULT, params: {
          param_view: getStudyHubScreenFromUrl(window.location.href),
          param_label: language,
          param_type
        },
      },
    };
  },
  setReferer: (url) => ({
    type: actionTypes.SetReferer,
    payload: url,
  }),
};

export const paramsCreators = {
  uploadTopic: (topic) => ({
    param_category: PARAM_CATEGORIES.UPLOAD_TOPIC,
    param_label: topic,
  }),
  uploadEssay: (essay) => ({
    param_category: PARAM_CATEGORIES.UPLOAD_ESSAY,
    param_label: essay,
  }),
  uploadStatus: (param_type, param_category, param_status, param_error_record) => ({
    param_type,
    param_category,
    param_status,
    param_error_record,
  }),
  viewResult: (param_type, param_label) => ({
    param_view: STUDY_HUB_SCREENS.VIEW_RESULT,
    param_type,
    param_label,
  }),
};

export const initialState = {
  referred_url: document.referrer,
};

export const reducer = (state = initialState, action) => {
  switch (action.type) {
    case actionTypes.SetReferer:
      return { ...state, referred_url: action.payload };
    default:
      return state;
  }
};

const selectReferredUrl = (state) => state.tracking?.referred_url || "";

function* getFinalParams(extraParams = {}) {
  const referred_url = yield select(selectReferredUrl); // Lấy referrer từ Redux
  return { ...getCommonParams(referred_url), ...extraParams };
}

function* trackCustomEvent(action) {
  const { event, event_name, params } = action.payload;
  const finalParams = yield getFinalParams(params);
  const currentScreen = getScreenFromUrl(finalParams.url);
  trackEvent(event, event_name, currentScreen, finalParams);
}

function* trackCustomView(action) {
  const { event, event_name, params } = action.payload;
  const finalParams = yield getFinalParams(params);
  const currentScreen = getScreenFromUrl(finalParams.url);
  const oldScreen = getScreenFromUrl(finalParams.referred_url);
  if (currentScreen !== oldScreen && currentScreen) {
    trackEvent(event, event_name, currentScreen, finalParams);
  }
  yield put(actions.setReferer(finalParams.url));
}

function* trackCustomViewStudyHub(action) {
  const { event, event_name, params } = action.payload;
  const finalParams = yield getFinalParams(params);
  const currentScreen = getScreenFromUrl(finalParams.url);
  trackEvent(event, event_name, currentScreen, finalParams);
  yield put(actions.setReferer(finalParams.url));
}

export function* saga() {
  yield takeEvery(actionTypes.CustomEvent, trackCustomEvent);
  yield takeEvery(actionTypes.CustomView, trackCustomView);
  yield takeEvery(actionTypes.CustomViewStudyHub, trackCustomViewStudyHub);
}
