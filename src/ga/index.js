// this file use for set up and add Google Analytics tracking to the project
import <PERSON>act<PERSON> from "react-ga4";
import { useEffect } from "react";
import { v4 as uuidv4 } from 'uuid';

const TRACKING_ID = process.env.GA_ID;

export const initGA = () => {
  console.log('Initializing GA with tracking ID:', TRACKING_ID);
  if (TRACKING_ID) {
    ReactGA.initialize(TRACKING_ID);
  } else {
    console.warn('GA_ID not found in environment variables');
  }
};

export const useAnalyticsEventTracker = (category = "Event") => {
  return (action = "test action", label = "test label") => {
    //ReactGA.event({
    //  category: category,
    //  action: action,
    //  label: label, // optional
    //  //value: 99, // optional, must be a number
    //  //nonInteraction: true, // optional, true/false
    //  //transport: "xhr", // optional, beacon/xhr/image
    //});
  };
};

export const usePageViewTracker = (pageTitle) => {
  useEffect(() => {
    //ReactGA.send({
    //  hitType: "pageview",
    //  page: window.location.pathname + window.location.search,
    //  title: pageTitle || "Unknown Title",
    //});
  }, []);
};

export const trackEvent = (event, action, screen, params = {}) => {
  //console.log("trackEvent", event, action, screen, params.param_view, params.param_type, params.param_label, params);
  const id = uuidv4();
  const timestamp = new Date().toISOString();
  //ReactGA.event({ category: event, action: action, label: screen }, params);
  ReactGA.event(event, { id, timestamp, event, event_name: action, event_screen: screen, ...params });
};