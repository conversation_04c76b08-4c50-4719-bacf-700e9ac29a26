import React from 'react';
import '@src/common/prototype';

import { LINK } from '@src/constants/link';

import Setting from '@app/pages/Setting';

import User from '@app/pages/User';
import UserStudent from '@src/app/pages/Student/UserStudent';
import Payment from '@app/pages/Payment';
import Pricing from '@src/app/pages/Pricing';
import Exchange from '@src/app/pages/Student/Exchange';
import { CoursesListScreen } from '@src/app/pages/RolePlay/Courses/CoursesListScreen';
import { CourseCreationScreen } from '@src/app/pages/RolePlay/Courses/CourseCreationScreen';
import { RolePlaySessionScreen } from '@src/app/pages/RolePlay/RolePlaySession/RolePlaySessionScreen';
import SessionResultScreen from '@app/pages/RolePlay/SessionResultScreen';

const ROUTERS = [

  {
    menuLang: 'Setting',
    path: LINK.SETTING,
    // icon: HOMEPAGE,
    // activeIcon: DASHBOARD_ACTIVE,
    element: <Setting />,
    permissionRequired: null,
  },

  {
    menuLang: 'USERS',
    path: LINK.ACCOUNT,
    permissionRequired: null,
    element: <User />,
  },
  {
    menuLang: 'USERS',
    path: LINK.ACCOUNT,
    permissionRequired: null,
    element: <UserStudent />,
    forStudent: true,
  },


  {
    menuLang: 'PAYMENT',
    path: LINK.PAYMENT_ID.format(':id'),
    permissionRequired: null,
    element: <Payment />,
    forStudent: true,
  },
  {
    menuLang: 'PAYMENT',
    path: LINK.PAYMENT_VNPAY,
    permissionRequired: null,
    element: <Payment />,
    forStudent: true,
  },

  {
    menuLang: 'EXCHANGE',
    path: LINK.EXCHANGE,
    permissionRequired: null,
    element: <Exchange />,
    forStudent: true,
  },
  {
    menuLang: 'Pricing',
    path: LINK.PRICING,
    permissionRequired: null,
    element: <Pricing />,
    forStudent: true,
  },
  {
    menuLang: 'Courses',
    path: LINK.COURSES,
    permissionRequired: null,
    element: <CoursesListScreen />,
    forStudent: true,
  },
  {
    menuLang: 'CoursesCreation',
    path: LINK.COURSE_CREATE,
    permissionRequired: null,
    element: <CourseCreationScreen />,
    forStudent: true,
  },
  {
    menuLang: 'Role Play Courses',
    path: LINK.ROLE_PLAY_COURSES,
    permissionRequired: null,
    element: <CoursesListScreen />,
    forStudent: true,
  },
  {
    path: LINK.ROLE_PLAY_COURSE_CREATE,
    permissionRequired: null,
    element: <CourseCreationScreen />,
    forStudent: true,
  },
  {
    path: LINK.ROLE_PLAY_SESSION.format(':courseId'),
    permissionRequired: null,
    element: <RolePlaySessionScreen />,
    forStudent: true,
  },
  {
    path: LINK.ROLE_PLAY_SESSION_RESULT.format(':courseId', ':sessionId'),
    element: <SessionResultScreen />,
    permissionRequired: null,
    forStudent: true,
  },
];

export { ROUTERS };
