import { TOAST_MESSAGE } from '@src/constants/constant';
import { toastError } from '@app/ConfirmProvider';


function renderMessageError(err, method) {
  if (err && err.message === CONSTANT.CANCEL) return null;
  const errorMethod = method || err?.response?.config?.method || CONSTANT.DEFAULT;
  const messageString = err?.response?.data?.message || TOAST_MESSAGE.ERROR[errorMethod] || TOAST_MESSAGE.ERROR.DEFAULT;
  toastError(messageString);
}


export { renderMessageError };
