<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_2073_9055)">
<rect x="3.29639" y="20.3584" width="35.8824" height="24.7059" rx="4.70588" transform="rotate(-15 3.29639 20.3584)" fill="url(#paint0_linear_2073_9055)"/>
<rect x="3.65661" y="20.5664" width="35.2941" height="24.1176" rx="4.41177" transform="rotate(-15 3.65661 20.5664)" stroke="url(#paint1_linear_2073_9055)" stroke-width="0.588235"/>
</g>
<g filter="url(#filter1_bd_2073_9055)">
<rect x="8.23535" y="15.2941" width="41.1765" height="28.2353" rx="4.70588" fill="#BFB5FF" fill-opacity="0.6" shape-rendering="crispEdges"/>
<rect x="8.52947" y="15.5882" width="40.5882" height="27.6471" rx="4.41177" stroke="url(#paint2_linear_2073_9055)" stroke-width="0.588235" shape-rendering="crispEdges"/>
</g>
<path d="M29.6283 26.4999H29.4812V26.6469V31.9277C29.4812 32.1839 29.4145 32.3727 29.3059 32.4955C29.1996 32.6158 29.0377 32.6906 28.8077 32.6906C28.5838 32.6906 28.4261 32.6151 28.3222 32.4941C28.2155 32.3699 28.1505 32.1804 28.1505 31.9277V26.6469V26.4999H28.0034H26.2405C26.0724 26.4999 25.942 26.4471 25.8547 26.3651C25.7685 26.284 25.7132 26.1638 25.7132 26.0051C25.7132 25.837 25.7693 25.7179 25.8538 25.6399C25.9398 25.5605 26.0699 25.5103 26.2405 25.5103H31.4075C31.5781 25.5103 31.7082 25.5605 31.7942 25.6399C31.8787 25.7179 31.9348 25.837 31.9348 26.0051C31.9348 26.1638 31.8795 26.284 31.7933 26.3651C31.706 26.4471 31.5756 26.4999 31.4075 26.4999H29.6283ZM19.3437 24.1609C19.3437 23.312 19.5665 22.6934 19.9784 22.2861C20.3907 21.8786 21.0158 21.6594 21.8696 21.6594H35.7703C36.628 21.6594 37.2552 21.8805 37.6686 22.2892C38.0816 22.6976 38.3043 23.3163 38.3043 24.1609V25.8995C38.3043 26.1257 38.2366 26.2903 38.1297 26.3981C38.0228 26.5057 37.8605 26.573 37.6389 26.573C37.4219 26.573 37.2591 26.506 37.1508 26.3977C37.0426 26.2895 36.9735 26.1247 36.9735 25.8995V24.3071C36.9735 23.8885 36.8632 23.5507 36.6296 23.3183C36.3961 23.0861 36.0626 22.982 35.6647 22.982H21.9752C21.5772 22.982 21.2437 23.0861 21.0103 23.3183C20.7767 23.5507 20.6663 23.8885 20.6663 24.3071V25.8995C20.6663 26.1257 20.5986 26.2906 20.4923 26.3985C20.3863 26.5061 20.2259 26.573 20.0091 26.573C19.7875 26.573 19.6252 26.5057 19.5183 26.3981C19.4114 26.2903 19.3437 26.1257 19.3437 25.8995V24.1609ZM21.0319 28.9299C21.0319 29.4992 20.569 29.9608 20.0091 29.9608C19.4404 29.9608 18.9781 29.4986 18.9781 28.9299C18.9781 28.3686 19.441 27.8989 20.0091 27.8989C20.5684 27.8989 21.0319 28.3679 21.0319 28.9299ZM38.6618 28.9299C38.6618 29.4992 38.1989 29.9608 37.6389 29.9608C37.0702 29.9608 36.6079 29.4986 36.6079 28.9299C36.6079 28.3611 37.0702 27.8989 37.6389 27.8989C38.1982 27.8989 38.6618 28.3679 38.6618 28.9299ZM35.7703 36.5497H21.8696C21.0161 36.5497 20.3909 36.3286 19.9785 35.92C19.5664 35.5116 19.3437 34.8928 19.3437 34.0482V31.9846C19.3437 31.7584 19.4114 31.5937 19.5183 31.486C19.6252 31.3784 19.7875 31.3111 20.0091 31.3111C20.2259 31.3111 20.3863 31.378 20.4923 31.4856C20.5986 31.5935 20.6663 31.7583 20.6663 31.9846V33.902C20.6663 34.3206 20.7767 34.6583 21.0103 34.8907C21.2437 35.123 21.5772 35.227 21.9752 35.227H35.6647C36.0626 35.227 36.3961 35.123 36.6296 34.8907C36.8632 34.6583 36.9735 34.3206 36.9735 33.902V31.9846C36.9735 31.7584 37.0412 31.5937 37.1482 31.486C37.255 31.3784 37.4174 31.3111 37.6389 31.3111C37.8559 31.3111 38.0187 31.3781 38.127 31.4864C38.2352 31.5946 38.3043 31.7594 38.3043 31.9846V34.0482C38.3043 34.8928 38.0816 35.5115 37.6686 35.9199C37.2552 36.3285 36.628 36.5497 35.7703 36.5497Z" fill="url(#paint3_linear_2073_9055)" fill-opacity="0.8" stroke="url(#paint4_linear_2073_9055)" stroke-width="0.294118"/>
<g filter="url(#filter2_bdd_2073_9055)">
<path d="M45.2049 45.3773V45.2437L45.0719 45.2309C43.3488 45.0656 41.8968 44.3683 40.8759 43.2697C39.8554 42.1715 39.2579 40.664 39.2579 38.863V37.117C39.2579 36.7325 39.5691 36.431 39.9617 36.431C40.3543 36.431 40.6655 36.7325 40.6655 37.117V38.801C40.6655 40.3463 41.1896 41.6378 42.1109 42.543C43.0319 43.448 44.3383 43.9556 45.8821 43.9556C47.426 43.9556 48.7323 43.448 49.6533 42.543C50.5746 41.6378 51.0988 40.3463 51.0988 38.801V37.117C51.0988 36.7345 51.4168 36.431 51.8114 36.431C52.202 36.431 52.5063 36.7306 52.5063 37.117V38.863C52.5063 40.664 51.9088 42.1715 50.8883 43.2697C49.8674 44.3683 48.4154 45.0656 46.6923 45.2309L46.5593 45.2437V45.3773V47.1765V47.3235H46.7064H49.9591C50.3516 47.3235 50.6717 47.6339 50.6717 48.0184C50.6717 48.3951 50.3506 48.7134 49.9591 48.7134H41.8052C41.4137 48.7134 41.0925 48.3951 41.0925 48.0184C41.0925 47.6339 41.4126 47.3235 41.8052 47.3235H45.0579H45.2049V47.1765V45.3773ZM48.8637 38.6592C48.8637 40.5518 47.5939 41.8269 45.8821 41.8269C44.1703 41.8269 42.9006 40.5518 42.9006 38.6592V32.6856C42.9006 30.793 44.1703 29.5179 45.8821 29.5179C47.5939 29.5179 48.8637 30.793 48.8637 32.6856V38.6592Z" fill="url(#paint5_linear_2073_9055)" stroke="url(#paint6_linear_2073_9055)" stroke-width="0.294118"/>
</g>
<defs>
<filter id="filter0_b_2073_9055" x="-2.7058" y="5.06898" width="53.0586" height="45.1559" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="3.52941"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2073_9055"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_2073_9055" result="shape"/>
</filter>
<filter id="filter1_bd_2073_9055" x="2.94123" y="9.99995" width="51.7645" height="38.8236" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2.64706"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2073_9055"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.76471"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.163491 0 0 0 0 0.0698958 0 0 0 0 0.7625 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_2073_9055" result="effect2_dropShadow_2073_9055"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_2073_9055" result="shape"/>
</filter>
<filter id="filter2_bdd_2073_9055" x="19.7646" y="13.8828" width="52.2354" height="52.2354" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2073_9055"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.603922 0 0 0 0 0.329412 0 0 0 0 0.878431 0 0 0 0.48 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_2073_9055" result="effect2_dropShadow_2073_9055"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.603922 0 0 0 0 0.329412 0 0 0 0 0.878431 0 0 0 0.64 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_2073_9055" result="effect3_dropShadow_2073_9055"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_2073_9055" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2073_9055" x1="3.29639" y1="20.3584" x2="40.8827" y2="42.1214" gradientUnits="userSpaceOnUse">
<stop stop-color="#AEA0FF"/>
<stop offset="1" stop-color="#9A54E0"/>
</linearGradient>
<linearGradient id="paint1_linear_2073_9055" x1="22.7327" y1="20.3584" x2="22.3599" y2="45.0587" gradientUnits="userSpaceOnUse">
<stop stop-color="#F8F0FF"/>
<stop offset="1" stop-color="#F8F0FF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint2_linear_2073_9055" x1="30.5393" y1="15.2941" x2="30.115" y2="43.523" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFF1F0"/>
<stop offset="0.0001" stop-color="#E6E2FF"/>
<stop offset="1" stop-color="#E6E2FF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint3_linear_2073_9055" x1="18.6215" y1="19.9692" x2="46.0357" y2="33.5999" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFFAFA"/>
<stop offset="1" stop-color="#FFFAFA" stop-opacity="0.4"/>
</linearGradient>
<linearGradient id="paint4_linear_2073_9055" x1="28.8199" y1="36.6967" x2="29.0461" y2="21.5157" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.8"/>
<stop offset="1" stop-color="white" stop-opacity="0.2"/>
</linearGradient>
<linearGradient id="paint5_linear_2073_9055" x1="39.1108" y1="29.3708" x2="56.7024" y2="34.244" gradientUnits="userSpaceOnUse">
<stop stop-color="#AEA0FF"/>
<stop offset="1" stop-color="#9A54E0"/>
</linearGradient>
<linearGradient id="paint6_linear_2073_9055" x1="52.9409" y1="46.7652" x2="41.4888" y2="31.5302" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.4"/>
<stop offset="1" stop-color="white" stop-opacity="0.8"/>
</linearGradient>
</defs>
</svg>
