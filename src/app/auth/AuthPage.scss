@import "src/app/styles/scroll";

//auth-bg-dark

:root {
  --auth-bg: url("../../asset/background/auth-banner.svg");
  --auth-intro-color: #FFF;
  --auth-header-title-color: #000;
  --auth-divider-color: #FFF;
  --auth-frame-border-color: #F1F1F1;
  --auth-frame-bg-color: rgba(255, 255, 255, 0.50);
  --auth-question-icon-color: #000;
  --auth-question-bg-color: #FFF;
}

[data-theme='dark'] {
  --auth-bg: url("../../asset/background/auth-bg-dark.png");
  --auth-intro-color: #F7E6E5;
  --auth-header-title-color: #FFF;
  --auth-divider-color: #4F4F4F;
  --auth-frame-border-color: #4F4F4F;
  --auth-frame-bg-color: rgba(56, 53, 53, 0.50);
  --auth-question-icon-color: #000;
  --auth-question-bg-color: #FFF;
}

#auth {
  background: #F6F7FB;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-wrap: wrap;
  height: 100vh;

  @extend .scrollbar;
  @extend .scrollbar-show;

  * {
    font-family: Inter, serif !important;
  }

  .auth-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 24px 0;

    .auth-content {
      box-sizing: border-box;
      width: 512px;
      padding: 32px;
      border-radius: 24px;
      gap: 24px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      // align-items: center;
      background: radial-gradient(18.71% 33.59% at 50% 8.03%, #F4F3FF 0.01%, #FFFFFF 100%);
      box-shadow: 0px -4px 0px 0px #FFFFFF;
      z-index: 1;

      .auth-content__logo-wrapper {
        width: 100%;
        display: flex;
        justify-content: center;
      }

      .auth-content__logo {
        max-width: 240px;
        max-height: 57px;
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .auth-content__title {
        font-size: 24px;
        font-weight: 700;
        line-height: 32px;
        text-align: center;
        color: #0c4da2;
      }

      .auth-content__form {
        display: flex;
        flex-direction: column;
        gap: 24px;
        width: 100%;

        .ant-form-item {
          margin-bottom: 0;

          &:focus-visible {
            outline: none;
          }

          .ant-form-item-control-input-content {
            border-radius: 16px;
            background: #E7E5FF;
            box-shadow: 0px 4px 8px 0px #E7E5FF29;

            >input:-webkit-autofill {
              border-radius: 16px;
            }
          }

          .ant-input.ant-input-lg {
            padding: 15px 24px;
            background: none;
            border: none;
            box-shadow: unset;
            outline: unset;
            font-size: 16px;
            line-height: 24px;
          }

          input:-webkit-autofill {
            -webkit-box-shadow: 0 0 0px 1000px #E7E5FF inset !important;
          }

          .ant-input-affix-wrapper.ant-input-affix-wrapper-lg {
            background: none;
            border: none;
            box-shadow: unset;
            outline: unset;
            padding: 15px 24px;

            .ant-input.ant-input-lg {
              padding: 0;
            }
          }

          .ant-form-item-explain-error {
            padding: 6px 16px;
            border-radius: 8px;
            margin: 4px 0 0 0;
            background: #FFDADA;
            color: #FF0307;
            font-size: 14px;
            line-height: 20px;
          }
        }
      }

      .auth-content__back-to {
        display: flex;
        align-items: center;
        gap: 4px;

        img {
          rotate: 90deg;
        }

        a {
          color: #313131;
          font-size: 14px;
          font-weight: 500;
          line-height: 18.2px;
          text-align: left;

          &:not(:hover):not(:active) {
            text-decoration: none;
          }
        }
      }

      .auth-content__redirect {
        display: flex;
        width: fit-content;
        align-self: center;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        text-align: center;
        color: #0c4da2;
        cursor: pointer;

        &:not(:hover):not(:active) {
          text-decoration: none;
        }

        .auth-form__checkbox {
          margin-right: 6px;
        }
      }

      .login-social {
        //background: red;
        margin-top: 24px;
        display: flex;
        justify-content: center;
      }

      .auth-content__submit {
        align-self: center;
        box-shadow: 0px 4px 8px 0px #E7E5FF29;
        border-radius: 16px;
        height: 54px;
        background-color: #0c4da2;
        color: #FFF;
      }

      .auth-content__question {
        text-align: center;
        color: var(--typo-colours-primary-black);
        line-height: 24px;

        .auth-content__question__redirect {
          font-weight: 600;
          color: #3A18CE;
          margin-left: 4px;

          &:not(:hover):not(:active) {
            text-decoration: none;
          }
        }
      }
    }

  }

  .auth-message {
    border-radius: 4px;
    display: flex;
    padding: 8px 24px;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-top: 16px;
    width: 384px;
    box-sizing: border-box;
    z-index: 1;

    &.auth-error {
      background: var(--support-colours-red-light-1);
      color: var(--support-colours-red);

      .auth-message__content__request {
        color: var(--typo-colours-support-blue);
        cursor: pointer;
      }

      .auth-message__content__resend {
        display: flex;
        justify-content: center;
        gap: 4px;
        // color: #000;
      }
    }

    &.auth-warning {
      background: var(--support-colours-yellow-light-1);

      .auth-message__title {
        color: var(--support-colours-yellow);
        width: 230px;
      }

      .auth-message__content {
        color: var(--typo-colours-support-blue-dark);
        width: 250px;
      }
    }

    &.auth-success {
      background: var(--support-colours-green-light-1);

      .auth-message__title {
        color: var(--typo-colours-support-green);
      }

      .auth-message__content {
        color: var(--typo-colours-support-blue-dark);
        padding: 0 16px;
      }
    }

    .auth-message__title {
      font-size: 22px;
      font-weight: 600;
      line-height: 30px;
      text-align: center;
    }

    .auth-message__content {
      text-align: center;
      font-size: 14px;
      line-height: 20px;
    }
  }

  .auth-content-back-drop {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 226px;
    height: 511px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .select-user-type-back-drop-item {
      width: 226px;
      height: 226px;
      border-radius: 50%;
      z-index: -1;
      filter: blur(170px);

      &.item-1 {
        background-color: #DDA82A;
        opacity: 80%;
      }

      &.item-2 {
        background-color: #4461F2;
      }
    }
  }

  .auth-select-language {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 32px;
  }

  // @media screen and (min-width: 1536px) {
  //   .auth-banner {
  //     grid-column: span 9;

  //     &__content {
  //       max-width: calc(100% - 150px);
  //     }
  //   }

  //   .auth-container {
  //     grid-column: 11/14;
  //   }
  // }

  // @media screen and (min-width: 1024px) and (max-width: 1535.98px) {
  //   .auth-banner {
  //     grid-column: span 8;
  //   }

  //   .auth-container {
  //     grid-column: 10/14;
  //   }
  // }

  // @media screen and (min-width: 768px) and (max-width: 1023.98px) {
  //   .auth-banner {
  //     grid-column: span 7;
  //   }

  //   .auth-container {
  //     grid-column: 9/14;
  //   }
  // }

  // @media screen and (max-width: 767.98px) {
  //   .auth-banner {
  //     grid-column: span 7;
  //   }

  //   .auth-container {
  //     grid-column: span 7;
  //   }
  // }
}