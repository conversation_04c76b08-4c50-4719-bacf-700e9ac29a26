import { Outlet, Route, Routes, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch } from 'react-redux';
import { useEffect, useMemo, useRef, useState } from "react";

import Login from "./components/Login";
import ForgotPassword from "./components/ForgotPassword";
import GoogleSection from "@app/auth/components/LoginGoogle/GoogleSection";
import Register from "@app/auth/components/Register";

import MAP from "@src/asset/banner/auth-banner-map.svg";
import CONTENT from "@src/asset/banner/auth-banner-content.svg";

import "./AuthPage.scss";
import { Dropdown } from "antd";
import { CONSTANT, LANGUAGE } from "@constant";
import VN_FLAG from "@src/asset/icon/flag/vn.svg";
import UK_FLAG from "@src/asset/icon/flag/uk.svg";

import * as app from "@src/ducks/app.duck";
import SelectLanguage from "../component/SelectLanguage";
import ModalResendEmail from "./components/ModalResendEmail";

export const AuthLayout = () => {
  const { i18n, t } = useTranslation();
  const dispatch = useDispatch();
  const location = useLocation();
  const authMessageRef = useRef();

  const [isShowmodalResend, setShowmodalResend] = useState(false);
  const [countSubmit, setCountSubmit] = useState(0);
  const [showRequest, setShowRequest] = useState(false);
  const [authMessageProp, setAuthMessageProp] = useState({
    authTitle: "",
    authMessage: "",
    authStatus: "",
    email: ""
  });

  useEffect(() => {
    setAuthMessageProp({
      authTitle: "",
      authMessage: "",
      authStatus: "",
      email: ""
    })
    setShowRequest(false);
  }, [location])

  useEffect(() => {
    if (countSubmit) {
      scrollToAuthMessage();
    }
  }, [countSubmit]);

  const handleChangeLang = lang => {
    if (i18n.language !== lang) {
      dispatch(app.actions.setLanguage(lang));
      i18n.changeLanguage(lang);
    }
  };

  const scrollToAuthMessage = () => {
    const messageElement = authMessageRef?.current;
    if (!messageElement) return;
    const rect = messageElement?.getBoundingClientRect();

    // Kiểm tra nếu div nằm ngoài khung nhìn (viewport)
    if (rect.top < 0 || rect.bottom > (window.innerHeight || document.documentElement.clientHeight)) {
      messageElement.scrollIntoView({ behavior: "smooth" });
    }
  };

  const authMessageClassname = useMemo(() => {
    if (authMessageProp.authStatus === CONSTANT.SUCCESS) {
      return "auth-success";
    } else if (authMessageProp.authStatus === CONSTANT.WARNING) {
      return "auth-warning";
    }
    return "auth-error";
  }, [authMessageProp.authStatus]);

  const onToggleModalResend = () => {
    setShowmodalResend(pre => !pre);
  }

  return (
    <div id="auth">
      <div className="auth-container">
        <div className="auth-content">
          <Outlet context={{ setAuthMessageProp, setCountSubmit, setShowRequest }} />
        </div>
        {authMessageProp.authMessage && <div ref={authMessageRef} className={`auth-message ${authMessageClassname}`}>
          {authMessageProp?.authTitle && <div className="auth-message__title">
            {t(authMessageProp.authTitle)}
          </div>}
          <div className="auth-message__content">
            {authMessageProp?.authStatus === CONSTANT.SUCCESS
              ? t(authMessageProp.authMessage).format(authMessageProp.email)
              : t(authMessageProp?.authMessage)}

            {showRequest && <div className="auth-message__content__resend">{t("RESEND_ACTIVATION_EMAIL")}?
              <span className="auth-message__content__request" onClick={onToggleModalResend}>{t("REQUEST")}</span>
            </div>}
          </div>
        </div>}
      </div>
      <div className="auth-content-back-drop">
        <div className="select-user-type-back-drop-item item-1" />
        <div className="select-user-type-back-drop-item item-2" />
      </div>
      <div className="auth-select-language">
        <SelectLanguage />
      </div>
      <ModalResendEmail open={isShowmodalResend} onCancel={onToggleModalResend} />
    </div>
  );
};


const AuthPage = () => (
  <Routes>
    <Route element={<AuthLayout />}>
      <Route path="login" element={<Login />} />
      <Route path="forgot-password" element={<ForgotPassword />} />
      <Route path="sign-up" element={<Register />} />
      <Route index element={<Login />} />
    </Route>
  </Routes>
);

export default AuthPage;

