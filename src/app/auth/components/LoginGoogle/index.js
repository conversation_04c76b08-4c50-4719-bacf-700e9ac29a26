import { useEffect, useMemo, useState } from "react";

import Loading from "@component/Loading";
import { oauthGoogle } from "@services/Auth";
import queryString from "query-string";
import { useLocation } from "react-router-dom";
import { connect } from "react-redux";
import * as auth from "@src/ducks/auth.duck";
import { CONSTANT } from "@constant";


function LoginGoogle({ ...props }) {
  const location = useLocation();
  
  useEffect(() => {
    handleLoginGoogle();
  }, []);
  
  async function handleLoginGoogle() {
    const queryObj = queryString.parse(location.search);
    const apiResponse = await oauthGoogle({ code: queryObj.code });
    localStorage.setItem(CONSTANT.LOGIN_GOOGLE, JSON.stringify(apiResponse));
    window.close();
  }
  
  return <Loading active />;
}

function mapStateToProps(store) {
  return {};
}

export default connect(mapStateToProps, auth.actions)(LoginGoogle);