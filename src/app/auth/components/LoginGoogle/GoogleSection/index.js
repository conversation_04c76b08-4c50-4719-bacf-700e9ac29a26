import { useEffect } from "react";
import { Divider } from "antd";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import { toast } from "@component/ToastProvider";

import AntButton from "@component/AntButton";

import { CONSTANT } from "@constant";

import GOOGLE_ICON from "@src/asset/icon/google.svg";

import * as auth from "@src/ducks/auth.duck";

import "./GoogleSection.scss";
import { calGoogleWindowPosition } from "@common/functionCommons";

function GoogleSection(props) {
  const { t } = useTranslation();
  const { setAuthMessageProp, setCountSubmit } = props;

  const handleLoginGoogle = () => {
    const redirectUri = `${window.location.origin}/auth/login-google`;

    const { width, height, top, left } = calGoogleWindowPosition(600, 600);
    window.open(
      "https://accounts.google.com/o/oauth2/v2/auth" +
      "?response_type=code" +
      `&client_id=${process.env.GOOGLE_LOGIN_CLIENT_ID}` +
      `&redirect_uri=${redirectUri}` +
      "&access_type=offline" +
      //"&prompt=consent" +
      "&scope=email profile",
      "google-login",
      `
      scrollbars=yes,
      width=${width},
      height=${height},
      top=${top},
      left=${left}
      `,
    );
  };

  useEffect(() => {
    window.addEventListener("storage", (event) => {
      if (event.storageArea === localStorage && event.key === CONSTANT.LOGIN_GOOGLE) {

        const loginResponse = JSON.parse(localStorage.getItem(CONSTANT.LOGIN_GOOGLE));
        if (loginResponse?.hasOwnProperty("success") && loginResponse.success) {
          props.requestUser();
        } else {
          setAuthMessageProp({
            authTitle: loginResponse?.code === 202 ? "AUTHENTICATION_WARING" : "AUTHENTICATION_FAILED",
            authStatus: loginResponse?.code === 202 ? CONSTANT.WARNING : CONSTANT.ERROR,
            authMessage: loginResponse?.message || t("AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR"),
          });
          setCountSubmit(pre => pre + 1);
        }
        localStorage.removeItem(CONSTANT.LOGIN_GOOGLE);
      }
    }, false);
  }, []);

  return <div className="google-section">
    <AntButton
      block
      size="large"
      className="google-section__button"
      icon={<img src={GOOGLE_ICON} alt="" />}
      onClick={handleLoginGoogle}
    >
      {t("LOGIN_WITH_GOOGLE")}
    </AntButton>
    <Divider plain style={{ margin: "24px 0 0 0" }}>{t("OR_CONTINUE_WITH")}</Divider>
  </div>;
}

function mapStateToProps(store) {
  return {};
}

export default connect(mapStateToProps, auth.actions)(GoogleSection);