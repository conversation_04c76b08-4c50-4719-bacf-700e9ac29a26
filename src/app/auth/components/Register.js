import { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { Checkbox, Form, Input } from "antd";
import { trim } from "lodash";
import { useOutletContext, useNavigate } from 'react-router-dom';

import AntButton from "@component/AntButton";

import { LINK } from "@link";
import { BUTTON, CONSTANT } from "@constant";
import RULE from "@rule";

import { register } from "@services/Auth";

import * as auth from "@src/ducks/auth.duck";
import { AntForm } from "@src/app/component/AntForm";


function Register() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { setAuthMessageProp, setCountSubmit } = useOutletContext();

  const [form] = Form.useForm();
  // const values = Form.useWatch([], form);

  const [validData, setValidData] = useState(false);
  // const [isAgree, setAgree] = useState(false);

  const handleSubmit = async (data) => {
    const dataRequest = {
      ...data,
      fullName: trim(data.fullName),
      password: trim(data.password),
      phone: trim(data.phone),
    };
    const apiResponse = await register(dataRequest);
    if (apiResponse?.code === 200) {
      if (apiResponse?.data?._id) {
        setAuthMessageProp({
          authStatus: CONSTANT.SUCCESS,
          authTitle: "AUTH_TITLE_SIGN_UP_SUCCESS",
          authMessage: "AUTH_MESSAGE_REGISTER_SUCCESS",
        });
        // setTimeout(() => {
        //   navigate(LINK.LOGIN);
        // }, 3000);
      } else {
        setAuthMessageProp({
          authStatus: CONSTANT.WARNING,
          authTitle: "AUTH_TITLE_SIGN_UP_WAITING_LIST",
          authMessage: "AUTH_MESSAGE_SIGN_UP_WAITING_LIST",
        });
      }
    } else {
      setAuthMessageProp({
        authStatus: CONSTANT.ERROR,
        authTitle: "AUTHENTICATION_SIGNUP_FAILED",
        authMessage: apiResponse?.message || t("AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR"),
      });
    }
    setCountSubmit(pre => pre + 1);
  };

  const handleFocus = (fieldName) => {
    form.setFields([{
      name: fieldName,
      errors: [],
    }]);
  }

  // const onChangeCheckBox = (e) => {
  //   setAgree(e.target.checked);
  // };

  // const submitAble = validData && isAgree;

  return (
    <>
      <div className="auth-content__title">{t("SIGN_UP")}</div>

      <AntForm
        className="auth-content__form"
        form={form}
        requiredMark={false}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{ email: "", password: "" }}
      >
        <AntForm.Item
          name="email"
          rules={[RULE.REQUIRED, RULE.EMAIL]}
          validateTrigger="onBlur"
          validateFirst
        >
          <Input size="large" placeholder={t("EMAIL")} onFocus={() => handleFocus("email")} />
        </AntForm.Item>

        <AntForm.Item
          name="fullName"
          rules={[RULE.REQUIRED]}
          validateTrigger="onBlur"
        >
          <Input size="large" placeholder={t("USER_NAME")} onFocus={() => handleFocus("fullName")} />
        </AntForm.Item>

        <AntForm.Item
          name="phone"
          rules={[RULE.REQUIRED, RULE.PHONE]}
          validateTrigger="onBlur"
          validateFirst
        >
          <Input size="large" placeholder={t("PHONE_NUMBER")} onFocus={() => handleFocus("phone")} />
        </AntForm.Item>

        <AntForm.Item
          name="password"
          validateTrigger="onBlur"
          rules={[() => ({
            validator(_, value) {
              if (!value || !value?.trim()) {
                return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
              } else if (value.trim().length >= 6) {
                return Promise.resolve();
              } else return Promise.reject(new Error(t("MINIMUM_6_CHARACTERS_PASSWORD")));
            },
          }),]}
        >
          <Input.Password size="large" placeholder={t("CREATE_PASSWORD")} onFocus={() => handleFocus("password")} />
        </AntForm.Item>

        <AntForm.Item
          name="confirm"
          dependencies={["password"]}
          validateTrigger="onBlur"
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value) {
                  return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                }
                if (getFieldValue("password") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error(t("VALIDATE_MESSAGE_CONFIRM_PASSWORD")));
              },
            }),
          ]}
        >
          <Input.Password size="large" placeholder={t("RE_ENTER_PASSWORD")} onFocus={() => handleFocus("confirm")} />
        </AntForm.Item>
      </AntForm>


      {/* <div className="auth-form__link">
          <Checkbox className="auth-form__checkbox" onChange={onChangeCheckBox} />
          {t("I_AGREE_WITH")}?
          <Link to="#" className="auth-form__link-redirect">{t("TERM_AND_CONDITIONS")}</Link>
        </div> */}

      <AntButton
        block
        size="large"
        className="auth-content__submit"
        type={BUTTON.DEEP_NAVY}
        onClick={form.submit}
      // disabled={!submitAble}
      >
        {t("SIGN_UP")}
      </AntButton>

      <div className="auth-content__question">
        <span>{t("HAVE_AN_ACCOUNT")}</span>
        <Link to={LINK.LOGIN} className="auth-content__question__redirect">{t("LOGIN_HERE")}</Link>
      </div>
    </>);
}

function mapStateToProps(store) {
  return {};
}

export default (connect(mapStateToProps, auth.actions)(Register));
