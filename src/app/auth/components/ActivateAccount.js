import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import queryString from "query-string";
import { connect } from "react-redux";

import { toast } from "@component/ToastProvider";

import { activateAccount } from "@src/app/services/User";

import { LINK } from "@link";

import * as authRedux from "@src/ducks/auth.duck";

function ActivateAccount({ user, ...props }) {
  const { t } = useTranslation();
  const location = useLocation();
  const { activationToken } = queryString.parseUrl(location.search)?.query;
  const navigate = useNavigate();

  useEffect(() => {
    onActivate();
  }, []);

  async function onActivate() {
    const apiResponse = await activateAccount(activationToken);
    if (apiResponse?.code === 200) {
      if (user) props.logout();
      toast.success(t("ACTIVATE_ACCOUNT_SUCCESS"));
      navigate(LINK.HOMEPAGE);
    } else if (apiResponse?.code === 400) {
      toast.error(t("INVITATION_EXPIRED"));
      navigate(LINK.LOGIN);
    } else {
      toast.error(t("AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR"));
      navigate(LINK.LOGIN);
    }

  }
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = { ...authRedux.actions };

export default (connect(mapStateToProps, mapDispatchToProps)(ActivateAccount));

