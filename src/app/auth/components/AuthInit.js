import { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useLocation } from "react-router-dom";

import SplashScreen from "@component/SplashScreen";

import { CONSTANT } from "@constant";

import * as auth from "@src/ducks/auth.duck";

const AuthInit = ({ user, ...props }) => {
  const location = useLocation();
  
  const [showSplashScreen, setShowSplashScreen] = useState(true);
  
  useEffect(() => {
    props.requestUser();
  }, []);
  
  
  useEffect(() => {
    if (user !== CONSTANT.INITIAL) {
      setShowSplashScreen(false);
    }
  }, [user, location]);
  
  if (showSplashScreen) return <SplashScreen />;
  return props.children;
};

const mapStateToProps = (state) => ({ user: state.auth.user });
const connector = connect(mapStateToProps, { ...auth.actions });

export default connector(AuthInit);
