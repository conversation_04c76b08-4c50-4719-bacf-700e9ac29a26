import { useTranslation } from "react-i18next";
import { Form, Input } from "antd";
import { Link, useNavigate, useLocation, useOutletContext } from "react-router-dom";
import queryString from "query-string";
import { connect } from "react-redux";

import { AntForm } from "@src/app/component/AntForm";
import AntButton from "@component/AntButton";

import { BUTTON, CONSTANT } from "@constant";
import RULE from "@rule";

import { confirmRegister } from "@src/app/services/User";
import * as auth from "@src/ducks/auth.duck";

function ConfirmRegister({ user, ...props }) {
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const location = useLocation();
  const { email, organizationId } = queryString.parseUrl(location.search)?.query;
  const navigate = useNavigate();

  const { setAuthMessageProp, setCountSubmit } = useOutletContext();

  async function onConfirm(values) {
    const dataRequest = {
      email,
      organizationId,
      fullName: values.fullName,
      password: values.password.trim(),
    }
    const apiResponse = await confirmRegister(dataRequest);
    if (apiResponse?.code === 200 && apiResponse?.data) {
      if (user?.email) props.logout();
      setAuthMessageProp({
        authStatus: CONSTANT.SUCCESS,
        authTitle: t("AUTH_TITLE_CONFIRM_REGISTER_SUCCESS"),
        authMessage: t("AUTH_MESSAGE_GET_BACK_TO_LOGIN"),
      });
      setTimeout(() => {
        navigate("/auth");
      }, 3000);
    } else {
      setAuthMessageProp({
        authStatus: CONSTANT.ERROR,
        authTitle: "AUTHENTICATION_SIGNUP_FAILED",
        authMessage: apiResponse?.message || t("AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR"),
      });
    }
    setCountSubmit(pre => pre + 1);
  }

  const handleFocus = (fieldName) => {
    form.setFields([{
      name: fieldName,
      errors: [],
    }]);
  }


  return (<>

    <div className="auth-form">
      <div className="auth-form__title">{t("CONFIRM_REGISTER")}</div>
      <AntForm
        form={form}
        layout="vertical"
        onFinish={onConfirm}
        autoComplete="off"
      >
        <AntForm.Item
          label={t("YOUR_NAME")}
          name="fullName"
          rules={[RULE.REQUIRED]}
          validateTrigger="onBlur"
        >
          <Input size="large" placeholder={t("ENTER_YOUR_NAME")} onFocus={() => handleFocus("fullName")}/>
        </AntForm.Item>

        <AntForm.Item
          label={t("PASSWORD")} name="password"
          validateTrigger="onBlur"
          rules={[() => ({
            validator(_, value) {
              if (!value || !value?.trim()) {
                return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
              }
              if (value.trim().length >= 6) {
                return Promise.resolve();
              }
              return Promise.reject(new Error(t("MINIMUM_6_CHARACTERS_PASSWORD")));
            },
          }),]}
        >
          <Input.Password size="large" placeholder={t("ENTER_YOUR_PASSWORD")} onFocus={() => handleFocus("password")} />
        </AntForm.Item>

        <AntForm.Item
          name="confirm"
          label={t("CONFIRM_PASSWORD")}
          dependencies={["password"]}
          validateTrigger="onBlur"
          rules={[
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (!value) {
                return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
              }
              if (getFieldValue("password") === value) {
                return Promise.resolve();
              }
              return Promise.reject(new Error(t("VALIDATE_MESSAGE_CONFIRM_PASSWORD")));
            },
          }),
          ]}
        >
          <Input.Password size="large" placeholder={t("CONFIRM_YOUR_PASSWORD")} onFocus={() => handleFocus("confirm")}/>
        </AntForm.Item>
      </AntForm>

      <AntButton
        size="large"
        className="auth-submit"
        type={BUTTON.DEEP_NAVY}
        onClick={form.submit}
      >
        {t("CONFIRM")}
      </AntButton>
    </div>

    <div className="auth-question">
      {/* <span className="auth-question__icon">?</span> */}
      <span>{t("GO_TO_BACK")}</span>
      <Link to="/auth" className="auth-question__redirect">{t("LOGIN")}</Link>
    </div>
  </>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {
  ...auth.actions,
};

export default (connect(mapStateToProps, mapDispatchToProps)(ConfirmRegister));

