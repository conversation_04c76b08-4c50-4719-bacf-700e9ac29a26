import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import queryString from "query-string";
import { connect } from "react-redux";

import { toast } from "@component/ToastProvider";

import { rejectInvatation } from "@src/app/services/User";

import { LINK } from "@link";

import * as auth from "@src/ducks/auth.duck";

function RejectInvitation({ user, ...props }) {
  const { t } = useTranslation();
  const location = useLocation();
  const { organizationId, accessToken } = queryString.parseUrl(location.search)?.query;
  const navigate = useNavigate();

  useEffect(() => {
    onConfirm();
  }, []);

  async function onConfirm() {
    const apiResponse = await rejectInvatation(accessToken, { organizationId });

    let toastMessage = "";
    const { email: userEmail } = user || {};
    const { email: apiEmail, message, code } = apiResponse || {};
    const isSuccess = code === 200;

    if (userEmail === apiEmail) {
      toastMessage = message;
    } else {
      if (userEmail) props.logout();

      toastMessage = isSuccess
        ? t("DECLINED_EMAIL_INVITATION").format(apiEmail)
        : code === 404
          ? message
          : t("INVITATION_EMAIL_EXPIRED").format(apiEmail);
    }

    isSuccess ? toast.success(toastMessage) : toast.error(toastMessage);

    navigate(LINK.HOMEPAGE);
  }

  return;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {
  ...auth.actions,
};

export default (connect(mapStateToProps, mapDispatchToProps)(RejectInvitation));

