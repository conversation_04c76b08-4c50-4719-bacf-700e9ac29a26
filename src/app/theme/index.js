import { useEffect, useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { connect } from "react-redux";
import { ConfigProvider } from "antd";
import queryString from "query-string";
import { useTranslation } from "react-i18next";

import enUS from "antd/locale/en_US";
import vi_VN from "antd/locale/vi_VN";

import { THEME_TYPE, USER_TYPE } from "@constant";

import LIGHT_THEME from "@app/theme/LIGHT_THEME";
import STUDENT_THEME from "@app/theme/STUDENT_THEME";

import * as app from "@src/ducks/app.duck";
import { setCookieTheme } from "@common/functionCommons";

function Theme({ children, theme, user, ...props }) {
  
  const location = useLocation();
  const navigate = useNavigate();
  const { i18n } = useTranslation();
  const locale = i18n.language === "vi" ? vi_VN : enUS;
  
  
  useEffect(() => {
    const queryObj = queryString.parse(location.search);
    const queryTheme = queryObj?.theme?.toUpperCase();
    if (Object.values(THEME_TYPE).includes(queryTheme)) {
      delete queryObj.theme;
      const queryStr = queryString.stringify(queryObj);
      setCookieTheme(queryTheme);
      navigate(`${location.pathname}?${queryStr}`, { replace: true });
    }
    props.getAppTheme();
    
  }, []);
  
  useEffect(() => {
    document.documentElement.setAttribute("data-theme", theme?.toLowerCase());
  }, [theme]);
  
  const inputTheme = {
    //fontSize: 16,
    //fontSizeLG: 16,
    //controlHeightLG: 48,
    paddingInline: 15,
    paddingInlineLG: 15,
    paddingInlineSM: 15,
    //paddingBlockLG: 13,
    //controlHeight: 40,
    //colorTextPlaceholder: "#858585",
    borderRadius: 4,
    borderRadiusLG: 4,
    //colorBgContainerDisabled: "#EFEFEF",
    //colorTextDisabled: "#858585",
    colorBorder: theme === THEME_TYPE.LIGHT ? "#F1F1F1" : "red",
    hoverBorderColor: "#09196B",
    activeBorderColor: "#09196B",
    colorTextPlaceholder: "#828FA2",
  };
  
  const studentTheme = {
    fontFamily: "Inter, serif",
    headerBg: "#F6F7FB",
    bodyBg: "#F6F7FB",
  };
  
  
  const { bodyBg, headerBg, siderBg } = useMemo(() => {
    if (user?.type === USER_TYPE.STUDENT)
      return STUDENT_THEME;
    else {
      return LIGHT_THEME;
    }
  }, [user]);
  
  
  return <>
    <ConfigProvider
      theme={{
        token: {
          colorText: "#000",
          colorPrimary: "#09196B",
          //borderRadius: 8,
          fontSize: 16,
          fontSizeLG: 16,
          lineHeight: 1.25,
          lineHeightLG: 1.25,
          lineHeightSM: 1.25,
          ...user?.type === USER_TYPE.STUDENT ? studentTheme : {},
        },
        
        components: {
          Button: {
            controlHeight: 36,
            controlHeightLG: 40,
            controlHeightSM: 32,
            borderRadius: 0,
            borderRadiusLG: 8,
            borderRadiusSM: 4,
            fontWeight: 400,
            fontWeightLg: 600,
            
            paddingInline: 23,
            paddingInlineLG: 23,
            paddingInlineSM: 7,
            
            defaultBg: "transparent",
            defaultBorderColor: "transparent",
            defaultColor: "transparent",
            
            colorTextDisabled: "#FFF",
            borderColorDisabled: "#D9D9D9",
            colorBgContainerDisabled: "#D9D9D9",
            
            defaultShadow: "none",
            primaryShadow: "none",
          },
          Input: inputTheme,
          InputNumber: {
            ...inputTheme,
            controlWidth: "100%",
          },
          Dropdown: {
            color: "red",
            colorBgElevated: "#FFF",
            paddingXXS: 16,
            controlPaddingHorizontal: 24,
            controlHeight: 36,
            colorText: "#172B4D",
            borderRadius: 8,
            borderRadiusSM: 0,
            
            controlItemBgHover: "#EAEDFF",
            controlItemBgActiveHover: "#031B9A",
            controlItemBgActive: "#09196B",
            
          },
          Select: {
            paddingSM: 16,
            paddingInline: 15,
            paddingInlineLG: 15,
            paddingInlineSM: 15,
            borderRadius: 4,
            borderRadiusLG: 4,
            colorBorder: theme === THEME_TYPE.LIGHT ? "#F1F1F1" : "red",
            colorTextPlaceholder: "#828FA2",
          },
          Layout: {
            bodyBg,
            headerBg,
            siderBg,
            //footerBg: theme === THEME_TYPE.LIGHT ? "#FFF" : "#000",
            //headerPadding: "16px 24px",
            headerPadding: 0,
            headerHeight: 72,
            paddingContentHorizontal: 300,
            paddingContentVertical: 300,
            //paddingContentHorizontal
            //paddingContentHorizontalLG
            //paddingContentHorizontalSM
            //paddingContentVertical
            //paddingContentVerticalLG
            //paddingContentVerticalSM
            
          },
          Menu: {
            activeBarHeight: 0,
            itemPaddingInline: 40,
            itemMarginBlock: 0,
            itemMarginInline: 0,
            controlHeight: 35,
            controlPaddingHorizontal: 28,
            borderRadiusLG: 16,
            subMenuItemBorderRadius: 0,
            padding: 32,
            itemHeight: 53,
            
            colorBgElevated: "#09196B",       // sub menu bg color
            controlItemBgActive: "#BABAFF",   // sub menu bg active color
            colorBgTextHover: "#BABAFF",      // sub menu bg hover color
            
            
            horizontalItemSelectedColor: "#09196B",                // sub menu color
            itemColor: "#FFF",                // sub menu color
            itemSelectedColor: "#FFF",                // sub menu color
            itemHoverColor: "#FFF",           // sub menu hover color
          },
          Modal: {
            paddingContentHorizontalLG: 32, // padding left right
            paddingMD: 24, // padding top bottom
            marginXS: 24, // header margin bottom
            marginSM: 24, // footer margin top
            
            controlHeight: 40,
            
            fontWeightStrong: 700,
            contentBg: theme === THEME_TYPE.LIGHT ? "#FFF" : "#1C1A1A",
            headerBg: "transparent",
            titleColor: theme === THEME_TYPE.LIGHT ? "#09196B" : "#FFF",
            borderRadiusLG: 16,
            titleLineHeight: 1.25,
            colorBgMask: "rgba(0, 0, 0, 0.8)",
          },
          Form: {
            colorTextHeading: "#000",
            verticalLabelPadding: "0 0 16px",
            itemMarginBottom: 16,
            labelFontSize: 16,
            fontSize: 16,
            colorError: "#E70C0C",
          },
          Slider: {
            railSize: 24,
            handleSize: 24,
            handleSizeHover: 24,
            handleLineWidth: 0,
            handleLineWidthHover: 0,
            dotActiveBorderColor: "transparent",
            dotBorderColor: "transparent",
            borderRadiusXS: 0,
            handleColor: theme === THEME_TYPE.LIGHT ? "#09196B" : "#4F4F4F",
            handleActiveColor: theme === THEME_TYPE.LIGHT ? "#09196B" : "#4F4F4F",
            
            trackHoverBg: "#09196B",
            trackBg: "#09196B",
            
            railBg: "#EAEDFF",
            railHoverBg: "#EAEDFF",
            
            colorPrimaryBorderHover: theme === THEME_TYPE.LIGHT ? "#09196B" : "#4F4F4F",
          },
          Notification: {},
          Radio: {},
          Table: {
            headerBg: "#FFFFFF",
            rowHoverBg: "#EAEDFF",
            borderColor: "#E8EAED",
            rowSelectedBg: "#BCC7FF",
            headerBorderRadius: 0,
            cellPaddingBlock: 16,
            cellPaddingInline: 24,
          },
          Breadcrumb: {
            itemColor: "#000000",
            linkColor: "#000000",
            separatorColor: "#000000",
            lastItemColor: "#000000",
            linkHoverColor: "#0E6DB9",
            colorBgTextHover: "#transparent",
          },
          Divider: {
            colorSplit: "#D9D9D9",
            colorText: "#828FA2",
          },
          Segmented: {
            trackBg: "#FFFFFF",
            itemColor: "#09196B",
            itemHoverColor: "#09196B",
            itemSelectedBg: "#09196B",
            itemSelectedColor: "#FFFFFF",
            motionDurationMid: "0.005s",
            motionDurationSlow: "0.2s",
            trackPadding: 0,
          },
          Progress: {
            defaultColor: "#09196B",
          },
          Popover: {
            defaultColor: "#09196B",
          },
          Image: {
            colorBgMask: "#000000B2",
          },
        },
      }}
      locale={locale}
    >
      {children}
    </ConfigProvider>
  </>;
}


function mapStateToProps(store) {
  const { theme } = store.app;
  const { user } = store.auth;
  return { theme, user };
}

export default connect(mapStateToProps, { ...app.actions })(Theme);
