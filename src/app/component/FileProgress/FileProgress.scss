.file-progress {
  display: flex;
  flex-direction: row;
  gap: 16px;


  .file-progress__file {
    display: flex;
    flex-direction: row;
    flex: 1;
    align-items: center;
    border-radius: 8px;
    padding: 0 16px;
    height: 80px;
    border: 1px solid var(--lighttheme-content-background-stroke);
    gap: 10px;

    .file-progress__file-icon {
      width: 32px;
      height: 32px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .file-progress__file-info {
      display: flex;
      flex-direction: column;
      gap: 11px;
      flex: 1;

      .file-progress__file-name {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        flex: 1;

        .file-progress__file-capacity {
          float: right;
          color: var(--typo-colours-support-blue-light);
        }
      }

      .file-progress__file-progress {
        position: relative;

        &.file-progress__file-progress-show-status {
          .ant-progress .ant-progress-text {
            display: none;
          }

          .file-progress__progress-status {
            display: block;
          }
        }

        .ant-progress {
          height: 24px;
          margin: 0;

          .ant-progress-outer {
            margin-inline-end: calc(-2em - 16px);
            padding-inline-end: calc(2em + 16px);
          }

          .ant-progress-text {
            margin-inline-start: 16px;
          }
        }

        .file-progress__progress-status {
          display: none;
          position: absolute;
          right: 8px;
          top: 0;
          bottom: 0;

          img {
            width: 24px;
            height: 24px;
          }
        }
      }
    }
  }

  .file-progress__remove-file {
    display: flex;
    align-items: center;


    .ant-btn {
      box-shadow: var(--shadow-level-2);
    }

    .ant-btn-icon svg {
      path {
        stroke: var(--typo-colours-support-blue-light)
      }
    }
  }
}