import React from "react";
import { Progress } from "antd";
import clsx from "clsx";

import AntButton from "@component/AntButton";

import { UPLOAD_STATUS } from "@constant";
import { getFileExtension, humanFileSize } from "@common/functionCommons";

import UploadError from "@component/SvgIcons/UploadStatus/UploadError";
import UploadSuccess from "@component/SvgIcons/UploadStatus/UploadSuccess";
import Close from "@component/SvgIcons/Close";

import FILE_BLUE from "@src/asset/icon/file/file-blue.svg";
import FILE_DOC from "@src/asset/icon/file/file-doc.svg";
import FILE_DOCX from "@src/asset/icon/file/file-docx.svg";
import FILE_JPG from "@src/asset/icon/file/file-jpg.svg";
import FILE_MP3 from "@src/asset/icon/file/file-mp3.svg";
import FILE_MP4 from "@src/asset/icon/file/file-mp4.svg";
import FILE_PDF from "@src/asset/icon/file/file-pdf.svg";
import FILE_PNG from "@src/asset/icon/file/file-png.svg";
import FILE_MOV from "@src/asset/icon/file/file-mov.svg";
import FILE_WAV from "@src/asset/icon/file/file-wav.svg";

import "./FileProgress.scss";


function FileProgress({ resource, onDelete }) {
  
  function renderResourceExtension(resource) {
    const extension = getFileExtension(resource?.file?.name);
    const imageMap = {
      doc: <img src={FILE_DOC} alt="file-doc" />,
      docx: <img src={FILE_DOCX} alt="file-docx" />,
      jpg: <img src={FILE_JPG} alt="file-jpg" />,
      mp3: <img src={FILE_MP3} alt="file-mp3" />,
      mp4: <img src={FILE_MP4} alt="file-mp4" />,
      pdf: <img src={FILE_PDF} alt="file-pdf" />,
      png: <img src={FILE_PNG} alt="file-png" />,
      mov: <img src={FILE_MOV} alt="file-mov" />,
      wav: <img src={FILE_WAV} alt="file-wav" />,
    };
    // Return the image component for the file extension or a default image component
    return imageMap[extension] || <img src={FILE_BLUE} alt="file" />;
  }
  
  const isShowStatus = [UPLOAD_STATUS.SUCCESS, UPLOAD_STATUS.ERROR].includes(resource.status);
  
  return <>
    <div className="file-progress">
      <div className="file-progress__file">
        <div className="file-progress__file-icon">
          {renderResourceExtension(resource)}
        </div>
        <div className="file-progress__file-info">
          <div className="file-progress__file-name">
            {resource.file.name}
            <div className="file-progress__file-capacity">
              {humanFileSize(resource.file.size)}
            </div>
          </div>
          {resource.status !== UPLOAD_STATUS.PENDING && <div
            className={clsx("file-progress__file-progress", { "file-progress__file-progress-show-status": isShowStatus })}>
            <Progress percent={resource.percent} />
            
            {isShowStatus && <div className="file-progress__progress-status">
              {resource.status === UPLOAD_STATUS.ERROR && <UploadError />}
              {resource.status === UPLOAD_STATUS.SUCCESS && <UploadSuccess />}
            </div>}
          </div>}
        </div>
      </div>
      
      <div className="file-progress__remove-file">
        <AntButton
          size="small"
          icon={<Close />}
          shape="circle"
          onClick={() => onDelete(resource)}
          disabled={resource.status === UPLOAD_STATUS.UPLOADING}
        />
      </div>
    </div>
  </>;
}

export default FileProgress;