import FeedbackContent from "../FeedbackContent";
import { useEffect, useState } from "react";

import './FeedbackModal.scss';
import AntModal from "../AntModal";
import { getGroupFeedBack } from "@src/app/services/Feedback";
import Loading from "../Loading";

const FeedbackModal = ({ toolId, projectId }) => {

  const [openFeedback, setOpenFeedback] = useState(true);
  const [isLoading, setLoading] = useState(false);
  const [feedbackData, setFeedbackData] = useState([]);

  const onToggleFeedback = () => {
    setOpenFeedback(pre => !pre);
  };

  useEffect(() => {
    getFeedBackList();
  }, []);

  const getFeedBackList = async () => {
    setLoading(true);
    const response = await getGroupFeedBack();
    if (response) {
      setFeedbackData(response);
    }
    setLoading(false);
  }

  return <AntModal
    width={448}
    className="feedback__modal"
    open={openFeedback}
    onCancel={onToggleFeedback}
    footerless
    closeIcon={null}
  >
    <Loading active={isLoading} className="feedback__loading" >
      <FeedbackContent
        onClose={onToggleFeedback}
        openFeedback={openFeedback}
        toolId={toolId}
        projectId={projectId}
        feedbackData={feedbackData} />
    </Loading>
  </AntModal>
}
export default FeedbackModal;