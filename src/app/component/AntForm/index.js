import React, { useContext, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Form } from "antd";

//import AntFormItem from "@component/AntForm/AntFormItem";
import { TagInput } from "@component/AntForm/TagInput";

import "./AntForm.scss";

export const AntFormContext = React.createContext();

function AntForm({ children, ...props }) {
  const { t } = useTranslation();
  const isFirst = useRef(true);
  
  useEffect(() => {
    if (isFirst.current) {
      isFirst.current = false;
    } else {
      props.form?.validateFields();
    }
  }, [t]);
  
  return (
    <AntFormContext.Provider value={{ form: props.form }}>
      <Form
        requiredMark={false}
        scrollToFirstError
        {...props}
      >
        {children}
      </Form>
    </AntFormContext.Provider>
  );
}

function AntFormItem({ children, ...props }) {
  const { t } = useTranslation();
  const { form } = useAntForm();
  
  const rules = props.rules?.map(rule => {
    rule.message = t(rule.lang) || rule.message;
    return rule;
  });
  
  return (
    <Form.Item {...props} rules={rules}>
      {children}
    </Form.Item>
  );
}


AntForm.Item = AntFormItem;
AntForm.TagItem = TagInput;

const useAntForm = () => useContext(AntFormContext);

export { AntForm, useAntForm };