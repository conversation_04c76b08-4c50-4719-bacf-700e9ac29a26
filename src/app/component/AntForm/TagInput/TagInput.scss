.tag-input {
  .ant-input-prefix {
    margin: 0;
    width: 100%;

    .tag-input__tags {
      display: flex;
      flex-direction: row;
      width: 100%;
      flex-wrap: wrap;
      column-gap: 8px;
      row-gap: 6px;

      .ant-tag {
        margin-inline-end: 0;
        max-width: 100%;

        .tag-input__tag-label {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          position: relative;
          max-width: calc(100% - 15px);
          float: left;
        }

        .ant-tag-close-icon {
          margin-inline-start: 3px;
          //position: absolute;
          //inset-inline-end: 0;
          //cursor: pointer;
        }
      }

      > * {
        float: left;
      }
    }

    .tag-input__input {
      border: 0;
      padding: 0;
      box-shadow: none !important;
      width: unset;
      min-width: 50px;
      flex: 1;
    }
  }

  input:not(.tag-input__input) {
    display: none;
  }

  .ant-input-suffix {
    .ant-input-show-count-suffix {
      position: absolute;
      bottom: -28px;
      inset-inline-end: 0;
      white-space: nowrap;
      pointer-events: none;
    }
  }
}