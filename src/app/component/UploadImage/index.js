import React, { useCallback, useMemo } from "react";
import { useDropzone } from "react-dropzone";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import { toast } from "@component/ToastProvider";
import Loading from "@component/Loading";
import Upload40 from "@component/SvgIcons/Upload/Upload40";
import AntButton from "@component/AntButton";

import { BUTTON } from "@constant";
import { API } from "@api";

import { getFileExtension } from "@common/functionCommons";

import "./UploadImage.scss";

UploadImage.propTypes = {
  disabled: PropTypes.bool,
  isUploading: PropTypes.bool,
};

UploadImage.defaultProps = {
  disabled: false,
  isUploading: false,
};

function UploadImage({ imagePreviewId, isUploading, acceptImg, disabled, ...props }) {
  const { t } = useTranslation();
  
  const accept = useMemo(() => {
    return acceptImg?.reduce(function (grouped, element) {
      grouped[`image/${element}`] = [`.${element}`];
      return grouped;
    }, {});
  }, [acceptImg]);
  
  
  function handleReject() {
    toast.warning({ description: t("WE_CAN_WORK_WITH_IMAGES").format(acceptImg.join(", ")) });
  }
  
  const handleDrop = useCallback(async (acceptedFiles, fileRejections) => {
    if (fileRejections.length) {
      handleReject();
    } else {
      props.onDrop(acceptedFiles);
    }
  }, []);
  
  const handlePaste = useCallback(async (event) => {
    const images = [];
    const items = (event.clipboardData || event.originalEvent.clipboardData).items;
    for (let index in items) {
      const item = items[index];
      if (item.kind === "file" && item.type.includes("image")) {
        const blob = item.getAsFile();
        const fileExtension = getFileExtension(blob.name);
        if (acceptImg?.length && !!acceptImg?.includes(fileExtension)) {
          images.push(blob);
        }
      }
    }
    
    if (items.length === images.length) {
      props.onDrop(images);
    } else {
      handleReject();
    }
  }, []);
  
  
  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop: handleDrop,
    noClick: !imagePreviewId,
    accept,
    disabled,
  });
  
  
  return <div className={clsx("upload-image-container", { "disabled-upload": disabled })}>
    
    <Loading active={isUploading}>
      <div {...getRootProps()} onPaste={handlePaste}>
        <input {...getInputProps()} />
        
        {imagePreviewId
          ? <div className="upload-image__preview">
            <img src={API.STREAM_ID.format(imagePreviewId)} alt="preview" />
          </div>
          : <div className="upload-image-content">
            <div className="upload-image-inner">
              <div className="">
                <Upload40 />
              </div>
              <div className="upload-image__title">
                {t("DROP_YOUR_FILE_HERE")}
              </div>
              <div className="upload-image__description">
                {t("WE_CAN_WORK_WITH")} JPG, PNG
              </div>
              <div className="upload-image__description">
                {t("DROPZONE_IMAGE_DESCRIPTION")}
              </div>
              
              <div className="upload-image__select-image">
                <AntButton
                  size="large"
                  type={BUTTON.DEEP_NAVY}
                  onClick={open}
                >
                  {t("SELECT_IMAGE")}
                </AntButton>
              </div>
            </div>
          </div>}
      </div>
    </Loading>
  </div>;
}

export default UploadImage;