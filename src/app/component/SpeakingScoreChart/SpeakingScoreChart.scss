.speaking-score-chart {

  //padding: 10px 20px;
  //background: var(--white);
  //border: 1px solid #000;

  display: flex;
  flex-direction: row;
  gap: 32px;

  .speaking-score__pronunciation {
    height: 250px;
    width: 250px;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .pronunciation-score__title {
      margin: 16px 0;
      font-weight: 700;
      text-align: center;
    }

    .pronunciation-score__progress {
      width: 150px;
      height: 150px;
      align-self: center;
      margin-bottom: 10px;

      .g2-html-annotation {
        font-size: 60px !important;
        font-weight: 600 !important;
        left: 50% !important;
        top: 47% !important;
        user-select: none;
      }
    }

    .pronunciation-score__scale {
      display: flex;
      flex-direction: row;
      gap: 8px;
      font-size: 14px;

      .score-scale__item {
        display: flex;
        flex-direction: row;
        gap: 8px;

        .score-scale__color {
          height: 20px;
          width: 20px;
          border-radius: 4px;
        }

        .score-scale__text {

        }
      }
    }
  }

  .speaking-score__breakdown {
    height: 250px;


    .score-breakdown__title {
      margin: 40px 0 14px 0;
      font-size: 14px;
      font-weight: 600;
    }

    .score-breakdown__content {
      display: flex;
      flex-direction: column;
      row-gap: 5px;

      .breakdown-item {
        min-width: 280px;

        .breakdown-item__info {
          display: flex;
          flex-direction: row;
          justify-content: space-between;

          .breakdown-item__title {
            font-size: 14px;
          }

          .breakdown-item__percentage-score {
            .breakdown-item__score {
              font-weight: 600;
            }
          }
        }

        .breakdown-item__chart {
          .ant-progress {
            height: 15px;
            display: flex;

            .ant-progress-outer {
              .ant-progress-inner, .ant-progress-inner .ant-progress-bg-outer {
                border-radius: 3px;
              }
            }
          }
        }
      }

    }


  }
}

