import { memo, useMemo, useState } from "react";
import { Flex, Progress, Slider } from "antd";
import { Pie } from "@ant-design/plots";

import "./SpeakingScoreChart.scss";

const SCORE_SCALE = [
  { text: "0 ~ 59", color: "rgb(168, 0, 0)" },
  { text: "60 ~ 79", color: "rgb(218, 185, 52)" },
  { text: "80 ~ 109", color: "#218d51" },
];

function SpeakingScoreChart({ overallLabel, overallScore, scoreBreakdown }) {
  
  function convertCamelCaseToNormalText(text) {
    const result = text.replace(/([A-Z])/g, " $1");
    return result.charAt(0).toUpperCase() + result.slice(1).toLowerCase();
  }
  
  function renderScoreColor(score) {
    if (score < 60) {
      return "rgb(168, 0, 0)";
    } else if (score < 80) {
      return "rgb(218, 185, 52)";
    } else {
      return "#218d51";
    }
  }
  
  const config = {
    data: [
      { type: "earned points", value: overallScore },
      { type: "missing points", value: 100 - overallScore },
    ],
    color: [renderScoreColor(overallScore), "#ccc"],
    angleField: "value",
    colorField: "type",
    innerRadius: 2 / 3,
    label: false,
    legend: false,
    
    statistic: {
      title: null, // Ẩn chữ "Total" trong trung tâm
      content: {
        style: { fontSize: "32px", color: "#000" },
        formatter: () => overallScore,
      },
    },
    tooltip: false,
    appendPadding: 1,
    pieStyle: {
      stroke: "#FFF",
      lineWidth: 2,
    },
    
  };

  if (!scoreBreakdown) return;
  return <div className="speaking-score-chart">
    <div className="speaking-score__pronunciation">
      <div className="pronunciation-score__title">
        {overallLabel}
      </div>
      <div className="pronunciation-score__progress">
        <Pie {...config} />
      </div>
      <div className="pronunciation-score__scale">
        
        {SCORE_SCALE.map((scoreScale, index) => {
          return <div key={index} className="score-scale__item">
            <div className="score-scale__color" style={{ backgroundColor: scoreScale.color }} />
            <div className="score-scale__text">
              {scoreScale.text}
            </div>
          </div>;
        })}
      
      
      </div>
    </div>
    <div className="speaking-score__breakdown">
      <div className="score-breakdown__title">
        Score breakdown
      </div>
      <div className="score-breakdown__content">
        
        {Object.entries(scoreBreakdown).map(([key, value]) => {
          return <div key={key} className="breakdown-item">
            <div className="breakdown-item__info">
              <div className="breakdown-item__title">
                {convertCamelCaseToNormalText(key)}
              </div>
              <div className="breakdown-item__percentage-score">
                <span className="breakdown-item__score">{value}</span>
                <span> / 100</span>
              </div>
            </div>
            <div className="breakdown-item__chart">
              <Progress
                percent={value}
                //strokeColor="#218d51"
                strokeColor={renderScoreColor(value)}
                trailColor="#ccc"
                size={[280, 15]}
                showInfo={false}
              />
            </div>
          </div>;
        })}
      
      
      </div>
    
    </div>
  
  </div>;
}

const areEqual = (prevProps, nextProps) => {
  return prevProps.scoreBreakdown === nextProps.scoreBreakdown;
};
export default memo(SpeakingScoreChart, areEqual);