import React, { useMemo } from "react";
import { Button } from "antd";
import PropTypes from "prop-types";
import clsx from "clsx";

import { BUTTON, CONSTANT } from "@constant";

import "./AntButton.scss";

function AntButton({
                     children,
                     type = BUTTON.WHITE,
                     className,
                     size,
                     ref,
                     icon,
                     iconLocation = CONSTANT.LEFT,
                     bordered,
                     active,
                     fullIcon = false,
                     ...props
                   }) {
  
  const buttonType = useMemo(() => {
    if (!active) return type;
    return type.replace("ghost-", "deep-")
               .replace("white-", "deep-")
               .replace("light-", "deep-");
  }, [active, type]);
  
  
  const btnClassName = clsx(
    `ant-btn-${buttonType}`,
    { [className]: !!className },
    { "ant-btn-icon-only": !children },
    { "ant-btn-compact": size === "compact" },
    { "ant-btn-xsmall": size === "xsmall" },
    { "ant-btn-mini": size === "mini" },
    { "ant-btn-tiny": size === "tiny" },
    { "ant-btn-bordered": !!bordered },
    { "ant-btn-full-icon": !!fullIcon },
  );
  
  return <Button
    className={btnClassName}
    {...["large", "small"].includes(size) ? { size } : {}}
    {...iconLocation === CONSTANT.LEFT ? { icon } : {}}
    {...ref ? { ref } : {}}
    {...props}
  >
    {children}
    {iconLocation === CONSTANT.RIGHT && <span className="ant-btn-icon">
      {icon}
    </span>}
  </Button>;
}

function AntButtonLabel({ children }) {
  return (
    <span className="ant-btn-label">
      {children}
    </span>
  );
}

function AntButtonIcon({ children }) {
  return (
    <span className="ant-btn-icon">
      {children}
    </span>
  );
}

AntButton.Label = AntButtonLabel;
AntButton.Icon = AntButtonIcon;


AntButton.propTypes = {
  type: PropTypes.string,
  iconLocation: PropTypes.string,
};

export default AntButton;