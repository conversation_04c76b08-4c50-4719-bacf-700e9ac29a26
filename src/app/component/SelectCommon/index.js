import { Select } from 'antd';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';

import './SelectCommon.scss';

const SelectCommon = ({
  options,
  value,
  onChange,
  onClick,
  disabled,
  fieldNames = { label: 'name', value: '_id' },
  className,
  placeholder,
  ...rest
}) => {
  const { t } = useTranslation();

  return (
    <Select
      options={options}
      fieldNames={fieldNames}
      className={`select-common ${className || ''}`}
      value={value}
      onChange={onChange}
      disabled={disabled}
      onClick={onClick}
      placeholder={placeholder || t('SELECT_OPTION')}
      {...rest}
    />
  );
};

SelectCommon.propTypes = {
  options: PropTypes.array.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  onClick: PropTypes.func,
  disabled: PropTypes.bool,
  fieldNames: PropTypes.object,
  className: PropTypes.string,
  placeholder: PropTypes.string
};

export default SelectCommon;
