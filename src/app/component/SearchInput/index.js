import React from "react";
import { Input } from "antd";

import AntButton from "@component/AntButton";

import Close from "@component/SvgIcons/Close";
import SEARCH_ICON from "@src/asset/icon/button/search-icon.svg";

import './SearchInput.scss'

function SearchInput({ className, onClear, ...props }) {
  
  return <Input
    className={`search-input ${className}`}
    prefix={props.value && <AntButton
      size="tiny"
      onClick={onClear}
      icon={<Close />}
    />}
    suffix={<img src={SEARCH_ICON} alt="" />}
    {...props}
  />;
}

export default SearchInput;