:root {
  --modal-color: #000000;
  --button-action-hover: #efefef;
  --button-action-border-color-hover: #fff;
  --input-border-color: #f1f1f1;
}

[data-theme="dark"] {
  --modal-color: #fff;
  --button-action-hover: rgba(56, 53, 53, 0.5);
  --button-action-border-color-hover: #4f4f4f;
}

.modal-share {
  width: 352px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  color: var(--modal-color) !important;
  font-size: 16px;
  line-height: 20px;

  .modal-share__loading {
    display: flex;
    align-items: center;
    width: 100%;
    height: 80px !important;

    .loading-spin {
      top: 50%;
    }
  }

  .modal-share__no-permission {
    color: #f50606;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.25;
  }

  .modal-share__add-email {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    padding: 10px 16px;
    flex-direction: column;
    justify-content: center;
    gap: 16px;
    border-radius: 4px;
    border: 1px solid var(--input-border-color);
    background: #fff;

    .add-email__email-item {
      display: flex;
      justify-content: space-between;
      max-width: 100%;

      .email-item__email {
        max-width: 240px;
        display: flex;
        padding: 4px 8px;
        align-items: center;
        gap: 8px;
        border-radius: 4px;
        font-size: 13px;
        line-height: 20px;
        text-align: justify;

        &.valid-email {
          background: var(--background-light-background-1);
          color: var(--typo-colours-support-blue-dark);
        }

        &.invalid-email {
          background: var(--support-colours-red-light-1);
          color: var(--support-colours-red);

          .email-item__remove_icon {
            svg {
              path {
                stroke: var(--support-colours-red);
              }
            }
          }
        }

        span {
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }

        .email-item__remove_icon {
          cursor: pointer;
          display: flex;
        }
      }
    }

    .add-email__input-wrapper {
      position: relative;

      input {
        border: none;
        box-shadow: none;
        color: #474747;
        font-size: 16px;
        font-weight: 400;
        line-height: 20px;
        padding: 0 2px;

        &::placeholder {
          color: var(--typo-colours-support-blue-light);
          font-size: 16px;
          font-weight: 400;
          line-height: 20px;
        }
      }

      .add-email__suggestion {
        position: absolute;
        top: 35px;
        width: 100%;
        padding: 10px 16px;
        margin: 0 -16px;
        box-shadow: 0px 4px 20px 0px #0000001A;
        background: var(--background-light-background-2);
        cursor: pointer;
        z-index: 1;
        border-radius: 8px;
      }
    }
  }


  .modal-share__access {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;

    .modal-share__access__action {
      background-color: unset;
      display: flex;
      align-items: center;
      padding: 0;
      height: 20px;
      border: none;

      svg {
        width: 16px;
        height: 16px;

        path {
          stroke: var(--modal-color);
        }
      }
    }
  }

  .modal-share__copy-link {
    display: flex;
    flex-direction: column;
    gap: 16px;

    div:first-child {
      font-weight: 600;
      font-size: 16px;
    }

    .copy-link-btn {
      background: #E5F6FF;
      color: #36A6FF;
      border: none;
      border-radius: 12px;
      height: auto;
      padding: 8px 16px;
      display: flex;
      align-items: center;
      gap: 10px;

      &:hover {
        background: #d4efff;
      }

      img {
        width: 24px;
        height: 24px;
      }

      svg {
        path {
          stroke: #36A6FF;
        }
      }
    }
  }

  .modal-share__access-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .ant-avatar {
      flex-shrink: 0;

      &.avatar-general-access {
        img {
          display: flex;
        }
      }
    }

    .access-item__info {
      font-weight: 400;

      &:not(.general-access) {
        max-width: 233px;
      }

      .item__info__title {
        font-size: 16px;
      }

      .access-item__info__email {
        color: var(--typo-colours-support-blue-light);
        font-size: 13px;
        padding-right: 8px;
        max-width: 100%;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      .access-item__info__desctiption {
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        color: var(--typo-colours-support-blue-light);
      }
    }

    .access-item__owner {
      font-size: 13px;
      font-weight: 400;
      padding-right: 24px;
      line-height: 17px;
    }
  }

  .modal-share__action {
    border: none;
    background: unset;
    border-radius: 4px;
    color: var(--modal-color) !important;
    text-align: justify;
    font-size: 13px;
    font-weight: 400;
    display: flex;
    padding: 4px 8px;
    align-items: center;
    gap: 8px;
    height: auto;
    box-shadow: none;

    &:hover,
    &.ant-dropdown-open {
      &:not(:disabled) {
        background: var(--primary-colours-blue-light-1);
      }
    }

    span {
      line-height: 17px;
    }

    svg {
      path {
        stroke: var(--modal-color);
      }
    }
  }
}

.action__menu {
  li {
    position: relative;
  }

  img {
    position: absolute;
    left: 28px;
    filter: invert(100%) sepia(100%) saturate(0%) hue-rotate(288deg) brightness(102%) contrast(102%);
    height: 16px;
    width: 16px;
  }

  .ant-dropdown-menu-item-divider {
    margin: 14px 32px !important;
    height: 0.5px !important;
    background-color: #fff !important;
  }
}

.modal-share__input-dropdown {
  width: 352px;
  border-radius: 4px;
  border: 1px solid var(--input-border-color);
  background: #fff;
  padding: 0;

  .rc-virtual-list-holder {
    overflow-y: auto !important;
  }

  .ant-select-item {
    display: flex;
    width: 100%;
    padding: 10px 16px;
    align-items: center;
    gap: 10px;
    border-radius: 0;

    .ant-select-item-option-content {
      color: #000;
      font-size: 16px;
      font-weight: 400;
      line-height: 20px;
    }

    svg {
      fill: #ffffff;
    }

    &.ant-select-item-option-selected {
      background-image: linear-gradient(180deg, #6ebcfb 0%, #2196f3 100%);
      flex-direction: row-reverse;

      .ant-select-item-option-content {
        color: #fff;
      }
    }

    &:not(.ant-select-item-option-selected) {
      .ant-select-item-option-content {
        padding-left: 24px;
      }
    }

    &.ant-select-item-option-active {
      background-color: #eee;
    }
  }
}
