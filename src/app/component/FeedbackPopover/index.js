import { Popover } from "antd";
import { useTranslation } from "react-i18next";
import FeedbackContent from "../FeedbackContent";
import { useEffect, useState } from "react";

import "./FeedbackPopover.scss";
import { getGroupFeedBack } from "@src/app/services/Feedback";

const FeedbackPopopver = ({ toolId, projectId }) => {
  const { t } = useTranslation();

  const [openFeedback, setOpenFeedback] = useState(false);
  const [feedbackData, setFeedbackData] = useState([]);

  useEffect(() => {
    getFeedBackList();
  }, []);

  const getFeedBackList = async () => {
    const response = await getGroupFeedBack();
    if (response) {
      setFeedbackData(response);
    }
  };

  const onToggleFeedback = () => {
    setOpenFeedback(pre => !pre);
  };

  if (!feedbackData.length) return null;
  console.log("feedbackData", feedbackData);
  console.log("openFeedback", openFeedback);

  return (<Popover
    content={<FeedbackContent
      onClose={onToggleFeedback}
      openFeedback={openFeedback}
      toolId={toolId}
      projectId={projectId}
      feedbackData={feedbackData}
    />}
    trigger="click"
    placement="top"
    arrow={false}
    align={{ offset: [-6, -8] }}
    open={openFeedback}
    onOpenChange={onToggleFeedback}
    overlayClassName="feedback__popover"
    fresh={true}
  >
    <span id="js-result-action-feedback">
      {t("HOW_RESULT")}
    </span>
  </Popover>);
};
export default FeedbackPopopver;