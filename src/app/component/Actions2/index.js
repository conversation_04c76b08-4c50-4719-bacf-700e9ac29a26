import React from "react";
import { <PERSON> } from "react-router-dom";
import { Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { EditOutlined } from "@ant-design/icons";

import AntButton from "../AntButton";
import { confirm } from "@component/ConfirmProvider";

import { BUTTON } from "@constant";

import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import "./Actions.scss";

const Actions2 = (props) => {
  const { t } = useTranslation();

  const {
    linkToEdit, handleEdit,
    editText = t("EDIT"),
    editIcon, disabledEdit,
    extraActions
  } = props;


  const {
    handleDelete,
    deleteText = t("DELETE"),
    confirmText = t("CONFIRM_DELETE"),
    deleteIcon, disabledDelete,
  } = props;

  function renderBtnEdit() {
    if (!handleEdit && !linkToEdit) return;

    if (linkToEdit) {
      return <Link to={linkToEdit}>
        <AntButton
          type={BUTTON.GHOST_WHITE}
          size="small"
          icon={editIcon || <EditOutlined />}
          disabled={disabledEdit}
        />
      </Link>;
    }

    return <AntButton
      type={BUTTON.GHOST_WHITE}
      size="small"
      icon={editIcon || <EditOutlined />}
      disabled={disabledEdit}
      onClick={handleEdit}
    />;
  }

  const onDelete = () => {
    confirm.delete({
      content: confirmText,
      handleConfirm: handleDelete,
    });
  };

  return (
    <div className="actions2">
      <Tooltip title={editText}>
        {renderBtnEdit()}
      </Tooltip>
      <Tooltip title={deleteText}>
        <AntButton
          type={BUTTON.GHOST_WHITE}
          size="small"
          icon={deleteIcon || <DeleteIcon />}
          onClick={onDelete}
          disabled={disabledDelete}
        >
        </AntButton>
      </Tooltip>
      {extraActions?.map((action, index) => <React.Fragment key={index}>{action}</React.Fragment>)}
    </div>
  );
};

export default Actions2;
