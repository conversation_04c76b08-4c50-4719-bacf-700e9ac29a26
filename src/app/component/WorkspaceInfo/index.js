import clsx from "clsx";
import { Avatar } from "antd";
import { useTranslation } from "react-i18next";

import InfoIcon from "@component/SvgIcons/Info";
import FolderIcon from "@component/SvgIcons/Folder";
import UserIcon from "@component/SvgIcons/User";
import CLOSE_ICON from "@src/asset/icon/close/close12.svg";


import { WORKSPACE_TYPE } from "@constant";

import "./WorkspaceInfo.scss";

const WorkspaceInfo = ({ ...props }) => {
  const { t } = useTranslation();
  const { workspaceDetail, onClose, isShowInfo } = props;
  const workspaceName = workspaceDetail?.userId?.fullName || workspaceDetail?.organizationId?.name || "";
  const isOrganization = workspaceDetail?.type === WORKSPACE_TYPE.ORGANIZATIONAL;
  if (!workspaceDetail) {
    return null;
  }
  return (
    <div className={clsx("workspace-info", { "workspace-info__is_organization": isOrganization })}>
      <div className="workspace-info__title">
        <Avatar
          size="large"
          shape="square"
          className={"workspace-info__avatar"}
        >
          {workspaceName[0]?.toUpperCase() || ""}
        </Avatar>
        <span className={"workspace-info__nameWorkspace"}>{t("WORKSPACE_NAME").format(workspaceName)}</span>
        {isShowInfo && <img className="workspace-info__close" onClick={onClose} src={CLOSE_ICON} />}
      </div>
      {workspaceDetail?.description && (
        <div className="workspace-info__item">
          <div className="workspace-info__item-title">
            <InfoIcon />
            {t("DESCRIPTION")}
          </div>
          <div className="workspace-info__item-info">
            {workspaceDetail?.description}
          </div>
        </div>)}
      <div className="workspace-info__item">
        <div className="workspace-info__item-title">
          <FolderIcon />
          {t("DATA")}
        </div>
        <div className="workspace-info__item-info">
          {`${Math.max(workspaceDetail?.folders, 0)} ${t("FOLDER").toLowerCase()}, ${Math.max(workspaceDetail?.projects, 0)} ${t("PROJECT").toLowerCase()}`}
        </div>
      </div>
      {isOrganization && (<div className="workspace-info__item">
        <div className="workspace-info__item-title">
          <UserIcon />
          {t("MEMBER")}
        </div>
        <div className="workspace-info__item-info">
          {`${workspaceDetail?.members || 0} ${t("MEMBER").toLowerCase()}`}
        </div>
      </div>)}
    </div>
  );
};

export default WorkspaceInfo;