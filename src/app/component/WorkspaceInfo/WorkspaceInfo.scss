.workspace-info {
  display: flex;
  padding: 24px;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  height: 100%;
  border-radius: 16px;
  color: #FFF;
  background: var(--primary-colours-blue-navy);


  .workspace-info__title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    width: 100%;

    .workspace-info__avatar {
      border: none;
      width: 40px;
      background: linear-gradient(180deg, #6EBCFB 0%, #2196F3 100%);
    }

    .workspace-info__nameWorkspace {
      width: calc(100% - 40px);
      word-break: break-word;
    }

    .workspace-info__close {
      cursor: pointer;
      inset-inline-end: 0;
      align-items: flex-start;
    }
  }

  &.workspace-info__is_organization {
    background: var(--typo-colours-support-purple) !important;

    .workspace-info__avatar {
      border: none;
      width: 40px;
      background: linear-gradient(180deg, #E76EFB 0%, #9B6EFB 100%);
      ;
    }
  }

  .workspace-info__item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .workspace-info__item-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .workspace-info__item-info {
      font-size: 14px;
    }
  }
}