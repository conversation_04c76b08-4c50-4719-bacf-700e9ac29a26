.audio-recorder-container {

  background-color: var(--white);
  border-radius: 8px;
  border-color: #424242;
  border-style: dotted;
  border-width: 1px;
  height: 150px;
  min-width: 400px;
  padding: 20px;

  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;

  position: relative;

  .audio-recorder__start-record {
    width: 48px;
    height: 48px;
    border-radius: 8px;

    .ant-btn-icon img {
      width: 48px;
      height: 48px;
    }

    .audio-recorder__waiting-record {
      position: relative;

      .waiting-record__background,
      .waiting-record__loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }

  .audio-recorder__upload-title {
    font-size: 14px;
    font-weight: 600;
  }

  .audio-recorder__upload-description {
    font-size: 12px;
  }

  .audio-recorder__select-file .audio-recorder__browse-file {
    font-size: 14px;
    color: #115EA3;
  }

  .audio-recorder__switch-type {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}