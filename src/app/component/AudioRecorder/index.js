import React, { useEffect, useRef, useState } from "react";
import { connect } from "react-redux";
import { io } from "socket.io-client";
import { Button, Progress } from "antd";
import { LoadingOutlined } from "@ant-design/icons";

import { toast } from "@component/ToastProvider";
import { useMarkSpeaking } from "@app/pages/Project/ProjectDetail/MarkSpeaking/MarkSpeakingDetail";

import { BUTTON, RECOGNIZE_STATUS, RECORDING_STATUS } from "@constant";
import { cloneObj, renderAudioDuration } from "@common/functionCommons";
import { convertPascalCaseToCamelCase } from "@common/dataConverter";

import AZURE_MIC from "@src/asset/icon/azure-mic.svg";
import AZURE_STOP from "@src/asset/icon/azure-stop.svg";
import AZURE_BG from "@src/asset/icon/azure-bg.svg";

import "./AudioRecorder.scss";
import { useTranslation } from "react-i18next";
import Upload from "@component/SvgIcons/Upload";
import AntButton from "@component/AntButton";
import VoiceViolet from "@component/SvgIcons/Voice/VoiceViolet";
import ReactDropzone from "@component/ReactDropzone";


const MAX_RECORD = 150;
const CHUNK_SIZE = 4096; // Kích thước khối xử lý

function AudioRecorder({ user }) {
  const { t } = useTranslation();
  
  const { formSpeakingRecorder, isAllowEditing } = useMarkSpeaking();
  const { instructionId, projectId } = useMarkSpeaking();
  const { setAudioFileId, setAudioUrlDownload } = useMarkSpeaking();
  const { recognizeStatus, setRecognizeStatus } = useMarkSpeaking();
  const { setResults, setTextRecognizing, setOpenAiResult, setErrors } = useMarkSpeaking();
  const { isMarkingSpeaking, setMarkingSpeaking } = useMarkSpeaking();
  
  const socket = useRef(null);
  const timeRef = useRef(null);
  
  const [recordStatus, setRecordStatus] = useState(RECORDING_STATUS.STOPPED);
  const audioSourceRef = useRef(null);
  const scriptProcessorRef = useRef(null);
  
  const [isRecord, setRecord] = useState(true);
  const [isConnected, setConnected] = useState(false);
  const [timeCount, setTimeCount] = useState(0);
  
  
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showProgress, setShowProgress] = useState(false);
  
  useEffect(() => {
    // Dọn dẹp khi component unmount
    return () => {
      if (socket.current) socket.current.disconnect();
    };
  }, []);
  
  useEffect(() => {
    if (socket.current) socket.current.disconnect();
  }, [isRecord]);
  
  useEffect(() => {
    if (isConnected) {
      if (recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING) {
        handleStreamRecord();
      } else if (recognizeStatus === RECOGNIZE_STATUS.NOT_STARTED) {
        socket.current?.disconnect();
      }
    }
  }, [isConnected, recognizeStatus]);
  
  useEffect(() => {
    if (timeCount >= MAX_RECORD) {
      stopRecording();
    }
  }, [timeCount]);
  
  async function handleStreamRecord() {
    const speakingRecorderData = formSpeakingRecorder.getFieldsValue();
    
    async function processAudioStream(socket, userId, speakingRecorderData, instructionId, projectId) {
      // 1. Lấy stream từ microphone
      const stream = await getAudioStream();
      
      if (!stream) {
        throw new Error(t("MICROPHONE_NOT_ACCESSIBLE"));
      }
      
      // 2. Tạo AudioContext
      const audioContext = new AudioContext({ sampleRate: 16000 });
      
      // 3. Kết nối stream với AudioContext
      const source = audioContext.createMediaStreamSource(stream);
      audioSourceRef.current = source;
      // 4. Tạo ScriptProcessorNode
      const bufferSize = CHUNK_SIZE; // Kích thước khối xử lý
      const scriptProcessor = audioContext.createScriptProcessor(bufferSize, 1, 1);
      scriptProcessorRef.current = scriptProcessor;
      // 5. Xử lý dữ liệu PCM trong callback
      scriptProcessor.onaudioprocess = (audioProcessingEvent) => {
        const inputBuffer = audioProcessingEvent.inputBuffer;
        const channelData = inputBuffer.getChannelData(0); // Lấy dữ liệu kênh đầu tiên (mono)
        
        // Chuyển đổi Float32 (-1.0 -> 1.0) sang Int16 (-32768 -> 32767)
        const pcmInt16Array = new Int16Array(channelData.length);
        for (let i = 0; i < channelData.length; i++) {
          pcmInt16Array[i] = Math.max(-1, Math.min(1, channelData[i])) * 0x7FFF;
        }
        
        // Tạo Uint8Array từ Int16Array để gửi qua socket
        const pcmUint8Array = new Uint8Array(pcmInt16Array.buffer);
        
        // Gửi dữ liệu qua socket
        const inputData = { ...speakingRecorderData, instructionId, projectId };
        socket.current?.emit("audio", {
          buffer: pcmUint8Array,
          inputData,
          userId: userId,
        });
      };
      
      // 6. Kết nối các nodes
      source.connect(scriptProcessor);
      scriptProcessor.connect(audioContext.destination); // Nếu không cần phát âm thanh, có thể bỏ dòng này.
      
      console.log("Audio stream is being processed and sent via socket...");
    }

// Gọi hàm với các tham số cụ thể
    try {
      await processAudioStream(socket, user._id, speakingRecorderData, instructionId, projectId);
    } catch (e) {
      console.log("e", e);
      stopRecording();
      clearData();
      setMarkingSpeaking(false);
      return toast.error(e.message);
    }
    setTimeCount(0);
    
    timeRef.current = setInterval(() => {
      setTimeCount(prevState => prevState + 1);
    }, 1000);
    
    //setMediaRecorder(recorder);
    setRecordStatus(RECORDING_STATUS.RECORDING);
  }
  
  function getAudioStream() {
    return navigator.mediaDevices.getUserMedia({ audio: true })
                    .then(stream => {
                      console.log("Đã truy cập micrô:", stream);
                      return stream; // Trả về stream
                    })
                    .catch(error => {
                      console.error("Lỗi truy cập micrô:", error);
                      return null; // Trả về null nếu có lỗi
                    });
  }
  
  
  const startRecording = async () => {
    const { errorFields } = await formSpeakingRecorder.validateFields();
    if (!!errorFields?.length) return;
    
    setMarkingSpeaking(true);
    setRecordStatus(RECORDING_STATUS.WAITING);
    setRecognizeStatus(RECOGNIZE_STATUS.RECOGNIZING);
    setResults([]);
    setOpenAiResult({ html: null, isWaiting: false });
    setErrors(prevState => {
      const newState = cloneObj(prevState);
      Object.values(newState).forEach(error => {
        error.count = 0;
      });
      return newState;
    });
    setAudioFileId(null);
    setAudioUrlDownload(null);
    socket.current = io("/teacher", { transports: ["websocket"], path: "/socket" });
    
    // Xử lý sự kiện khi nhận tin nhắn
    socket.current.on("connect", () => {
      console.log("Connected to WebSocket server");
      setConnected(true);
    });
    
    handleSocketResponse();
  };
  
  function handleSocketResponse() {
    // Xử lý sự kiện khi kết nối mở
    if (!socket.current) return;
    
    socket.current.on("message", (message) => {
      //if (message.state !== "recognizing")
      //  console.log("Message from server:", message);
      
      switch (message.state) {
        case "recognizing":
          setTextRecognizing(message.recognizing);
          break;
        case "sentence_score":
          if (!!message.results?.DisplayText) {
            setTextRecognizing(null);
          }
          const results = convertPascalCaseToCamelCase([message.results]);
          setResults(prevState => [...prevState, ...results]);
          break;
        case "overall_score":
          setResults(convertPascalCaseToCamelCase(message.results));
          setOpenAiResult(prevState => ({ ...prevState, isWaiting: true }));
          
          setRecognizeStatus(RECOGNIZE_STATUS.COMPLETE);
          break;
        case "recognized_text":
          //console.log("recognized_text", message);
          break;
        case "content_score":
          //console.log("content_score", message);
          break;
        case "audio_file_saved":
          setAudioFileId(message.fileId || "");
          setAudioUrlDownload(message.audioUrl || "");
          break;
        case "error":
          console.log("error", message);
          setOpenAiResult({ html: null, isWaiting: false });
          setRecognizeStatus(RECOGNIZE_STATUS.NOT_STARTED);
          setMarkingSpeaking(false);
          
          stopRecording();
          toast.error("AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR");
          break;
        case "openai_feedback":
          setOpenAiResult({ html: message.html, isWaiting: false });
          setMarkingSpeaking(false);
          break;
        default:
          break;
      }
    });
    
    socket.current.on("finish-recognition", () => {
      socket.current.disconnect();
    });
    
    socket.current.on("error", err => {
      console.log("Socket Error:", err.message);
      setConnected(false);
      setMarkingSpeaking(true);
    });
  }
  
  
  function clearData() {
    setErrors([]);
    setAudioFileId("");
    setAudioUrlDownload("");
    setOpenAiResult({ html: null, isWaiting: false });
    
    setResults([]);
    setTextRecognizing(null);
    setRecognizeStatus(RECOGNIZE_STATUS.NOT_STARTED);
  }
  
  const stopRecording = () => {
    clearInterval(timeRef.current);
    if (scriptProcessorRef.current) {
      scriptProcessorRef.current.onaudioprocess = null; // Hủy bỏ callback để ngừng xử lý âm thanh
      scriptProcessorRef.current.disconnect(); // Ngừng kết nối scriptProcessor
    }
    
    if (audioSourceRef.current) {
      audioSourceRef.current.disconnect();
      audioSourceRef.current.mediaStream?.getTracks()?.forEach(track => track.stop());
    }
    
    setRecordStatus(RECORDING_STATUS.STOPPED);
    
    socket.current?.emit("close-recording");
    //setConnected(false);
  };
  
  useEffect(() => {
    return () => {
      stopRecording();
      if (socket.current?.readyState === WebSocket.OPEN) {
        socket.current.close();
      }
    };
  }, []);
  
  function renderRecordIcon() {
    switch (recordStatus) {
      case RECORDING_STATUS.RECORDING:
        return <img src={AZURE_STOP} alt="" />;
      case RECORDING_STATUS.STOPPED:
        return <img src={AZURE_MIC} alt="" />;
      default:
        return <div className="audio-recorder__waiting-record">
          <img className="waiting-record__background" src={AZURE_BG} alt="" />
          <div className="waiting-record__loading">
            <LoadingOutlined />
          </div>
        </div>;
    }
  }
  
  async function onDropTemplateFile(files) {
    const { errorFields } = await formSpeakingRecorder.validateFields();
    if (!!errorFields?.length) return;
    
    const file = files?.[0];
    if (file) {
      try {
        setMarkingSpeaking(true);
        
        const arrayBuffer = await file.arrayBuffer();
        
        // Sử dụng AudioContext để giải mã dữ liệu
        const audioContext = new AudioContext({ sampleRate: 16000 });
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
        
        // Lấy dữ liệu từ audioBuffer và chuyển đổi sang buffer PCM
        const channelData = audioBuffer.getChannelData(0); // Lấy dữ liệu từ kênh 0 (mono)
        const pcmInt16Array = new Int16Array(channelData.length);
        
        // Chuyển đổi từ Float32 (-1.0 đến 1.0) sang Int16 (-32768 đến 32767)
        for (let i = 0; i < channelData.length; i++) {
          pcmInt16Array[i] = Math.max(-1, Math.min(1, channelData[i])) * 0x7FFF; // Chuyển đổi và giới hạn giá trị
        }
        
        // Tạo Uint8Array từ Int16Array để gửi qua socket
        const pcmUint8Array = new Uint8Array(pcmInt16Array.buffer);
        
        const chunks = [];
        for (let i = 0; i < pcmUint8Array.length; i += CHUNK_SIZE) {
          chunks.push(pcmUint8Array.slice(i, i + CHUNK_SIZE));
        }
        
        await sendFileAsChunks(chunks);
      } catch (e) {
        setMarkingSpeaking(false);
        console.log(333333333);
      }
      
    }
  }
  
  const sendFileAsChunks = async (chunks) => {
    setShowProgress(true);
    setUploadProgress(0);
    
    
    const speakingRecorderData = formSpeakingRecorder.getFieldsValue();
    
    socket.current = io("/teacher", { transports: ["websocket"], path: "/socket" });
    
    // Xử lý sự kiện khi nhận tin nhắn
    socket.current.on("connect", () => {
      console.log("Connected to WebSocket server");
      
      clearData();
      
      setRecognizeStatus(RECOGNIZE_STATUS.RECOGNIZING);
      
      chunks.forEach((chunk, index) => {
        const inputData = { ...speakingRecorderData, instructionId, projectId };
        
        socket.current.emit("audio", {
          buffer: chunk,
          inputData,
          userId: user._id,
        });
        
        setUploadProgress((index + 1) / chunks.length * 100);
        if (index + 1 === chunks.length) {
          socket.current?.emit("close-recording");
        }
        
      });
    });
    
    handleSocketResponse();
  };
  
  
  return (<>
      <div className="audio-recorder-container">
        {isRecord
          ? <>
            <Button
              type="text"
              className="audio-recorder__start-record"
              icon={renderRecordIcon()}
              onClick={recordStatus === RECORDING_STATUS.RECORDING ? stopRecording : startRecording}
              disabled={!isAllowEditing || (recordStatus === RECORDING_STATUS.STOPPED && isMarkingSpeaking)}
            />
            
            <div>
              {recordStatus === RECORDING_STATUS.STOPPED && t("PRESS_THE_MIC_TO_START_TALKING")}
              {recordStatus === RECORDING_STATUS.WAITING && t("PREPARING")}
              {recordStatus === RECORDING_STATUS.RECORDING && t("RECORDING")}
            </div>
            
            {recordStatus === RECORDING_STATUS.RECORDING && <div>
              {renderAudioDuration(MAX_RECORD - timeCount)}
            </div>}
          </>
          : <>
            <div className="audio-recorder__upload-title">
              {t("UPLOAD_RECORD_AUDIO")}
            </div>
            <div className="audio-recorder__upload-description">
              {t("UPLOAD_RECORD_AUDIO_GUIDE")}
            </div>
            <div className="audio-recorder__select-file">
              <ReactDropzone
                onDrop={onDropTemplateFile}
                accept={{
                  "audio/wav": [".wav"],
                }}
                disabled={isMarkingSpeaking}
              >
                <span className="audio-recorder__browse-file">
                  {t("SELECT_RECORD")}
                </span>
              </ReactDropzone>
            </div>
            
            {showProgress && <Progress percent={uploadProgress} />}
          </>}
        
        <AntButton
          size="small"
          className="audio-recorder__switch-type"
          icon={isRecord ? <Upload /> : <VoiceViolet />}
          type={BUTTON.LIGHT_NAVY}
          onClick={() => setRecord(prevState => !prevState)}
          disabled={isMarkingSpeaking}
        />
      
      </div>
    </>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(AudioRecorder);