import React, { useState } from "react";
import PropTypes from "prop-types";
import { Popover } from "antd";

import PlusIcon from "@component/SvgIcons/PlusIcon";

import "./DescriptionInput.scss";
import { useTranslation } from "react-i18next";
import { PERMISSION } from "@constant";
import RenameDescription from "../../RenameDescription";
import EditPen from "../../SvgIcons/Edit/EditPen";


function DescriptionInputFolder({ value, placeholder, modalTitle, onSubmit, permission }) {
  const { t } = useTranslation();

  const allowEdit = [PERMISSION.EDITOR, PERMISSION.OWNER].includes(permission);

  const [isShowModal, setShowModal] = useState(false);

  function renderNodataDescription() {
    return <div className="description-input-new" onClick={handleToggleModal}>
      <div className="description-input__icon">
        <PlusIcon />
      </div>
      <div className="description-input__value">
        {placeholder || t("ADD_SHORT_DESCRIPTION")}
      </div>
    </div>;
  }

  function renderDescription() {
    return (
      <div className="description-input__value">
        {allowEdit && <div className="description-input__icon-edit" onClick={handleToggleModal}>
          <EditPen />
        </div>}
        <Popover
          placement="bottomLeft"
          content={value}
          className='line-clamp-2'
          overlayStyle={{ maxWidth: '400px' }}
          trigger="hover"
          >
          {value}
        </Popover>
      </div>
    );
  }

  async function handleSubmit(description) {
    const isSuccess = await onSubmit(description);
    if (isSuccess) {
      handleToggleModal();
    }
  }

  function handleToggleModal() {
    setShowModal(!isShowModal);
  }

  if (!allowEdit && !value) {
    return null;
  }

  return <div className="description-input-folder">
    {value ? renderDescription() : renderNodataDescription()}
    <RenameDescription
      isShowModal={isShowModal}
      initialValue={value}
      title={modalTitle}
      handleClose={handleToggleModal}
      handleSubmit={handleSubmit}
      placeholder={placeholder || t("ADD_SHORT_DESCRIPTION")}
    />
  </div>;
}


DescriptionInputFolder.defaultProps = {
  modalTitle: '',
  permission: '',
  onSubmit: undefined,
  allowEdit: false,
};


DescriptionInputFolder.propTypes = {
  modalTitle: PropTypes.string,
  permission: PropTypes.string,
  onSubmit: PropTypes.func,
  allowEdit: PropTypes.bool,
};

export default DescriptionInputFolder;