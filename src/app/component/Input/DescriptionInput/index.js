import React, { useEffect, useRef, useState } from "react";
import { Input } from "antd";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";
import { Loading3QuartersOutlined } from "@ant-design/icons";

import { CONSTANT } from "@constant";

import EditIcon from "@component/SvgIcons/EditIcon";
import CheckIcon from "@component/SvgIcons/CheckIcon";
import PlusIcon from "@component/SvgIcons/PlusIcon";

import "./DescriptionInput.scss";

function DescriptionInput({
                            value, placeholder,
                            positionOfEdit = CONSTANT.RIGHT,
                            onSubmit = () => null,
                            showSubmit = true,
                          }) {
  const { t } = useTranslation();
  const inputRef = useRef(null);
  
  const [isLoading, setLoading] = useState(false);
  const [isEditing, setEditing] = useState(false);
  const [descriptionText, setDescriptionText] = useState("");
  
  useEffect(() => {
    setDescriptionText(value);
    setInputWidth(value);
  }, [value]);
  
  useEffect(() => {
    if (isEditing) {
      setInputWidth(value);
      inputRef?.current?.focus({ cursor: "end" });
    }
  }, [isEditing]);
  
  
  function onChange(e) {
    if (isLoading) return;
    setDescriptionText(e.target.value);
    setInputWidth(e.target.value);
  }
  
  function setInputWidth(input = "") {
    let width = 0, fontSize = "16px";
    const jsDescriptionInput = document.getElementsByClassName("js-description-input");
    if (jsDescriptionInput?.[0]) {
      fontSize = window.getComputedStyle(jsDescriptionInput[0]).fontSize;
    }
    
    if (input) {
      const inputValue = input?.replaceAll(" ", "\u00A0");
      const para = document.createElement("span");
      const node = document.createTextNode(inputValue);
      para.appendChild(node);
      para.style.setProperty("font-size", fontSize);
      document.body.appendChild(para);
      width = para.getBoundingClientRect().width;
      document.body.removeChild(para);
    }
    width = width < 2 ? 2 : width;
    
    if (jsDescriptionInput[0]) {
      jsDescriptionInput[0].style.width = `${width}px`;
    }
  }
  
  
  function handleClickEdit() {
    if (isLoading) return;
    if (isEditing) {
      setLoading(true);
      onSubmit(descriptionText)
        .then(() => {
          setEditing(false);
          setLoading(false);
        });
    } else {
      setEditing(true);
    }
  }
  
  function renderDes() {
    if (isEditing) {
      return renderEditDescription();
    } else {
      if (value) {
        return renderDescription();
      } else {
        return renderNodataDescription();
      }
    }
  }
  
  function renderNodataDescription() {
    return <div className="description-input-new" onClick={handleClickEdit}>
      <div className="description-input__icon">
        <PlusIcon />
      </div>
      <div className="description-input__value">
        {placeholder || t("ADD_SHORT_DESCRIPTION")}
      </div>
    </div>;
  }
  
  function handleKeyDown(e) {
    if (e.key === "Enter") {
      handleClickEdit();
    }
  }
  
  function renderEditDescription() {
    return <div className="description-edit">
      <div className="description-edit__input">
        <Input
          ref={inputRef}
          className="js-description-input"
          variant="borderless"
          onBlur={handleClickEdit}
          value={descriptionText}
          onChange={onChange}
          onKeyDown={handleKeyDown}
        />
      </div>
      <div className="description-edit__icon-submit">
        {isLoading
          ? <Loading3QuartersOutlined spin className="description-edit__icon-loading" />
          : <CheckIcon className="description-edit__icon-check" />}
      </div>
    </div>;
  }
  
  function renderBtnEdit() {
    if (!showSubmit) return null;
    return <div className="description-input__action" onClick={handleClickEdit}>
      <EditIcon className="description-edit__icon-edit" />
    </div>;
  }
  
  function renderDescription() {
    return <div className="description-input__value">
      {positionOfEdit === CONSTANT.LEFT && renderBtnEdit()}
      <div className="description-input__text">{value}</div>
      {positionOfEdit === CONSTANT.RIGHT && renderBtnEdit()}
    </div>;
  }
  
  if (!value && !showSubmit) return;
  
  return <div className="description-input">
    {renderDes()}
  </div>;
}


DescriptionInput.propTypes = {
  onSubmit: PropTypes.func,
  positionOfEdit: PropTypes.string,
  showSubmit: PropTypes.bool,
};

export default DescriptionInput;