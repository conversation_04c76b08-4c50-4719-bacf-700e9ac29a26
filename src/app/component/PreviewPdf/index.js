import React, { useEffect, useState, useCallback, memo } from "react";
import { Document, Page, pdfjs } from "react-pdf";

import Loading from "@src/app/component/Loading";

import "./PreviewPdf.scss";

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.js",
  import.meta.url,
).toString();

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

function PreviewPdf({ file }) {

  const [isLoading, setLoading] = useState(false);

  const [totalPages, setTotalPages] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);

  useEffect(() => {
    if (file) {
      setLoading(true);
    } else {
      setLoading(false);
      setTotalPages(0);
      setPageNumber(1);
    }
  }, [file]);

  const onLoadSuccess = useCallback(({ numPages }) => {
    setLoading(false);
    setTotalPages(numPages);
    setPageNumber(1);
  }, []);

  const onLoadError = useCallback(() => {
    setLoading(false);
  }, []);

  return <Loading active={isLoading} className="preview-pdf-component">
    {file && <Document
      file={file}
      onLoadSuccess={onLoadSuccess}
      onLoadError={onLoadError}
      loading=""
    >
      {/* Render only the current page instead of all pages for better performance */}
      <Page
        key={`page_${pageNumber}`}
        pageNumber={pageNumber}
        loading={null}
      />
    </Document>}
  </Loading>;
}

export default memo(PreviewPdf);