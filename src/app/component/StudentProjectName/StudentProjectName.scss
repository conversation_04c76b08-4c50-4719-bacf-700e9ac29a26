.student-project-name {
  display: block;
  position: relative;
  box-sizing: border-box;
  font-size: 22px;
  padding: 2px 0px 2px 4px;
  max-width: 100%;
  width: fit-content;
  height: 33px;
  color: var(--primary-colours-blue-navy);
  font-weight: 600;

  &.result-writing__project-name {
    max-width: min(700px, 100%);
  }

  .student-project-name__label {
    display: inline-block;
    position: relative;
    box-sizing: border-box;
    white-space: pre;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    padding: inherit;
    opacity: 0;
    min-width: 2px;
    user-select: none;
    vertical-align: top;
  }

  .student-project-name__input {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    text-overflow: ellipsis;

    font-family: inherit;
    color: inherit;
    font-size: inherit;
    line-height: inherit;
    padding: inherit;
    background: none;
    border: none;
    border-radius: 4px;
    margin-left: 1px;
    font-weight: 600;

    &:hover {
      border: 1px solid #DBDBDB;
      margin: 0;
    }

    &:focus,
    &:focus-visible {
      border: 1px solid var(--typo-colours-support-blue);
      margin: 0;
      outline: none;
    }

    &:disabled {
      border: none;
      margin-left: 1px;
    }

  }
}