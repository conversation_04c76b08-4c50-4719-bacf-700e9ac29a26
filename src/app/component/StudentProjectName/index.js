
import clsx from 'clsx';
import './StudentProjectName.scss';

const StudentProjectName = ({ isResult = false, value, disabledEdit, ...props }) => {

  return (
    <div className={clsx("student-project-name", { "result-writing__project-name": isResult })}>
      <span className="student-project-name__label">{value}</span>
      <input
        className="student-project-name__input"
        {...props}
        value={value || ''}
        disabled={disabledEdit}
      />
    </div>
  );
};

export default StudentProjectName;