import React, { useCallback } from "react";
import PropTypes from "prop-types";
import { useDropzone } from "react-dropzone";

import "./ReactDropzone.scss";

export default function ReactDropzone({ children, multiple = false, ...props }) {
  const onDrop = useCallback(props.onDrop, [props.callbackKey]);
  
  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: props.accept,
    multiple,
    disabled: props.disabled,
  });
  
  return (
    <>
      <div {...getRootProps()} className="cursor-pointer">
        <input {...getInputProps()} />
        {children}
      </div>
    </>
  );
}


ReactDropzone.propTypes = {
  onDrop: PropTypes.func,
};

ReactDropzone.defaultProps = {
  onDrop: () => null,
};
