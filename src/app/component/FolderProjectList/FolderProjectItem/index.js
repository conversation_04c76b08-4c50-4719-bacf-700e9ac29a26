import { useMemo } from "react";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

import Star from "@component/Star";
import FolderActionsDropdown from "@component/FolderActionsDropdown";
import ProjectActionsDropdown from "@component/ProjectActionsDropdown";

import { LINK } from "@link";
import { formatDate } from "@common/functionCommons";
import { toStar, unStar } from "@services/MySaved";

import { PROJECT_TYPE } from "@constant";

import FOLDER_ICON from "@src/asset/icon/folder/folder-light.svg";
import FILE_BLUE from "@src/asset/icon/file/file-blue.svg";
import FILE_GREEN from "@src/asset/icon/file/file-green.svg";
import FILE_YELLOW from "@src/asset/icon/file/file-yellow.svg";

import "./FolderProjectItem.scss";

export const getProjectIcon = (project) => {
  if (!project) return null;
  const projectType = project?.type || PROJECT_TYPE.NORMAL;
  switch (projectType) {
    case PROJECT_TYPE.NORMAL:
      return FILE_BLUE;
    case PROJECT_TYPE.EXAM_SCHOOL:
    case PROJECT_TYPE.EXAM_IELTS:
      return FILE_GREEN;
    case PROJECT_TYPE.MARK_TEST_SCHOOL:
    case PROJECT_TYPE.MARK_TEST_IELTS:
      return FILE_YELLOW;
    default:
      return FILE_BLUE;
  }
};

function FolderProjectItem({ user, dataSource, ...props }) {
  const { t } = useTranslation();
  
  const { showFolder, allowActions, showOwner } = props;
  const { handleAfterCopy, handleAfterRename, handleAfterDelete, handleAfterMove, handleAfterChangeStarred } = props;
  
  const [isFolder, linkToDetail] = useMemo(() => {
    const linkDetail = dataSource.folderName
      ? LINK.FOLDER_DETAIL.format(dataSource._id)
      : LINK.PROJECT_DETAIL.format(dataSource._id);
    return [!!dataSource.folderName, linkDetail];
  }, [dataSource]);
  
  
  const handleChangeStar = async () => {
    const apiRequest = {};
    if (isFolder) {
      apiRequest.folderId = dataSource._id;
    } else {
      apiRequest.projectId = dataSource._id;
    }
    const serviceStar = dataSource.isSaved ? unStar : toStar;
    const apiResponse = await serviceStar(apiRequest);
    if (apiResponse && handleAfterChangeStarred) {
      handleAfterChangeStarred({ ...dataSource, isSaved: !dataSource.isSaved });
    }
  };
  
  
  const creator = useMemo(() => {
    if (!dataSource) return null;
    return dataSource.ownerId?._id === user?._id
      ? t("BY_ME").toLowerCase()
      : `${t("BY").toLowerCase()} ${dataSource.ownerId?.fullName}`;
  }, [t, user, dataSource]);
  
  
  return <div className="folder-project-item-container">
    <Link to={linkToDetail} className="folder-project-item-wrapper">
      <div className="folder-project__icon">
        <img src={isFolder ? FOLDER_ICON : getProjectIcon(dataSource)} alt="" />
      </div>
      <div className="folder-project__content">
        <div className="folder-project__content-item">
          <div className="folder-project__content-name">
            {isFolder ? dataSource.folderName : dataSource.projectName}
          </div>
        </div>
        
        {!!showFolder && <div className="folder-project__content-item">
          <div className="folder-project__content-folder-name">
            {dataSource.folderId?.folderName}
          </div>
        </div>}
        
        <div className="folder-project__content-item">
          <div className="folder-project__content-creator">
            {showOwner || !isFolder
              ? `${formatDate(dataSource.createdAt)} ${creator}`
              : `${t("PROJECT")}: ${dataSource.projects}`}
          </div>
          <div className="folder-project__content-star">
            <Star active={dataSource?.isSaved} onClick={handleChangeStar} />
          </div>
        </div>
      
      </div>
    </Link>
    
    {!!allowActions && <div className="folder-project-item__action">
      {isFolder
        ? <FolderActionsDropdown
          folderData={dataSource}
          handleAfterCopy={handleAfterCopy}
          handleAfterRename={handleAfterRename}
          handleAfterDelete={handleAfterDelete}
        />
        : <ProjectActionsDropdown
          projectData={dataSource}
          handleAfterCopy={handleAfterCopy}
          handleAfterRename={handleAfterRename}
          handleAfterDelete={handleAfterDelete}
          handleAfterMove={handleAfterMove}
        />}
    
    </div>}
  </div>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(FolderProjectItem);