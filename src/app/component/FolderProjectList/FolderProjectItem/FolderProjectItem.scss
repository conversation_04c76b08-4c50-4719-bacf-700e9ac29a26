.folder-project-item-container {
  position: relative;

  .folder-project-item__action {
    position: absolute;
    top: 24px;
    right: 24px;
  }

  .folder-project-item-wrapper {
    border-radius: 16px;
    padding: 24px;
    display: flex;
    gap: 8px;
    background: var(--background-light-background-2);
    box-shadow: var(--shadow-level-3);

    .folder-project__icon {
      img {
        height: 24px;
      }
    }

    .folder-project__content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;

      .folder-project__content-item {
        display: flex;
        justify-content: space-between;
        gap: 8px;
        align-items: center;

        .folder-project__content-name {
          color: var(--typo-colours-support-blue-dark);
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          height: 24px;
          padding-right: 32px;
        }

        .folder-project__content-folder-name {
          font-size: 14px;
          color: var(--primary-colours-blue-navy-light-2);
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          height: 1lh;
          line-height: 18px;
          padding-right: 32px;
        }

        .folder-project__content-creator {
          font-size: 14px;
          color: var(--typo-colours-support-blue-light);
          line-height: 18px;
        }

        .folder-project__content-star {
          width: 24px;
          justify-content: center;
          display: flex;
          flex-shrink: 0;
        }
      }
    }


    &:hover {
      .folder-project__content-star .star-container {
        opacity: 1;
      }
    }

    &:active {
      &:not(:has(.folder-project__content-star:active)) {
        background: var(--primary-colours-blue-navy-light-2) !important;
      }
    }
  }

  &:hover,
  &:has(.ant-dropdown-open) {
    .folder-project-item-wrapper {
      background: var(--primary-colours-blue-navy-light-1);
    }
  }
}