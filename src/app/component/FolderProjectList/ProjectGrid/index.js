import { useTranslation } from "react-i18next";
import { <PERSON> } from "react-router-dom";
import clsx from "clsx";
import { Popover } from "antd";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
dayjs.extend(relativeTime);

import { LINK } from "@link";
import { formatDate } from "@common/functionCommons";
import { saved, unsaved } from "@src/app/services/MySaved";

import ProjectActionsDropdown from "@component/ProjectActionsDropdown";
import Starred from "@component/Star";

import FILE_ICON from "@src/asset/icon/file/file-blue.svg";

import './ProjectGrid.scss';

const ProjectGrid = ({ ...props }) => {
  const { projectData, user, allowActions, showOwner, showFolder } = props;
  const {handleAfterCopy, handleAfterRename, handleAfterDelete, handleAfterChangeStarred, handleAfterMove} = props;
  const { t } = useTranslation();

  const creator = projectData.ownerId?._id === user._id ? `${t("BY").toLowerCase()} ${t("ME").toLowerCase()}` : `${t("BY").toLowerCase()} ${projectData.ownerId?.fullName}`;
  const handleChangeStarred = async (project) => {
    let response;
    if (!project.isSaved) {
      response = await saved(project._id, null);
    } else {
      response = await unsaved(project._id, null);
    }
    if(response && handleAfterChangeStarred) {
      handleAfterChangeStarred({ ...project, isSaved: !project.isSaved });
    }
  }
  return (
    <div className={clsx("col-span-1 project-grid-wraper", { "has-actions": allowActions })}>
      <Link
        to={LINK.PROJECT_DETAIL.format(projectData._id)}
        className="project-grid__content"
      >
        <img src={FILE_ICON} alt='' />

        <div className="project-grid__info">
          <Popover
            placement="topLeft"
            content={projectData.projectName}
            className='project-grid__info__project-name'
            trigger="hover">
            {projectData.projectName}
          </Popover>
          {showOwner ?
            `${formatDate(projectData.createdAt)} ${creator}`
            :
            dayjs(projectData.updatedAt).fromNow()
          }
          {showFolder && projectData?.folderId?.folderName && <div className="project-item-info__folder-name">{projectData.folderId.folderName}</div>}
        </div>
      </Link >

      {allowActions && (
        <div className="project-grid__actions">
          <ProjectActionsDropdown
            projectData={projectData}
            handleAfterCopy={handleAfterCopy}
            handleAfterRename={handleAfterRename}
            handleAfterDelete={handleAfterDelete}
            handleAfterMove={handleAfterMove}
          />
        </div>
      )}

      <div className={clsx("project-grid__starred", { "starred": projectData.isSaved })}>
        <Starred active={projectData?.isSaved} onClick={() => { handleChangeStarred(projectData) }} />
      </div>

    </div >
  );
};
export default ProjectGrid;