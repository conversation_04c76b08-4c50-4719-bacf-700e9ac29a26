import { useTranslation } from "react-i18next";
import { <PERSON> } from "react-router-dom";
import clsx from "clsx";
import { Popover } from "antd";

import FolderActionsDropdown from "@component/FolderActionsDropdown";
import Starred from "@component/Star";

import { LINK } from "@link";
import { formatDate } from "@common/functionCommons";
import { saved, unsaved } from "@src/app/services/MySaved";

import FOLDER_ICON from "@src/asset/icon/folder/folder-light.svg";

import './FolderGrid.scss';

const FolderGrid = ({ ...props }) => {
  const { t } = useTranslation();
  const { folderData, user, allowActions, showOwner } = props;
  const { handleAfterCopy, handleAfterRename, handleAfterDelete, handleAfterChangeStarred } = props;

  const creator = folderData.ownerId?._id === user._id ? `${t("BY").toLowerCase()} ${t("ME").toLowerCase()}` : `${t("BY").toLowerCase()} ${folderData.ownerId?.fullName}`;
  const handleChangeStarred = async (folder) => {
    let response;
    if (!folder.isSaved) {
      response = await saved(null, folder._id);
    } else {
      response = await unsaved(null, folder._id);
    }
    if (response && handleAfterChangeStarred) {
      handleAfterChangeStarred({ ...folder, isSaved: !folder.isSaved });
    }
  }
  return (
    <div className={clsx("col-span-1 folder-grid-wraper", { "has-actions": allowActions })}>
      <Link
        to={LINK.FOLDER_DETAIL.format(folderData._id)}
        className="folder-grid__content"
      >
        <img src={FOLDER_ICON} alt='' />
        <div className="folder-grid__info">
          <Popover
            placement="topLeft"
            content={folderData.folderName}
            className='folder-grid__info__name'
            trigger="hover">
            {folderData.folderName}
          </Popover>
          {showOwner ?
            `${formatDate(folderData.createdAt)} ${creator}`
            :
            `${t("PROJECT")}: ${folderData.projects || 0}`
          }
        </div>
      </Link >

      {allowActions && (
        <div className="folder-grid__actions">
          <FolderActionsDropdown
            folderData={folderData}
            handleAfterCopy={handleAfterCopy}
            handleAfterRename={handleAfterRename}
            handleAfterDelete={handleAfterDelete}
          />
        </div>
      )}

      <div className={clsx("folder-grid__starred", { "starred": folderData.isSaved })}>
        <Starred active={folderData?.isSaved} onClick={() => { handleChangeStarred(folderData) }} />
      </div>
    </div >
  );
};
export default FolderGrid;