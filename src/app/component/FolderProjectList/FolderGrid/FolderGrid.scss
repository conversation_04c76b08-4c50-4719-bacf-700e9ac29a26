.folder-grid-wraper {
  border-radius: 16px;
  position: relative;
  box-shadow: var(--shadow-level-3);
  background: var(--background-light-background-2);

  &:hover,
  &:has(.ant-dropdown-open) {
    .folder-grid__content {
      background: var(--background-hover);
    }
  }

  &:not(:hover) {
    .folder-grid__starred {
      &:not(.starred) {
        opacity: 0;
      }
    }
  }

  &.has-actions {
    .folder-grid__content {
      padding: 24px 56px 24px 24px;
    }
  }

  .folder-grid__starred {
    position: absolute;
    bottom: 16px;
    right: 16px;
    cursor: pointer;
    display: flex;
  }

  .folder-grid__actions {
    position: absolute;
    top: 24px;
    right: 24px;
    cursor: pointer;
    display: flex;
  }

  .folder-grid__content {
    border-radius: 16px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 24px;
    height: 100%;

    &:active {
      background: var(--primary-colours-blue-light-2);
    }

    .folder-grid__info {
      display: flex;
      flex-direction: column;
      gap: 8px;
      color: #6486A1;
      font-size: 14px;
      font-weight: 400;
      height: 100%;

      .folder-grid__info__name {
        color: var(--typo-colours-support-blue-dark);
        font-size: 16px;
        word-break: break-word;
        min-height: 2lh;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden
      }
    }
  }
}