import React from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import clsx from "clsx";

import AntButton from "@component/AntButton";
import NoData from "@component/NoData";

import FolderProjectItem from "./FolderProjectItem";

import { BUTTON } from "@constant";

import ArrowUpRight from "@component/SvgIcons/ArrowUpRight";

import "./FolderProjectList.scss";

function FolderProjectList({ user, searchNoData = false, ...props }) {
  const {
    dataSource,
    allowActions,
    showOwner,
    showFolder,
    isWorkspace,
    linkShowMore,
    textNodata,
  } = props;
  const { handleAfterCopy, handleAfterRename, handleAfterDelete, handleAfterChangeStarred, handleAfterMove } = props;
  const { t } = useTranslation();
  
  if (!dataSource?.length) {
    return <NoData searchNoData={searchNoData}>{textNodata}</NoData>;
  }
  return <div className={clsx("folder-project-list", { "for-workspace": isWorkspace })}>
    {dataSource.map(item => {
        return <FolderProjectItem
          key={item._id}
          dataSource={item}
          showFolder={showFolder}
          showOwner={showOwner}
          allowActions={allowActions}
          handleAfterCopy={handleAfterCopy}
          handleAfterRename={handleAfterRename}
          handleAfterDelete={handleAfterDelete}
          handleAfterMove={handleAfterMove}
          handleAfterChangeStarred={handleAfterChangeStarred}
        />;
      },
    )}
    {linkShowMore && <div className="show-more">
      <Link to={linkShowMore}>
        <AntButton size="large" type={BUTTON.DEEP_NAVY}>
          {t("SHOW_MORE")}
          <ArrowUpRight />
        </AntButton>
      </Link>
    </div>}
  </div>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}


export default (connect(mapStateToProps)(FolderProjectList));