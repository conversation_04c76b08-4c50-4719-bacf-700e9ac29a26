.folder-project-list {
  display: grid;
  gap: 24px;

  @media screen and (min-width: 768px) {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  @media screen and (max-width: 767.98px) {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  &.for-workspace {
    @media screen and (min-width: 768px) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    @media screen and (max-width: 767.98px) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  .show-more {
    display: flex;
    height: 100%;
    justify-content: center;
    align-items: center;

    button {
      display: flex;
      gap: 8px;

      svg path {
        //stroke: var(--primary-colours-blue);
      }

      &:active {
        svg path {
          stroke: #FFF;
        }
      }
    }
  }
}