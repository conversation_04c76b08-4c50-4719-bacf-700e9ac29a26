import React, { useEffect, useState } from "react";
import { Col, Form, Row, Select, DatePicker } from "antd";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
dayjs.extend(weekday);
dayjs.extend(localeData);

import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";

import { BUTTON } from "@constant";

import { handleSearchParams } from "@src/common/functionCommons";

import "./Filter.scss";

const TimeFilter = (props) => {
  const { extraFilter, allowClear } = props;
  const [formFilter] = Form.useForm();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const [isShowSelectDate, setShowSelectDate] = useState(false);
  const [minDate, setMinDate] = useState(null);
  const [maxDate, setMaxDate] = useState(null);
  const [isSearchable, setSearchable] = useState(false);

  useEffect(() => {
    const { query } = handleSearchParams(location.search);
    handleQueryFromUrl(query);
  }, [location.search]);

  const handleQueryFromUrl = (query) => {
    const { time, fromDate, toDate } = query;

    const newQuery = {
      ...query,
      ...fromDate ? { fromDate: dayjs(fromDate * 1000) } : {},
      ...toDate ? { toDate: dayjs(toDate * 1000) } : {},
    };

    setShowSelectDate(time === "custom");
    setMinDate(newQuery?.fromDate);
    setMaxDate(newQuery?.toDate);
    formFilter.setFieldsValue(newQuery);
  };

  const onFilterSubmit = (values) => {
    const { fromDate, toDate } = values;
    const repareValues = {
      ...values,
      ...fromDate ? { fromDate: dayjs(fromDate)?.startOf("day")?.unix() } : {},
      ...toDate ? { toDate: dayjs(toDate)?.endOf("day")?.unix() } : {},
    };
    updateUrlQuery(repareValues);
  };

  const updateUrlQuery = (dataSearch = {}) => {
    let searchParams = new URLSearchParams();
    Object.entries(dataSearch).forEach(([key, value]) => {
      if (value) searchParams.append(key, value);
    });
    navigate(`?${searchParams.toString()}`, { replace: true });
    setSearchable(false);
  };

  const clearFormFilter = () => {
    setShowSelectDate(false);
    formFilter.resetFields();
    setSearchable(true);
  };

  const onFormChange = () => {
    setSearchable(true);
  };

  const hanldeChangeSelectTime = (value) => {
    if (value === "custom") {
      setShowSelectDate(true);
    } else {
      setShowSelectDate(false);
      formFilter.setFieldsValue({ fromDate: null, toDate: null });
    }
  };

  const renderExtraFilter = () => {
    if (!extraFilter?.length) return null;
    return extraFilter.map(item => (
      <Col key={item.name} xs={24} md={12} lg={6}>
        <AntForm.Item name={item.name}>
          {item.component}
        </AntForm.Item>
      </Col>
    ))
  };

  return <AntForm
    form={formFilter}
    size={"large"}
    className={"filter-form"}
    onFinish={onFilterSubmit}
    onValuesChange={onFormChange}
  >
    <Row gutter={24} className="grow">
      <Col xs={24} md={12} lg={8} xl={6}>
        <AntForm.Item name={"time"}>
          <Select placeholder={t("SELECT_TIME")} onChange={hanldeChangeSelectTime} allowClear={allowClear}>
            <Select.Option value={"week"}>{t("THIS_WEEK")}</Select.Option>
            <Select.Option value={"month"}>{t("THIS_MONTH")}</Select.Option>
            <Select.Option value={"custom"}>{t("CUSTOM")}</Select.Option>
          </Select>
        </AntForm.Item>
      </Col>
      {isShowSelectDate && <>
        <Col xs={24} md={12} lg={8} xl={6}>
          <AntForm.Item
            name={"fromDate"}
            rules={[
              () => ({
                validator(_, value) {
                  if (!!value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                },
              }),
            ]}
          >
            <DatePicker
              placeholder={t("SELECT_FROM_DATE")}
              size="large"
              className="filter-form__date-picker"
              format="DD/MM/YYYY"
              maxDate={maxDate}
              onChange={setMinDate}
            />
          </AntForm.Item>
        </Col>
        <Col xs={24} md={12} lg={8} xl={6}>
          <AntForm.Item
            name={"toDate"}
            rules={[
              () => ({
                validator(_, value) {
                  if (!!value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                },
              }),
            ]}>
            <DatePicker
              placeholder={t("SELECT_TO_DATE")}
              size="large"
              className="filter-form__date-picker"
              format="DD/MM/YYYY"
              minDate={minDate}
              onChange={setMaxDate}
            />
          </AntForm.Item>
        </Col>
      </>}
      {renderExtraFilter()}
    </Row>
    <div className={"filter-form__actions"}>
      <AntButton type={BUTTON.GHOST_WHITE} onClick={clearFormFilter}>{t("CLEAR")}</AntButton>
      <AntButton type={BUTTON.DEEP_NAVY} htmlType={"submit"} disabled={!isSearchable}>{t("SEARCH")}</AntButton>
    </div>
  </AntForm>;
};

export default TimeFilter;