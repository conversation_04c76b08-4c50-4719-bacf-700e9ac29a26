@import "src/app/styles/scroll";

.feedback-content {
  @extend .scrollbar;
  @extend .scrollbar-show;

  width: 448px;
  display: flex;
  flex-direction: column;
  min-height: min(500px, 100%, calc(100vh - 100px));
  max-height: min(100%, calc(100vh - 100px));
  overflow: auto;

  .feedback-content__header {
    display: flex;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid transparent;
    border-image: linear-gradient(90deg, #FFFFFF 0%, #E0E0E0 50.5%, #FFFFFF 100%);
    border-image-slice: 1;

    font-size: 22px;
    font-weight: 600;
    line-height: 30px;
    text-align: left;

    button {
      svg {
        width: 14px;
        height: 14px;
      }
    }
  }

  .feedback-content__body {
    display: flex;
    flex-direction: column;
    padding: 24px;
    gap: 16px;

    .feedback-content__body__feedback-item {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .feedback-item__title {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        text-align: left;
      }

      .feedback-item__content {
        display: flex;
        gap: 16px;

        &.feedback-icon {
          align-items: center;

          &.selected {
            height: 80px;
          }

          .feedback-icon__item {
            cursor: pointer;
            background-size: contain;
            background-repeat: no-repeat;
            width: 60px;
            height: 60px;
            transition: width 0.3s ease, height 0.3s ease;

            &.active {
              width: 80px;
              height: 80px;
            }
          }
        }

        &.feedback-number {
          height: 51px;

          .feedback-number__item {
            width: 60px;
            font-size: 16px;
            font-weight: 700;
            line-height: 18.75px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            box-sizing: border-box;
            cursor: pointer;

            &:not(.active) {
              border: 1px solid #DBDBDB;
            }

            &.active {
              background-color: #3A18CE;
              color: #FFFFFF;
            }
          }
        }

        &.feedback-boolean {
          .ant-radio-wrapper .ant-radio-checked .ant-radio-inner {
            background-color: #3A18CE;
            border-color: #3A18CE;

            &::after {
              transform: scale(0);
            }
          }
        }

        &.feedback-comment {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .ant-input-affix-wrapper {
            border: none;
            box-shadow: none;
          }

          textarea {
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #DBDBDB;
          }

          &.error {
            textarea {
              border-color: red;
            }

            .feedback-item__error {
              color: #ff4d4f;
            }
          }
        }
      }
    }
  }

  .feedback-content__footer {
    display: flex;
    margin-bottom: 24px;
    justify-content: center;
    margin-top: auto;
  }

}

.feedback-icon__tooltip {
  .ant-tooltip-inner {
    background-color: #2E2A2ACC;
    border-radius: 4px;
  }

  .ant-tooltip-arrow {
    &::before {
      background-color: #2E2A2ACC;
    }
  }
}

.feedback-thank-you-modal {
  .ant-modal-content {
    border-radius: 16px;
    box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.15);

    .ant-modal-close {
      top: 16px;
      right: 24px;
    }

    .ant-modal-body {
      padding: 0;
    }
  }
}

.feedback-thank-you {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  gap: 24px;

  &__image {
    width: 100%;
    display: flex;
    justify-content: center;

    img {
      max-width: 100%;
      height: auto;
    }
  }

  &__text {
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5em;
    text-align: center;
  }
}
