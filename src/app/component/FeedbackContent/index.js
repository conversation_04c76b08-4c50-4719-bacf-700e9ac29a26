import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";

import { submitFeedback } from "@src/app/services/Feedback";

import "./FeedbackContent.scss";
import AntButton from "../AntButton";
import { BUTTON, FEEDBACK_TYPE } from "@constant";
import Close from "../SvgIcons/Close";
import { Radio, Input, Tooltip, Modal } from "antd";

import POOR_ICON from "@src/asset/icon/feedback/poor.svg";
import GOOD_ICON from "@src/asset/icon/feedback/good.svg";
import EXCELLENT_ICON from "@src/asset/icon/feedback/excellent.svg";
import VERY_POOR_ICON from "@src/asset/icon/feedback/very-poor.svg";
import NORMAL_ICON from "@src/asset/icon/feedback/normal.svg";

import POOR_ACTIVE_ICON from "@src/asset/icon/feedback/poor-active.svg";
import GOOD_ACTIVE_ICON from "@src/asset/icon/feedback/good-active.svg";
import EXCELLENT_ACTIVE_ICON from "@src/asset/icon/feedback/excellent-active.svg";
import VERY_POOR_ACTIVE_ICON from "@src/asset/icon/feedback/very-poor-active.svg";
import NORMAL_ACTIVE_ICON from "@src/asset/icon/feedback/normal-active.svg";
import THANKYOU_FEEDBACK from "@src/asset/image/thankyou-feedback.svg";
import clsx from "clsx";
import { toast } from "../ToastProvider";
import { stringSplit } from "@src/common/functionCommons";

const FEEDBACK_ICONS = [
  {
    key: 1,
    icon: VERY_POOR_ICON,
    activeIcon: VERY_POOR_ACTIVE_ICON,
    titleLang: "VERY_BAD",
  },
  {
    key: 2,
    icon: POOR_ICON,
    activeIcon: POOR_ACTIVE_ICON,
    titleLang: "BAD",
  },
  {
    key: 3,
    icon: NORMAL_ICON,
    activeIcon: NORMAL_ACTIVE_ICON,
    titleLang: "AVERAGE",
  },
  {
    key: 4,
    icon: GOOD_ICON,
    activeIcon: GOOD_ACTIVE_ICON,
    titleLang: "GOOD",
  },
  {
    key: 5,
    icon: EXCELLENT_ICON,
    activeIcon: EXCELLENT_ACTIVE_ICON,
    titleLang: "EXCELLENT",
  },
];

const FeedbackContent = ({ onClose, toolId, openFeedback, feedbackData, projectId }) => {
  const { t, i18n } = useTranslation();

  const [iconFeedback, setIconFeedback] = useState(0);
  const [numberFeedback, setNumberFeedback] = useState(0);
  const [booleanFeedback, setBooleanFeedback] = useState(0);
  const [comments, setComments] = useState("");
  const [showThankYou, setShowThankYou] = useState(false);


  useEffect(() => {
    if (openFeedback) {
      setIconFeedback(0);
      setNumberFeedback(0);
      setBooleanFeedback(0);
      setComments("");
      setShowThankYou(false);
    }
  }, [openFeedback]);

  const feedbackDataDisplay = useMemo(() => {
    if (!feedbackData?.length) return [];

    return feedbackData.map(item => ({
      index: item.index,
      _id: item.feedback?._id,
      type: item.feedback?.feedbackType,
      name: item.feedback?.localization?.name[i18n.language],
    }));
  }, [feedbackData, i18n.language]);

  const isErrorComment = useMemo(() => {
    if (!comments) return false;
    return comments.length > 1000;
  }, [comments]);

  const onSubmit = async () => {
    // if (isSubmitting) return;

    const dataFeedback = [];
    // setSubmitting(true);

    feedbackDataDisplay.forEach(item => {
      let newFeedBack = { feedbackId: item._id };
      switch (item.type) {
        case FEEDBACK_TYPE.ICON:
          if (iconFeedback) newFeedBack.rating = iconFeedback;
          break;
        case FEEDBACK_TYPE.NUMBER:
          if (numberFeedback) newFeedBack.rating = numberFeedback;
          break;

        case FEEDBACK_TYPE.BOOL:
          if (booleanFeedback) newFeedBack.bool = booleanFeedback === 1 ? true : false;
          break;

        case FEEDBACK_TYPE.COMMENT:
          if (comments) newFeedBack.comment = comments;
          break;
      }
      if (Object.values(newFeedBack).length > 1) {
        dataFeedback.push(newFeedBack);
      }
    });
    const dataRequest = { dataFeedback };
    if (toolId) dataRequest.toolId = toolId;
    if (projectId) dataRequest.projectId = projectId;
    const response = await submitFeedback(dataRequest);
    if (response) {
      // Show thank you popup instead of toast
      setShowThankYou(true);
    }
    // setSubmitting(false);
  };

  const handleCountWords = (maxWords) => ({
    show: true, max: maxWords,
    strategy: (txt) => txt.length,
  });

  const renderFeedbackIcon = () => (
    <div className={clsx("feedback-item__content feedback-icon", { "selected": iconFeedback })}>
      {FEEDBACK_ICONS.map((item) => (
        <Tooltip
          key={item.key}
          title={t(item.titleLang)}
          placement="top"
          align={{ offset: [0, -7] }}
          overlayClassName="feedback-icon__tooltip">
          <div
            key={item.key}
            className={`feedback-icon__item ${item.key === iconFeedback ? "active" : ""}`}
            onClick={() => setIconFeedback(item.key)}
            style={{ backgroundImage: `url(${item.key === iconFeedback ? item.activeIcon : item.icon})` }}
          />
        </Tooltip>
      ))}
    </div>);

  const renderFeedbackNumber = () => (<div className="feedback-item__content feedback-number">
    {[1, 2, 3, 4, 5].map((item) => (
      <span
        key={item}
        className={clsx("feedback-number__item", { "active": item === numberFeedback })}
        onClick={() => setNumberFeedback(item)}>
        {item}
      </span>
    ))
    }
  </div>);

  const renderFeedbackBoolean = () => (
    <div className="feedback-item__content feedback-boolean">
      <Radio.Group onChange={e => setBooleanFeedback(e.target.value)} value={booleanFeedback}>
        <Radio value={1}>{t("YES_FEEDBACK")}</Radio>
        <Radio value={2}>{t("NO")}</Radio>
      </Radio.Group>
    </div>
  );


  const renderFeedbackComment = () => (
    <div className={clsx("feedback-item__content feedback-comment", { "error": isErrorComment })}>
      <Input.TextArea
        value={comments}
        onChange={(e) => setComments(e.target.value)}
        autoSize={{ minRows: 3, maxRows: 10 }}
        count={handleCountWords(1000)}
      />
      {isErrorComment && <span className="feedback-item__error">{t("FEEDBACK_COMMENT_ERROR")}</span>}
    </div>
  );

  const renderFeedback = (feedback) => {
    switch (feedback.type) {
      case FEEDBACK_TYPE.ICON:
        return renderFeedbackIcon();
      case FEEDBACK_TYPE.NUMBER:
        return renderFeedbackNumber();

      case FEEDBACK_TYPE.BOOL:
        return renderFeedbackBoolean();

      case FEEDBACK_TYPE.COMMENT:
        return renderFeedbackComment();

      default:
        return null;
    }
  };

  const containerElement = document.getElementById("js-speaking-feedback__body");
  const maxHeight = containerElement?.clientHeight - 50;

  const disableSubmit = !iconFeedback || !numberFeedback || !booleanFeedback || isErrorComment;

  const handleCloseThankYou = () => {
    setShowThankYou(false);
    onClose();
  };
  return (
    <>
      <div className="feedback-content" style={{ maxHeight }}>
        <div className="feedback-content__header">
          <span className="feedback-content__header-title">{t("FEEDBACK")}</span>
          <AntButton
            size="mini"
            type={BUTTON.GHOST_WHITE}
            icon={<Close/>}
            onClick={onClose}
          />
        </div>
        <div className="feedback-content__body">
          {feedbackDataDisplay?.map((item) => (
            <div className="feedback-content__body__feedback-item" key={item.index}>
              <div className="feedback-item__title">
                {`${item.index}. ${item.name}`}
              </div>
              {renderFeedback(item)}
            </div>
          ))}
        </div>
        <div className="feedback-content__footer">
          <AntButton
            type={BUTTON.DEEP_GREEN}
            size="large"
            disabled={disableSubmit}
            // loading={isSubmitting}
            onClick={onSubmit}>
            {t("SUBMIT_FEEDBACK")}
          </AntButton>
        </div>
      </div>

      <Modal
        open={showThankYou}
        footer={null}
        closable={true}
        onCancel={handleCloseThankYou}
        closeIcon={<Close />}
        width={542}
        className="feedback-thank-you-modal"
        centered
      >
        <div className="feedback-thank-you">
          <div className="feedback-thank-you__image">
            <img src={THANKYOU_FEEDBACK} alt="Thank you" />
          </div>
          <div className="feedback-thank-you__text">
            {t("THANK_YOU_FOR_YOUR_FEEDBACK")}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default FeedbackContent;
