import React from "react";

import AntButton from "@component/AntButton";

import { BUTTON } from "@constant";

import ChevronLeft from "@component/SvgIcons/ChevronLeft";
import ChevronRight from "@component/SvgIcons/ChevronRight";

import "./ChangeResult.scss";

function ChangeResult({ disabled, onChange, current, total }) {
  
  return <div className="change-result-container">
    <AntButton
      size="xsmall"
      type={BUTTON.WHITE_BLUE}
      className="change-result__action"
      icon={<ChevronLeft />}
      onClick={() => onChange(current - 1)}
      disabled={disabled || current === 1}
    />
    
    <div className="change-result__index">
      {current}/{total}
    </div>
    
    <AntButton
      size="xsmall"
      type={BUTTON.WHITE_BLUE}
      className="change-result__action"
      icon={<ChevronRight />}
      onClick={() => onChange(current + 1)}
      disabled={disabled || current === total}
    />
  </div>;
}

export default ChangeResult;