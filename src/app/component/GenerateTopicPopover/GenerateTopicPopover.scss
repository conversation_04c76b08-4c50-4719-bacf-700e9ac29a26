.ant-popover.generate-topic__popover {
  .ant-popover-content .ant-popover-inner {
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0px 4px 32px 0px #00000014;


    .generate-topic-content {
      width: 408px;
      display: flex;
      flex-direction: column;

      .ant-form-item {
        .ant-form-item-label {
          padding-bottom: 8px;

          label {
            font-weight: 500;
            font-size: 16px;
            line-height: 24px;
            letter-spacing: 0px;
          }
        }

        .ant-select {
          height: 48px;

          .ant-select-selector {
            border-radius: 16px;
            padding: 0 16px;
          }

          .ant-select-selection-search-input {
            height: 46px;
          }

          &.ant-select-focused {
            .ant-select-selector {
              box-shadow: none;
              outline: none;
            }
          }

          .anticon-down {
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;

            &::after {
              content: '';
              height: 6.5px;
              width: 6.5px;
              border-right: 1px solid var(--primary-colours-blue-navy);
              border-bottom: 1px solid var(--primary-colours-blue-navy);
              transform: rotate(45deg);
            }

            svg {
              display: none;
            }
          }

          .anticon.anticon-search {
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
          }
        }
      }

      .generate-topic-button {
        display: flex;
        width: fit-content;
        align-self: center;

        .ant-btn-icon {
          img {
            width: 24px;
            height: 24px;
          }
        }
      }
    }
  }
}

.generate-topic-btn.ant-btn.ant-btn-xsmall:not(.ant-btn-circle) {
  border-radius: 8px;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0px;
  text-align: right;
}

.generate-topic-select {
  padding: 0;
  border-radius: 8px;

  .rc-virtual-list-holder-inner {
    gap: 4px;

    .ant-select-item {
      padding: 12px 16px;
      border-radius: 0;

      .ant-select-item-option-content {
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0px;
      }

      &.ant-select-item-option-active {
        background-color: #E7E5FF;
      }

      &.ant-select-item-option-selected {
        background-color: var(--primary-colours-blue-navy);

        .ant-select-item-option-content {
          color: #FFFFFF;
        }

      }
    }
  }
}