import { Form, Popover, Select } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import TWINKLE_ICON from "@src/asset/icon/twinkle.svg";

import { BUTTON, CONSTANT } from "@constant";
import AntButton from "../AntButton";

import { generateTopic } from "@src/app/services/Content";
import { AntForm } from "../AntForm";
import "./GenerateTopicPopover.scss";

const GenerateTopicPopover = ({ tool, onTopicGenerated, showCategorySelect = true }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const { listCategory, listTag } = tool || {};

  const [open, setOpen] = useState(false);
  const [isLoading, setLoading] = useState(false);

  useEffect(() => {
    if (open) {
      form.resetFields();
    }
  }, [open]);

  const onToggleGenerateTopic = (newOpen) => {
    if (!isLoading) {
      setOpen(newOpen);
    }
  };

  const handleSubmit = async (values) => {
    setLoading(true);
    const dataRequest = { ...values, inputType: tool?.inputType };
    const response = await generateTopic(dataRequest);
    if (response) {
      onTopicGenerated(response);
      setOpen(false); // Đóng Popover sau khi API hoàn thành
    }
    setLoading(false);
  };

  if (!listTag?.length || (showCategorySelect && !listCategory?.length)) return null;

  const content = (<div className="generate-topic-content">
    <AntForm
      requiredMark={false}
      form={form}
      layout="vertical" onFinish={handleSubmit}
      className="generate-topic-form"
    >
      {showCategorySelect && (
        <AntForm.Item
          label={t("TYPE_OF_ESSAY")}
          name="category"
        >
          <Select
            size="large"
            placeholder={t("SELECT_TYPE_OF_ESSAY")}
            options={listCategory}
            fieldNames={{ label: "name", value: "name" }}
            popupClassName="generate-topic-select"
            allowClear
          >
          </Select>
        </AntForm.Item>
      )}

      <AntForm.Item label={t("TAG")} name="tag">
        <Select
          size="large"
          showSearch
          placeholder={t("SEARCH_AND_SELECT_TAG")}
          options={listTag}
          fieldNames={{ label: "name", value: "name" }}
          filterOption={(input, option) => option?.name?.toLowerCase().includes(input?.toLowerCase())}
          popupClassName="generate-topic-select"
          allowClear
        >
        </Select>
      </AntForm.Item>
    </AntForm>
    <AntButton
      size="large"
      className="generate-topic-button"
      block
      type={BUTTON.DEEP_GREEN}
      onClick={form.submit}
      icon={<img src={TWINKLE_ICON} alt="" />}
      loading={isLoading}
      iconLocation={CONSTANT.RIGHT}
    >
      {t("GENERATE_TOPIC")}
    </AntButton>
  </div>);

  return (<Popover
    content={content}
    trigger="click"
    placement="rightBottom"
    arrow={false}
    align={{ offset: [6, 0] }}
    open={open}
    onOpenChange={onToggleGenerateTopic}
    overlayClassName="generate-topic__popover"
  >
    <AntButton
      size="xsmall"
      type={BUTTON.DEEP_GREEN}
      className={"generate-topic-btn"}
    >
      {t("GENERATE_TOPIC")}
    </AntButton>
  </Popover>);
};

export default GenerateTopicPopover;