import { useState } from "react";
import { Dropdown } from "antd";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";

import AntButton from "@component/AntButton";
import Share from "@component/Share";
import RenameDescription from "@src/app/component/RenameDescription";

import { BUTTON } from "@constant";
import { copyFolder, deleteFolder, updateFolder } from "@services/Folder";

import MoreVerticalIcon from "@component/SvgIcons/MoreVertical";
import Copy from "@component/SvgIcons/Copy";
import ShareIcon from "@component/SvgIcons/ShareIcon";
import Edit from "@component/SvgIcons/Edit";
import Trash from "@component/SvgIcons/Trash";


const FolderActionsDropdown = ({ user, availableWorkspaces, size = "xsmall", ...props }) => {

  const { t } = useTranslation();
  const { folderData, className, menuClassName } = props;
  const { handleAfterCopy, handleAfterRename, handleAfterDelete, handleAfterShare } = props;
  const workspaceId = folderData?.workspaceId?._id || folderData?.workspaceId;

  const [isShowModalShare, setShowmodalShare] = useState(false);
  const [isShowModalRename, setShowmodalRename] = useState(false);

  const onCopyFolderAction = async (e) => {
    const dataRequest = { folderId: folderData._id };
    const params = { workspaceId };
    const apiResponse = await copyFolder(dataRequest, true, params);
    if (apiResponse) {
      const newWorkspaces = availableWorkspaces.map(workspace => {
        if (workspace?._id === apiResponse?.workspaceId) {
          return { ...workspace, folders: workspace.folders + 1, projects: workspace.projects + folderData.projects };
        }
        return workspace;
      });
      props.setAvailableWorkspaces(newWorkspaces);

      const newFolder = { ...apiResponse, projects: folderData?.projects };
      if (handleAfterCopy) {
        handleAfterCopy(newFolder);
      }
      const toMyWorkspace = workspaceId !== apiResponse?.workspaceId;
      toast.success(toMyWorkspace ? "COPY_FOLDER_TO_MY_WORSKSPACE_SUCCESS" : "COPY_FOLDER_SUCCESS");
    }
  };

  const onDeleteFolderAction = () => {
    confirm.delete({
      content: t("CONFIRM_DELETE_FOLDER"),
      handleConfirm: async (e) => {
        const params = { workspaceId };
        const apiResponse = await deleteFolder(folderData._id, true, params);
        if (apiResponse) {
          const newWorkspaces = availableWorkspaces.map(workspace => {
            if (workspace?._id === apiResponse?.workspaceId) {
              return {
                ...workspace,
                folders: workspace.folders - 1,
                projects: workspace.projects - folderData.projects,
              };
            }
            return workspace;
          });
          props.setAvailableWorkspaces(newWorkspaces);

          if (handleAfterDelete) {
            handleAfterDelete(apiResponse);
          }
          toast.success("DELETE_FOLDER_SUCCESS");
        }
      },
    });
  };

  const onShareFolderAction = (e) => {
    setShowmodalShare(!isShowModalShare);
  };

  const onRenameFolderAction = (e) => {
    setShowmodalRename(!isShowModalRename);
  };
  const renameFolder = async (newFolderName) => {
    const dataRequest = { _id: folderData._id, folderName: newFolderName };
    const params = { workspaceId };
    const dataResponse = await updateFolder(dataRequest, true, params);
    if (dataResponse) {
      handleAfterRename(dataResponse);
      toast.success("UPDATE_FOLDER_SUCCESS");
      onRenameFolderAction();
    }
  };

  return <><Dropdown
    className={className}
    onClick={e => {
      e.stopPropagation();
      e.preventDefault();
    }}
    menu={{
      onClick: ({ domEvent }) => {
        domEvent.preventDefault();
        domEvent.stopPropagation();
      },
      items: [
        { key: "COPY", label: t("COPY"), icon: <Copy />, onClick: onCopyFolderAction },
        { key: "SHARE", label: t("SHARE"), icon: <ShareIcon />, onClick: onShareFolderAction },
        { key: "RENAME", label: t("RENAME"), icon: <Edit />, onClick: onRenameFolderAction },
        { key: "DELETE", label: t("DELETE"), icon: <Trash />, onClick: onDeleteFolderAction },
      ],
      className: clsx("action-dropdown-menu", menuClassName),
    }}
    trigger={["click"]}
    placement="bottomLeft"
  >
    <AntButton
      size={size}
      fullIcon
      type={BUTTON.GHOST_WHITE}
      icon={<MoreVerticalIcon />}
      onClick={e => {
        e.stopPropagation();
        e.preventDefault();
      }}
    />
  </Dropdown>

    <Share
      isShowModal={isShowModalShare}
      handleCancel={onShareFolderAction}
      handleAfterShare={handleAfterShare}
      queryAccess={{ folderId: folderData._id }}
      name={folderData?.folderName}
      owner={folderData?.ownerId}
      workspaceId={workspaceId}
    />
    <RenameDescription
      isShowModal={isShowModalRename}
      initialValue={folderData?.folderName}
      handleClose={onRenameFolderAction}
      handleSubmit={renameFolder}
      placeholder={t("FOLDER_NAME_PLACEHOLDER")}
      required
    />
  </>;
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {
};

export default connect(mapStateToProps, mapDispatchToProps)(FolderActionsDropdown);
