import { useMemo } from "react";
import { Dropdown } from "antd";
import { useTranslation } from "react-i18next";

import ChevronDown from "@component/SvgIcons/ChevronDown";

import "./SelectWorkspace.scss";

export default function SelectWorkspace({ ...props }) {
  const { t } = useTranslation();
  const { workspaces, activeKey, setActiveKey } = props;
  
  const dropdownItems = useMemo(() => {
    return workspaces.map((workspace) => {
      return {
        key: workspace._id,
        label: `${workspace.userId?.fullName || workspace.organizationId?.name}’s Workspace`,
        onClick: () => setActiveKey(workspace._id),
      };
    });
  }, [workspaces]);
  
  return (
    <Dropdown
      className="select-workspace-btn"
      menu={{
        selectedKeys: [activeKey],
        items: [...dropdownItems],
        className: 'select-workspace-menu',
      }}
      trigger={["click"]}
      placement="bottomLeft"
    >
      <div>
        {t("SELECT_WORKSPACE")}
        <ChevronDown/>
      </div>
    </Dropdown>
  );
}
