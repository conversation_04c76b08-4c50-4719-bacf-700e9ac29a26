export const Mood = () => {
  return (<svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8 1.75C6.66498 1.75 5.35994 2.14588 4.2499 2.88758C3.13987 3.62928 2.27471 4.68349 1.76382 5.91689C1.25292 7.15029 1.11925 8.50749 1.3797 9.81686C1.64015 11.1262 2.28303 12.329 3.22703 13.273C4.17104 14.217 5.37377 14.8598 6.68314 15.1203C7.99252 15.3808 9.34971 15.2471 10.5831 14.7362C11.8165 14.2253 12.8707 13.3601 13.6124 12.2501C14.3541 11.1401 14.75 9.83502 14.75 8.5C14.748 6.7104 14.0362 4.99466 12.7708 3.72922C11.5053 2.46378 9.78961 1.75199 8 1.75ZM8 13.75C6.96165 13.75 5.94662 13.4421 5.08326 12.8652C4.2199 12.2883 3.547 11.4684 3.14964 10.5091C2.75228 9.54978 2.64831 8.49418 2.85088 7.47578C3.05345 6.45738 3.55347 5.52191 4.28769 4.78769C5.02192 4.05346 5.95738 3.55345 6.97578 3.35088C7.99418 3.1483 9.04978 3.25227 10.0091 3.64963C10.9684 4.04699 11.7883 4.7199 12.3652 5.58326C12.9421 6.44661 13.25 7.46165 13.25 8.5C13.2485 9.89193 12.6949 11.2264 11.7107 12.2107C10.7264 13.1949 9.39193 13.7485 8 13.75ZM11.25 10.5C11.25 10.6989 11.171 10.8897 11.0303 11.0303C10.8897 11.171 10.6989 11.25 10.5 11.25H5.5C5.30109 11.25 5.11033 11.171 4.96967 11.0303C4.82902 10.8897 4.75 10.6989 4.75 10.5C4.75 10.3011 4.82902 10.1103 4.96967 9.96967C5.11033 9.82902 5.30109 9.75 5.5 9.75H10.5C10.6989 9.75 10.8897 9.82902 11.0303 9.96967C11.171 10.1103 11.25 10.3011 11.25 10.5ZM4.75 7.25C4.75 7.05222 4.80865 6.85888 4.91853 6.69443C5.02841 6.52998 5.18459 6.40181 5.36732 6.32612C5.55005 6.25043 5.75111 6.23063 5.94509 6.26921C6.13907 6.3078 6.31726 6.40304 6.45711 6.54289C6.59696 6.68275 6.6922 6.86093 6.73079 7.05491C6.76937 7.24889 6.74957 7.44996 6.67388 7.63268C6.5982 7.81541 6.47002 7.97159 6.30557 8.08147C6.14112 8.19135 5.94778 8.25 5.75 8.25C5.48479 8.25 5.23043 8.14464 5.0429 7.95711C4.85536 7.76957 4.75 7.51522 4.75 7.25ZM11.25 7.25C11.25 7.44778 11.1914 7.64112 11.0815 7.80557C10.9716 7.97002 10.8154 8.09819 10.6327 8.17388C10.45 8.24957 10.2489 8.26937 10.0549 8.23079C9.86093 8.1922 9.68275 8.09696 9.5429 7.95711C9.40304 7.81725 9.3078 7.63907 9.26922 7.44509C9.23063 7.25111 9.25044 7.05004 9.32612 6.86732C9.40181 6.68459 9.52998 6.52841 9.69443 6.41853C9.85888 6.30865 10.0522 6.25 10.25 6.25C10.5152 6.25 10.7696 6.35536 10.9571 6.54289C11.1446 6.73043 11.25 6.98478 11.25 7.25Z" fill="#FF834D" />
  </svg>
  );
}