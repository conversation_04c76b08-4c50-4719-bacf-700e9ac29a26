import React from "react";
import "./Trash.scss";

export default function Trash() {
  return (
    <svg width="16" height="16" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="Frame" clipPath="url(#clip0_1497_9938)">
        <g id="Group">
          <g id="Clip path group">
            <mask id="mask0_1497_9938" style={{ maskType: "luminance" }} maskUnits="userSpaceOnUse" x="0" y="0"
                  width="12" height="12">
              <g id="SVGID_2_">
                <path id="Vector" d="M12 0H0V12H12V0Z" fill="white" />
              </g>
            </mask>
            <g mask="url(#mask0_1497_9938)">
              <g id="Group_2">
                <path id="Vector_2" d="M1.5 3H2.5H10.5" stroke="#858585" strokeLinecap="round" strokeLinejoin="round" />
                <path id="Vector_3"
                      d="M9.5 3V10C9.5 10.55 9.05 11 8.5 11H3.5C2.95 11 2.5 10.55 2.5 10V3M4 3V2C4 1.45 4.45 1 5 1H7C7.55 1 8 1.45 8 2V3"
                      stroke="#858585" strokeLinecap="round" strokeLinejoin="round" />
              </g>
            </g>
          </g>
        </g>
      </g>
      <defs>
        <clipPath id="clip0_1497_9938">
          <rect width="12" height="12" fill="white" />
        </clipPath>
      </defs>
    </svg>
  
  );
}
