const ProgressCircle = ({ percent }) => {
  const radius = 50; // b<PERSON> k<PERSON>h
  const stroke = 10; // độ dày
  const normalizedRadius = radius - stroke / 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  const strokeDashoffset = circumference - (percent / 100) * circumference;

  return (
    <div className="w-[100px] h-[100px] relative">
      <svg
        height="100%"
        width="100%"
        viewBox="0 0 100 100"
        className="transform -rotate-90"
      >
        {/* Circle background */}
        <circle
          stroke="#e4dffe"
          fill="transparent"
          strokeWidth={stroke}
          r={normalizedRadius}
          cx="50"
          cy="50"
        />
        {/* Circle progress */}
        <circle
          stroke="#4d45be"
          fill="transparent"
          strokeWidth={stroke}
          strokeLinecap="round"
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          r={normalizedRadius}
          cx="50"
          cy="50"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center text-[#4d45be] font-semibold text-[24px]">
        {percent}%
      </div>
    </div>
  );
};

export default ProgressCircle;