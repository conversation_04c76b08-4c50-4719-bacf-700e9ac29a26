import PropTypes from "prop-types";

import STAR from "@src/asset/icon/star/star.svg";
import EMPTY_STAR from "@src/asset/icon/star/empty-star.svg";

import "./Star.scss";
import clsx from "clsx";

const Star = ({
                onClick = () => null,
                visible = false,
                className = "",
                ...props
              }) => {
  
  const { active } = props;
  
  async function handleChangeStar(e) {
    e.preventDefault();
    onClick();
  }
  
  return <div
    className={clsx("star-container", {
      "star-container__visible": visible || active,
      [className]: !!className,
    })}
    onClick={handleChangeStar}
  >
    <img src={active ? STAR : EMPTY_STAR} alt="" />
  </div>;
};


Star.propTypes = {
  onClick: PropTypes.func,
};


export default Star;
