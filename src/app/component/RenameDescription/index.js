import { useEffect, useRef, useState } from "react";
import { Form, Input } from "antd";
import { useTranslation } from "react-i18next";

import { AntForm } from "@src/app/component/AntForm";
import CustomModal from "../CustomModal";

import RULE from "@rule";

import "./RenameDescription.scss";

function RenameDescription({ ...props }) {
  const { t } = useTranslation();
  const { isShowModal, initialValue, title, placeholder, handleClose, handleSubmit, id } = props;
  const [form] = Form.useForm();
  const inputRef = useRef();
  
  const [isLoading, setLoading] = useState(false);
  
  useEffect(() => {
    if (isShowModal) {
      form.setFieldsValue({ "inputValue": initialValue });
      inputRef.current.focus();
    }
  }, [isShowModal]);
  
  const onSubmit = async (values) => {
    setLoading(true);
    await handleSubmit(values.inputValue);
    setLoading(false);
  };
  
  if (!isShowModal) return null;
  
  return (
    <CustomModal
      width={432}
      isShowModal={isShowModal}
      handleCancel={handleClose}
      loadingOkButton={isLoading}
      title={title || t("RENAME")}
      form={`rename-description-form-${id}`}
      className="rename-modal"
    >
      <AntForm form={form} onFinish={onSubmit} size="large" id={`rename-description-form-${id}`}>
        <AntForm.Item name="inputValue" rules={[RULE.REQUIRED]}>
          <Input
            placeholder={placeholder}
            allowClear
            ref={inputRef}
            autoComplete="off"
          />
        </AntForm.Item>
      </AntForm>
    </CustomModal>
  );
}

export default RenameDescription;