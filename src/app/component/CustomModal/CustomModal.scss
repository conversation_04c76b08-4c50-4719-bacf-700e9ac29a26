:root {
  --modal-content-border-color: #F1F1F1;
  --modal-cancel-text-color: #000;
  --modal-disable-button-bg: #EEEEEE;
}

[data-theme='dark'] {
  --modal-content-border-color: #4F4F4F;
  --modal-cancel-text-color: #FFF;
  --modal-disable-button-bg: rgba(56, 53, 53, 0.50);
}

.mask-custom-modal {
  background: rgba(11, 11, 11, 0.80) !important;
}

.wrap-custom-modal {
  display: flex;
  justify-content: center;
  align-items: center;

  .ant-modal {
    top: unset;
    margin: unset;
    padding: unset;

    .ant-modal-content {
      border-radius: 8px;
      border: 1px solid var(--modal-content-border-color);
      background: var(--background-light-background-2);
      padding: 0;
    }

    .ant-modal-header {
      display: none;
    }
  }

  .custom-modal__content {
    display: inline-flex;
    padding: 24px;
    flex-direction: column;
    gap: 24px;
    width: 100%;
    font-size: 16px;

    .custom-modal__title {
      width: 100%;
      font-weight: 600;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .custom-modal__body {
      margin-top: -8px;
    }

    .custom-modal__footer {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 32px;

      &.footer-align-center {
        justify-content: center;
      }

      button {
        box-shadow: none;
      }
    }
  }
}