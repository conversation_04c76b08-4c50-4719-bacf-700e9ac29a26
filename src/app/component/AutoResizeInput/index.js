import React, { useEffect, useMemo, useRef, useState } from "react";
import { Form, Input } from "antd";

import { randomKey } from "@common/functionCommons";

import "./AutoResizeInput.scss";

const AutoResizeInput = ({ value, disabledEdit, placeholder, maxLength, onSubmit, extra }) => {
  const containerRef = useRef(null);
  const extraRef = useRef(null);
  const inputRef = useRef(null);
  
  const [isFirst, setFirst] = useState(true);
  
  const [form] = Form.useForm();
  const [inputValue, setInputValue] = useState("");
  
  const autoResizeId = useMemo(() => randomKey().toString(), []);
  
  
  useEffect(() => {
    setWidthInit();
  }, [value]);
  
  function setWidthInit() {
    form.setFieldsValue({ autoResizeValue: value || "" });
    handleSetInputWidth(value);
    
    // double check first time
    if (isFirst) {
      setFirst(false);
      setTimeout(() => {
        handleSetInputWidth(value);
      }, 100);
    }
  }
  
  
  function calculateTextWidth(text, fontFamily, fontSize, fontWeight, fontStyle) {
    return new Promise(resolve => {
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");
      
      requestAnimationFrame(() => {
        context.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`;
        const metrics = context.measureText(text);
        resolve(Math.ceil(metrics.width));
      });
    });
  }
  
  function calculateTextWidthSpan(text, fontFamily, fontSize, fontWeight, fontStyle) {
    return new Promise(resolve => {
      const span = document.createElement("span");
      span.textContent = text;
      span.style.fontFamily = fontFamily;
      span.style.fontSize = fontSize + "px";
      span.style.fontWeight = fontWeight;
      span.style.fontStyle = fontStyle;
      span.style.position = "absolute";
      span.style.top = "-9999px";
      span.style.left = "-9999px";
      span.style.visibility = "hidden";
      document.body.appendChild(span);
      
      requestAnimationFrame(() => {
        const width = span.offsetWidth;
        document.body.removeChild(span);
        resolve(width);
      });
    });
  }
  
  
  const handleInputChange = (e) => {
    const inputValue = e.target.value;
    handleSetInputWidth(inputValue);
  };
  
  async function handleSetInputWidth(inputValue) {
    if (!containerRef.current) return;

    const containerStyle = window.getComputedStyle(containerRef.current);
    const inputStyle = window.getComputedStyle(inputRef.current.input);
    
    const extraWidth = !!extra && extraRef.current
      ? parseFloat(window.getComputedStyle(extraRef.current)?.width || 0)
      : 0;
    const maxWidth = parseFloat(containerStyle.width);
    const containerGap = extraWidth ? parseFloat(containerStyle.gap) : 0;
    
    const { fontFamily, fontStyle, fontWeight } = inputStyle;
    
    const fontSize = parseFloat(inputStyle.fontSize);
    const paddingLeft = parseFloat(inputStyle.paddingLeft);
    const paddingRight = parseFloat(inputStyle.paddingRight);
    const borderLeftWidth = parseFloat(inputStyle.borderLeftWidth);
    const borderRightWidth = parseFloat(inputStyle.borderRightWidth);
    
    setInputValue(inputValue);
    
    const textWidth = await calculateTextWidthSpan(inputValue, fontFamily, fontSize, fontWeight, fontStyle);
    
    const inputWidth = textWidth + paddingLeft + paddingRight + borderLeftWidth + borderRightWidth + 1;
    
    setInputWidth(Math.min(inputWidth, maxWidth - extraWidth - containerGap));
  }
  
  function setInputWidth(width) {
    if (!inputRef.current?.input) return;
    inputRef.current.input.style.width = `${Math.max(width, 50)}px`;
  }
  
  const handleSubmit = () => {
    onSubmit(inputValue);
  };
  
  return (<div
      ref={containerRef}
      className="auto-resize-input"
    >
      <div>
        <Form
          id={autoResizeId}
          form={form}
          onFinish={handleSubmit}
        >
          <Form.Item name="autoResizeValue">
            <Input
              ref={inputRef}
              //style={{ width: Math.max(inputWidth, 50) }}
              value={inputValue}
              onChange={handleInputChange}
              placeholder={placeholder}
              maxLength={maxLength}
              onPressEnter={handleSubmit}
              onBlur={handleSubmit}
              disabled={disabledEdit}
            />
          </Form.Item>
        </Form>
      </div>
      
      
      {!!extra && <div
        ref={extraRef}
        className="auto-resize-input__extra">
        {extra}
      </div>}
    </div>
  );
};

export default AutoResizeInput;