
.modal-wrapper {

  &.modal-wrapper__center {
    display: flex;

    .ant-modal {
      top: 0;
      margin: auto;
    }
  }


  .modal-content {
    .ant-modal-close {
      top: 24px;
      inset-inline-end: 32px;
      width: 40px;
      height: 40px;
      box-shadow: var(--shadow-level-2);
      border-radius: 50%;

      &:hover {
        background: var(--primary-colours-blue-navy-light-1);
        border-color: var(--primary-colours-blue-navy-light-1);
      }

      &:active {
        color: #FFF;
        background: var(--primary-colours-blue-navy);
        border-color: var(--primary-colours-blue-navy);

        .ant-btn-icon svg path, svg path {
          stroke: var(--white);
        }
      }
    }

    .modal-header {

      .ant-modal-title {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .ant-modal-title__text {
          color: var(--primary-colours-blue-navy);
          font-weight: 700;
          font-size: 24px;
        }

        .ant-modal-title__description {
          color: var(--typo-colours-support-blue-light);
          font-weight: 400;
          font-size: 16px;
        }
      }
    }

    .modal-body {

    }

    .modal-footer {
      &.modal-footer__center {
        text-align: center;
      }
    }
  }
}
