import { useCallback, useMemo } from "react";
import { Modal } from "antd";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";

import AntButton from "@component/AntButton";

import { BUTTON, CONSTANT } from "@constant";

import Time20 from "@component/SvgIcons/TimeIcon/Time20";

import "./AntModal.scss";
import clsx from "clsx";

const AntModal = ({
                    title, description, formId,
                    cancelText = "CANCEL",
                    okText = "DONE",
                    footerless = false,
                    confirmLoading = false,
                    align = CONSTANT.TOP,
                    footerAlign = CONSTANT.RIGHT,
                    open, onOk, onCancel,
                    ...props
                  }) => {
  const { t } = useTranslation();
  
  const { submitting } = props;
  
  const modalTitle = useMemo(() => {
    if (!title && !description) return null;
    return <>
      {!!title && <div className="ant-modal-title__text">{title}</div>}
      {!!description && <div className="ant-modal-title__description">{description}</div>}
    </>;
  }, [title, description]);
  
  const footerClassName = useMemo(() => {
    return clsx("modal-footer", {
        "modal-footer__center": footerAlign === CONSTANT.CENTER,
      },
    );
  }, [footerAlign]);
  
  const wrapperClassName = useMemo(() => {
    return clsx("modal-wrapper", {
        "modal-wrapper__center": align === CONSTANT.CENTER,
      },
    );
  }, [align]);
  
  const handleCancel = useCallback(() => confirmLoading ? () => null : onCancel(), [confirmLoading]);
  
  return <>
    <Modal
      classNames={{
        mask: "modal-mask",
        wrapper: wrapperClassName,
        content: "modal-content",
        body: "modal-body",
        header: "modal-header",
        footer: footerClassName,
      }}
      closeIcon={confirmLoading ? null : <Time20 />}
      title={modalTitle}
      open={!!open}
      onOk={onOk}
      onCancel={handleCancel}
      footer={footerless ? null : [
        <AntButton
          key="back"
          size="large"
          onClick={handleCancel}
          type={BUTTON.WHITE}
          disabled={confirmLoading}
        >
          {t(cancelText)}
        </AntButton>,
        <AntButton
          key="submit"
          size="large"
          type={BUTTON.DEEP_NAVY}
          htmlType="submit"
          form={formId}
          disabled={confirmLoading || submitting}
        >
          {t(okText)}
        </AntButton>,
      ]}
      {...props}
    >
      {props?.children}
    </Modal>
  </>;
};

AntModal.propTypes = {
  cancelText: PropTypes.string,
  footerless: PropTypes.bool,
};

export default (AntModal);
