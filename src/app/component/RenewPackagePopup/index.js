import React from 'react';
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import { Modal } from 'antd';
import { LINK } from "@link";
import AntButton from "@src/app/component/AntButton";
import { BUTTON } from "@constant";
import "./styles.scss";
import { css } from '@emotion/react';
import { CloseOutlined } from "@ant-design/icons";
const RenewPackagePopup = ({ isOpen, onClose, type = "package" }) => {
  const { t } = useTranslation();

  const renderTitle = () => {
    switch (type) {
      case "package":
        return t("RENEW_ACCOUNT");
      case "writing":
        return t("RENEW_ACCOUNT");
      case "speaking":
        return t("RENEW_ACCOUNT");
      default:
        return t("RENEW_ACCOUNT");
    }
  };

  const renderDescription = () => {
    let description;
    switch (type) {
      case "package":
        description = t("RENEW_ACCOUNT_DESCRIPTION", { days: 3, submissions: 3 });
        break;
      case "writing":
        description = t("RENEW_ACCOUNT_DESCRIPTION", { days: 3, submissions: 3 });
        break;
      case "speaking":
        description = t("RENEW_ACCOUNT_DESCRIPTION", { days: 3, submissions: 3 });
        break;
      default:
        description = t("RENEW_ACCOUNT_DESCRIPTION", { days: 3, submissions: 3 });
    }
    return description.split('\n').map((line, index) => (
      <React.Fragment key={index}>
        {line}
        <br />
      </React.Fragment>
    ));
  };
  const modalMaskStyle = css`
  .ant-modal-mask {
    background-color: rgba(0, 0, 0, 0.2) !important;
  }`;
  return (
    <Modal
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={542}
      className="renew-package-popup"
      centered
      css={modalMaskStyle}
      maskClosable={false}
      closable={false}
    >
      <div className="renew-package-popup__header">
        <div className="renew-package-popup__title">
          {renderTitle()}
        </div>
        <div className="renew-package-popup__close">
          <CloseOutlined onClick={onClose} />
        </div>
      </div>
      <div className="renew-package-popup__content">
        <div className="renew-package-popup__description">
          {renderDescription()}
        </div>
        <div className="renew-package-popup__actions">
          <Link to={LINK.PRICING} onClick={onClose}>
            <AntButton className="btn-renew">
              {t("RENEW_NOW").toUpperCase()}
            </AntButton>
          </Link>
        </div>
      </div>
    </Modal>
  );
};

export default RenewPackagePopup; 