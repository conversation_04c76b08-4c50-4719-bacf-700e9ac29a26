.renew-package-popup {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  width: 542px;
  height: 246px;
  filter: drop-shadow(0px 4px 30px rgba(0, 0, 0, 0.15));
  border-radius: 16px;
  flex: none;
  order: 1;
  flex-grow: 0;

  .ant-modal-content {
    padding: 0;
    border-radius: 16px;
    background: #FFFFFF;
    box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.15);
  }

  .ant-modal-close {
    top: 16px;
    right: 24px;
    color: #000000;
  }

  &__header {
    padding: 16px 24px;
    border-bottom: 1px solid;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-image-source: linear-gradient(90deg, #FFFFFF 0%, #E0E0E0 50.5%, #FFFFFF 100%);
    border-image-slice: 1;
  }

  &__title {
    font-family: Inter;
    font-size: 22px;
    font-weight: 600;
    line-height: 30px;
    color: #000000;
    margin: 0;
  }

  &__content {
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
  }

  &__description {
    font-family: Inter;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: #000000;
    text-align: center;
    margin: 0;
    max-width: 494px;
  }

  &__actions {
    display: flex;
    justify-content: center;
    width: 100%;

    .btn-renew {
      padding: 8px 24px;
      border-radius: 12px;
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      background: #26D06D !important;
      border-color: #26D06D !important;
      color: #FFFFFF !important;

      &:hover {
        background: #1fb35d !important;
        border-color: #1fb35d !important;
      }
    }

    .btn-cancel {
      display: none;
    }
  }

} 

.ant-modal-mask {
  background-color: rgba(0, 0, 0, 0.2) !important;
}
