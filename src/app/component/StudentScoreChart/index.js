import { useMemo } from "react";
import { Progress } from "antd";
import { useTranslation } from "react-i18next";

import { CONSTANT } from "@constant";

import "./StudentScoreChart.scss";

const AZURE_SCORE_SCALE = [
  { text: "0 ~ 59", color: "#C658FB" },
  { text: "60 ~ 79", color: "#316BFF" },
  { text: "80 ~ 100", color: "#41D592" },
];


const IELTS_SCORE_SCALE = [
  { text: "0 ~ 4.5", color: "#C658FB" },
  { text: "5.0 ~ 7.0", color: "#316BFF" },
  { text: "7.5 ~ 9.0", color: "#41D592" },
];

function renderAzureScoreColor(score) {
  if (score < 60) {
    return "#C658FB";
  } else if (score < 80) {
    return "#316BFF";
  } else {
    return "#41D592";
  }
}

function renderIeltsScoreColor(score) {
  if (score < 5) {
    return "#C658FB";
  } else if (score <= 7.5) {
    return "#316BFF";
  } else {
    return "#41D592";
  }
}

function StudentScoreChart({
                             label,
                             scoreBreakdown,
                             chartType,
                           }) {
  const { t } = useTranslation();
  
  const overall = useMemo(() => {
    if (!scoreBreakdown?.length || chartType === CONSTANT.IELTS) return 0;
    
    const validValues = scoreBreakdown
      .map(item => (item && typeof item.value === "number" ? item.value : 0));
    
    if (validValues.length === 0) return 0;
    
    const sum = validValues.reduce((sum, val) => sum + val, 0);
    return Math.round(sum / scoreBreakdown?.length);
  }, [scoreBreakdown]);
  
  
  function renderScoreColor(score) {
    switch (chartType) {
      case CONSTANT.AZURE:
        return renderAzureScoreColor(score);
      case CONSTANT.IELTS:
        return renderIeltsScoreColor(score);
      default:
        return "#000000";
    }
  }
  
  const scoreScale = useMemo(() => {
    switch (chartType) {
      case CONSTANT.AZURE:
        return AZURE_SCORE_SCALE;
      case CONSTANT.IELTS:
        return IELTS_SCORE_SCALE;
      default:
        return [];
    }
  }, [chartType]);
  
  const maxScore = useMemo(() => {
    switch (chartType) {
      case CONSTANT.AZURE:
        return 100;
      case CONSTANT.IELTS:
        return 9;
      default:
        return 0;
    }
  }, [chartType]);
  
  function calPercent(score) {
    if (chartType === CONSTANT.AZURE) return score;
    return 100 / 9 * score;
  }
  
  return <div className="speaking-result-chart">
    <div className="speaking-chart__header">
      <div className="speaking-chart__header-title">
        {label}
      </div>
      <div className="speaking-chart__score-scale">
        {scoreScale.map((scoreScale, index) => {
          return <div key={index} className="speaking-score-scale__item">
            <div className="speaking-score-scale__color" style={{ backgroundColor: scoreScale.color }} />
            <div className="speaking-score-scale__text">
              {scoreScale.text}
            </div>
          </div>;
        })}
      </div>
    </div>
    <div className="speaking-chart__body">
      {chartType !== CONSTANT.IELTS && <div className="speaking-chart__overall">
        <Progress
          percent={overall}
          type="circle"
          strokeColor={renderScoreColor(overall)}
          trailColor="#E7ECEE"
          strokeLinecap="butt"
          size={120}
          strokeWidth={10}
          format={() => overall}
        />
      </div>}
      <div className="speaking-chart__breakdown">
        {scoreBreakdown
          ?.map((score, index) => {
            return <div key={index} className="speaking-chart-breakdown_item">
              <div className="speaking-chart-breakdown__info">
                <div className="speaking-chart-breakdown__title">
                  {t(score.lang)}
                </div>
                <div className="speaking-chart-breakdown__percentage-score">
                  <span className="speaking-chart-breakdown__score">{score.value || 0}</span>
                  <span>/{maxScore}</span>
                </div>
              </div>
              <div className="speaking-chart-breakdown__chart">
                <Progress
                  percent={calPercent(score.value)}
                  //strokeColor="#218d51"
                  strokeColor={renderScoreColor(score.value)}
                  trailColor="#E7ECEE"
                  strokeLinecap="butt"
                  size={[100, 6]}
                  showInfo={false}
                />
              </div>
            </div>;
          })}
      </div>
    </div>
  </div>;
}

export default StudentScoreChart;