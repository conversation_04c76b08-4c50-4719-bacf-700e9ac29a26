.speaking-result-chart {
  width: 100%;
  user-select: none;
  background-color: #FFFFFF;
  padding: 16px;
  border-radius: 24px;
  box-shadow: 0 4px 20px 0 #0000001A;
  display: flex;
  flex-direction: column;
  gap: 8px;
  //min-width: 390px;

  .speaking-chart__header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap-reverse;
    gap: 8px;

    .speaking-chart__header-title {
      color: var(--navy);
      font-size: 14px;
      line-height: 20px;
      font-weight: 500;
      min-width: 140px;
    }

    .speaking-chart__score-scale {
      display: flex;
      flex-direction: row;
      gap: 8px;

      .speaking-score-scale__item {
        display: flex;
        flex-direction: row;
        gap: 4px;
        align-items: center;

        .speaking-score-scale__color {
          width: 16px;
          height: 16px;
          border-radius: 4px;
        }

        .speaking-score-scale__text {
          font-size: 12px;
          line-height: 20px;
        }
      }
    }
  }

  .speaking-chart__body {
    display: flex;
    flex-direction: row;
    gap: 8px;

    .speaking-chart__overall {
      height: 120px;
      width: 120px;

      .ant-progress {
        .ant-progress-text {
          font-size: 24px;
          font-weight: 700;
        }
      }
    }

    .speaking-chart__breakdown {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 15px;

      .speaking-chart-breakdown_item {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .speaking-chart-breakdown__info {
          display: flex;
          flex-direction: row;
          gap: 10px;
          justify-content: space-between;
          font-size: 12px;
          line-height: 20px;

          .speaking-chart-breakdown__title {

          }

          .speaking-chart-breakdown__percentage-score {
            .speaking-chart-breakdown__score {
              font-weight: 700;
            }
          }
        }

        .speaking-chart-breakdown__chart {
          .ant-progress {
            display: flex;

            .ant-progress-outer {
              width: 100% !important;

              .ant-progress-inner {

                .ant-progress-bg.ant-progress-bg-outer {
                  height: 6px !important;
                }
              }
            }
          }
        }
      }
    }
  }
}