:root {
  --folder-icon: url('../../../asset/icon/folder/folder-light.svg');
}

[data-theme='dark'] {
  --folder-icon: url('../../../asset/icon/folder/folder-dark.svg');
}

.modal-move {
  width: 352px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  color: var(--modal-color);
  font-size: 16px;
  line-height: 20px;
  text-align: justify;
  
  .modal-move__loading {
    display: flex;
    align-items: center;
    width: 100%;
    height: 80px !important;

    .loading-spin {
      top: 50%;
    }
  }

  .modal-move__no-permission {
    color: #F50606;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.25;
  }

  .modal-move__folder_item__icon {
    background-image: var(--folder-icon);
    height: 16px;
    width: 16px;
    flex-shrink: 0;
  }

  .modal-move__current-location {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    max-width: 100%;
    word-break: break-all;
    text-align: left;

    .current-location__label {
      color: var(--typo-colours-support-blue-light);
      font-size: 16px;
      font-weight: 400;
      padding-right: 16px;
    }

    .modal-move__folder_item__icon {
      margin-right: 8px;
      display: inline-block;
    }

    .current-location__folder-name {
      align-items: center;
      color: #2196f3;
      font-size: 16px;
      font-weight: 400;
      line-height: 23px;
    }
  }

  .modal-move__suggested {
    color: var(--modal-color);
    font-size: 16px;
    font-weight: 600;
  }

  .modal-move__folder {
    width: 100%;
    max-height: 259px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .modal-move__folder_item {
      color: var(--typo-colours-primary-black);
      display: flex;
      padding: 8px 16px;
      align-items: center;
      gap: 8px;
      width: 100%;
      line-height: 23px;
      box-sizing: border-box;
      cursor: pointer;
      word-break: break-all;
    
      &:hover {
        background: var(--primary-colours-blue-light-1);
      }
    
      &.active {
        background: var(--primary-colours-blue);
        color: var(--typo-colours-support-white);
      }
    
      span {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
      }
    
    }
  }
}