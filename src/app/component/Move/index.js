import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import CustomModal from "../CustomModal";
import Loading from "@component/Loading";

import { moveProject, getProjectDetail, getProjectPermission } from "@services/Project";
import { getAvailableFolder } from '@services/Folder';
import { toast } from "@component/ToastProvider";

import { PERMISSION } from "@constant";

import "./Move.scss";
import clsx from "clsx";

const Move = ({ user, ...props }) => {
  const { isShowModal, handleCancel, projectId, workspaceId, handleAfterMove } = props;
  const { t } = useTranslation();

  const [projectData, setProjectData] = useState({});
  const [currentFolder, setCurrentFolder] = useState({});
  const [selectedFolder, setSelectedFolder] = useState({});
  const [availableFolders, setAvailableFolders] = useState([]);
  const [isViewer, setViewer] = useState(false);
  const [isFirst, setFirst] = useState(false);
  const [isLoading, setLoading] = useState(false);

  useEffect(() => {
    if (isShowModal) {
      getAllData();
    }
  }, [isShowModal]);

  const getAllData = async () => {
    setFirst(true);
    const request = [getProjectDetail(projectId), getProjectPermission(projectId), getAvailableFolder(workspaceId)];
    const [projectDetailData, permissionResponse, availableFolderData] = await Promise.all(request);
    if (!permissionResponse || permissionResponse?.code === 404) {
      handleCancel();
      return;
    }
    if ([PERMISSION.VIEWER, PERMISSION.NO_PERMISSION].includes(permissionResponse.permission)) {
      setViewer(true);
    }
    if (projectDetailData) {
      setProjectData(projectDetailData?.data);
      if (projectDetailData?.data?.folderId) {
        setCurrentFolder({
          _id: projectDetailData?.data?.folderId?._id,
          folderName: projectDetailData?.data?.folderId?.folderName,
        });
      } else setCurrentFolder({});
    }
    if (availableFolderData) {
      setAvailableFolders(availableFolderData);
    }
    setFirst(false);
  };

  const hanldeSelectFolder = folder => {
    setSelectedFolder({
      _id: folder?._id,
      folderName: folder?.folderName,
    });
  };

  const handleOk = async () => {
    setLoading(true);
    const dataRequest = {
      _id: projectData?._id,
      folderId: selectedFolder?._id,
    };
    const params = { workspaceId: projectData?.workspaceId };
    const dataResponse = await moveProject(dataRequest, params);
    if (dataResponse) {
      toast.success("MOVE_PROJECT_SUCCESS");
      handleCancel();
      if (handleAfterMove) {
        handleAfterMove(dataResponse);
      }
    }
    setLoading(false);
  };

  const disableSubmit = !selectedFolder?._id;
  const title = projectData?.projectName ? `${t("MOVE")} "${projectData?.projectName}"` : t("MOVE");

  return <CustomModal
    width={400}
    isShowModal={isShowModal}
    handleOk={handleOk}
    handleCancel={handleCancel}
    className="modal-move"
    loadingOkButton={isLoading}
    disableOkButton={disableSubmit}
    title={title}
    okText={t("MOVE")}
  >
    {isFirst ? <Loading active className="modal-move__loading" /> : <>
      {currentFolder?.folderName && <div className="modal-move__current-location">
        <span className="current-location__label">
          {t('CURRENT_LOCATION')}:
        </span>
        <span className="modal-move__folder_item__icon" />
        <span className="current-location__folder-name">
          {currentFolder?.folderName}
        </span>
      </div>}
      {isViewer ? <div className="modal-move__no-permission">{t("NOT_HAVE_PERMISSION_MOVE_PROJECT")}</div>
        :
        <>
          <div className="modal-move__suggested">{t('SUGGESTED')}</div>
          <div className="modal-move__folder">
            {!!availableFolders?.length && availableFolders.map((folder, index) => {
              if (projectData?.folderId && projectData?.folderId?._id === folder?._id) return null;
              const isActive = folder?._id === selectedFolder?._id;
              return <div
                className={clsx("modal-move__folder_item", { active: isActive })}
                onClick={() => hanldeSelectFolder(folder)}
                key={index}
              >
                <div className="modal-move__folder_item__icon" />
                <span>
                  {folder?.folderName}
                </span>
              </div>;
            })}
          </div>
        </>}
    </>}
  </CustomModal>;
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(Move);
