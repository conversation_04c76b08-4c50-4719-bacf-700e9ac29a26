import React from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import PropTypes from "prop-types";
import { Form } from "antd";

import TimeStepper from "@component/TimeStepper";

import { convertSecondToMs } from "@common/functionCommons";

import "./DurationInput.scss";

function DurationInput({ maxDurationSeconds, onChange = () => null, ...props }) {
  const { t } = useTranslation();
  const { value, disabled, maxValue } = props;
  
  return <div className="duration-input">
    <Form.Item label={t("MAX_DURATION")}>
      <label className="label-input-disabled">{`${maxDurationSeconds / 60} ${t("MINUTES")}`.toLowerCase()}</label>
    </Form.Item>
    
    <Form.Item label={t("START")}>
      <TimeStepper
        value={value.start}
        min={value.end - maxDurationSeconds}
        max={value.end}
        onChange={start => onChange({ ...value, start })}
        disabled={disabled}
      />
    </Form.Item>
    
    <Form.Item label={t("END")}>
      <TimeStepper
        value={value.end}
        min={value.start}
        max={Math.min(value.start + maxDurationSeconds, maxValue)}
        onChange={end => onChange({ ...value, end })}
        disabled={disabled}
      />
    </Form.Item>
    
    <Form.Item label={t("DURATION")}>
      <label className="label-input-disabled">
        {convertSecondToMs(value?.end - value?.start)}
      </label>
    </Form.Item>
  </div>;
}

DurationInput.propTypes = {
  onChange: PropTypes.func.isRequired,
};

function mapStateToProps(store) {
  const { maxDurationSeconds } = store.app;
  return { maxDurationSeconds };
}

export default connect(mapStateToProps)(DurationInput);