
.output-language-container {
  display: flex;
  font-weight: 500;
  line-height: 24px;
  color: var(--navy);

  .ant-dropdown-trigger {
    display: flex;
    padding: 11px 15px;
    border: 1px solid #DBDBDB;
    gap: 8px;

    border-radius: 16px;
  }

  .ant-dropdown {
    min-width: unset !important;

    .ant-dropdown-menu.ant-dropdown-menu-vertical {
      padding: 0;
      gap: 0;

      .ant-dropdown-menu-item {
        padding: 12px 16px;

        .ant-dropdown-menu-title-content {
          display: flex;
          gap: 14px;

          .output-language-item__flag {
            height: 24px;
            width: 24px;
            object-fit: cover;
          }

          .output-language-item__lang {
            line-height: 24px;
            font-weight: 500;
            width: 91px;
          }
        }

        &:hover {
          background-color: #E7E5FF;
        }

        &.ant-dropdown-menu-item-selected, &.ant-dropdown-menu-item:active {
          background: var(--navy);

          .ant-dropdown-menu-title-content .output-language-item__lang {
            font-weight: 600;
          }
        }

      }
    }
  }

  .output-language__button-trigger {
    border-radius: 8px !important;
    font-size: 12px;
    gap: 4px;
    padding: 0 7px;
    width: 62px;
    justify-content: space-between;

    .ant-btn-icon svg {
      height: 20px;
      width: 20px;
    }
  }
}