import { useTranslation } from "react-i18next";
import { Dropdown } from "antd";

import AntButton from "@component/AntButton";
import { BUTTON, CONSTANT, LANGUAGE } from "@constant";

import VN_FLAG from "@src/asset/icon/flag/vn_square.svg";
import EN_FLAG from "@src/asset/icon/flag/en_square.svg";

import ChevronDown20 from "@component/SvgIcons/ChevronDown/ChevronDown20";

import "./OutputLanguage.scss";

function OutputLanguage({ disabled, value, onChange }) {
  const { t } = useTranslation();
  return (
    <div id="js-output-language" className="output-language-container">
      <Dropdown
        size="small"
        disabled={disabled}
        placement="bottomRight"
        menu={{
          selectedKeys: [value],
          items: [
            {
              key: LANGUAGE.VI,
              label: (
                <>
                  <img className="output-language-item__flag" src={VN_FLAG} alt="" />
                  <span className="output-language-item__lang">{t("VIE")}</span>
                </>
              ),
              onClick: () => onChange(LANGUAGE.VI),
            },
            {
              key: LANGUAGE.EN,
              label: (
                <>
                  <img className="output-language-item__flag" src={EN_FLAG} alt="" />
                  <span className="output-language-item__lang">{t("ENG")}</span>
                </>
              ),
              onClick: () => onChange(LANGUAGE.EN),
            },
          ],
        }}
        getPopupContainer={() => document.getElementById("js-output-language")}
      >
        <div>
          {t("OUTPUT_LANGUAGE")}

          <AntButton
            size="xsmall"
            type={BUTTON.DEEP_BLUE}
            icon={<ChevronDown20 />}
            iconLocation={CONSTANT.RIGHT}
            className="output-language__button-trigger"
            disabled={disabled}
          >
            {value === LANGUAGE.EN ? "Eng" : value === LANGUAGE.VI ? "Vie" : ""}
          </AntButton>
        </div>
      </Dropdown>
    </div>
  );
}

export default OutputLanguage;
