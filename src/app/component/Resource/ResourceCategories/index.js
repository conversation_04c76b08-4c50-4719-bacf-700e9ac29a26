import React from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import PropTypes from "prop-types";

import AntButton from "@component/AntButton";

import { BUTTON, CONSTANT } from "@constant";

import "./ResourceCategories.scss";


function ResourceCategories({ user, categorySelected, setCategorySelected, allowUpload = false }) {
  const { t } = useTranslation();
  
  return <>
    <div className="resource-categories">
      {!!allowUpload && <AntButton
        size="small"
        type={BUTTON.LIGHT_NAVY}
        active={categorySelected === CONSTANT.MY_COMPUTER}
        onClick={() => setCategorySelected(CONSTANT.MY_COMPUTER)}
      >
        {t("MY_COMPUTER")}
      </AntButton>}
      <AntButton
        size="small"
        type={BUTTON.LIGHT_NAVY}
        active={categorySelected === CONSTANT.MY_RESOURCE}
        onClick={() => setCategorySelected(CONSTANT.MY_RESOURCE)}
      >
        {t("MY_RESOURCES")}
      </AntButton>
      {!!user.organizationId && <AntButton
        size="small"
        type={BUTTON.LIGHT_NAVY}
        active={categorySelected === CONSTANT.ORG_RESOURCE}
        onClick={() => setCategorySelected(CONSTANT.ORG_RESOURCE)}
      >
        {t("RESOURCE_ORG_NAME").format(user.organizationId?.name)}
      </AntButton>}
    </div>
  </>;
}

ResourceCategories.defaultProps = {
  //allowUpload: PropTypes.bool,
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(ResourceCategories);