import { useState } from "react";
import { But<PERSON>, Dropdown } from "antd";
import { useTranslation } from "react-i18next";

import Share from '@component/Share';
import Move from "@component/Move";

import MORE_ICON from "@src/asset/icon/button/more-vertical.svg";
import COPY from "@src/asset/icon/button-newui/copy.svg";
import SHARE from "@src/asset/icon/button-newui/share.svg";
import MOVE from "@src/asset/icon/button-newui/move.svg";
import EDIT from "@src/asset/icon/button-newui/edit.svg";
import DELETE from "@src/asset/icon/button-newui/delete.svg";

import { copyProject, deleteProject } from '@services/Project';
import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";

const ProjectActionDropdown = ({ ...props }) => {
  
  const { projectData, user, className } = props;
  
  const { t } = useTranslation();

  const [isShowModalShare, setShowmodalShare] = useState(false);
  const [isShowModalMove, setIsShowModalMove] = useState(false);
  const isOwner = projectData?.ownerId?._id === user?._id;
  
  const handleCopyProjectAction = async (e) => {
    const dataRequest = { projectId: projectData._id };
    const apiResponse = await copyProject(dataRequest);
    if (apiResponse) {
      if(isOwner){
        props.getProjectData();
        props.getFolderData();
      }
      toast.success(isOwner ? "COPY_PROJECT_SUCCESS" : "COPY_PROJECT_TO_MY_WORSKSPACE_SUCCESS");
    }
  };
  
  const handleDeleteProjectAction = () => {
    confirm.delete({
      content: t("CONFIRM_DELETE_PROJECT"),
      handleConfirm: async () => {
        const apiResponse = await deleteProject(projectData._id);
        if (apiResponse) {
          props.getProjectData();
          props.getFolderData();
          toast.success("DELETE_PROJECT_SUCCESS");
        }
      },
    });
  }

  const handleShareProjectAction = (e) => {
    setShowmodalShare(prevState => !prevState);
  };
  
  const handleRenameProjectAction = (e) => {
    props.setIdEditing(true);
  };
  
  const handleMoveProjectAction = (e) => {
    setIsShowModalMove(prevState => !prevState);
  };
  
  return <><Dropdown
    className={className}
    menu={{
      items: [
        { key: "COPY", label: "Copy", icon: <img src={COPY} alt=""/>, onClick: handleCopyProjectAction },
        { key: "MOVE", label: "Move", icon: <img src={MOVE} alt=""/>, onClick: handleMoveProjectAction },
        { key: "SHARE", label: "Share", icon: <img src={SHARE} alt=""/>, onClick: handleShareProjectAction },
        { key: "RENAME", label: "Rename", icon: <img src={EDIT} alt=""/>, onClick: handleRenameProjectAction },
        { key: "DELETE", label: "Delete", icon: <img src={DELETE} alt=""/>, onClick: handleDeleteProjectAction },
      ],
    }}
    trigger={["click"]}
  >
    <Button icon={<img src={MORE_ICON} />} />
  </Dropdown >

    <Share
      isShowModal={isShowModalShare}
      handleCancel={handleShareProjectAction}
      queryAccess={{ projectId: projectData._id }}
      name={projectData?.projectName}
      owner={projectData?.ownerId}
      workspaceId={projectData?.workspaceId?._id || projectData?.workspaceId}
    />
    <Move
      isShowModal={isShowModalMove}
      handleCancel={handleMoveProjectAction}
      projectId={projectData._id}
      afterMove={props.getFolderData}
      workspaceId={projectData?.workspaceId?._id || projectData?.workspaceId}
    />
  </>;
};
export default ProjectActionDropdown;
