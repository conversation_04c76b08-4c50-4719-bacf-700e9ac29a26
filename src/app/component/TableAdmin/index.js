

import { Table } from "antd";
import { useTranslation } from "react-i18next";
import './TableAdmin.scss';
import clsx from "clsx";

const TableAdmin = (props) => {
  const { t } = useTranslation();
  const { dataSource, columns, className, pagination, ...otherProps } = props;

  const showTotal = (total, range) => {
    return `${range[0]} - ${range[1]} ${t("OF")} ${total}`;
  }
  return (
    <Table
      {...otherProps}
      className={clsx("admin-table", className)}
      dataSource={dataSource}
      columns={columns}
      pagination={pagination ? { ...pagination, showTotal } : false}
    />
  );
};

export default TableAdmin;