import React from "react";
import { Spin } from "antd";
import PropTypes from "prop-types";
import { Loading3QuartersOutlined } from "@ant-design/icons";
import ReactLoading from "react-loading";
import clsx from "clsx";

import { CONSTANT } from "@constant";

import LOADING from "@src/asset/gif/loading.gif";
import LOADING_1 from "@src/asset/gif/loading-1.gif";

import "./Loading.scss";

export default function Loading({ id, active = true, className = "", minHeight = 400, ...props }) {
  const { transparent, indicatorType, fixedMiddleScreen } = props;
  const style = {};
  if (!props.children) {
    style.height = minHeight + "px";
  }
  
  //let indicator = <img src={LOADING_1} alt="" />;
  //if (indicatorType === CONSTANT.BARS) {
  //  indicator = <ReactLoading height={32} width={32} type="bars" color="#8786DD" />;
  //}
  return (
    <div {...id ? { id } : {}} className={`loading-component${className ? ` ${className}` : ""}`} style={style}>
      {active && <div className={clsx("loading-backdrop", { "loading-backdrop-transparent": !!transparent })}>
        {/*<Spin className={fixedMiddleScreen ? "fixed-middle" : "loading-spin"}
         size="large"
         indicator={indicator}
         />*/}
        <img
          className={fixedMiddleScreen ? "fixed-middle" : "loading-spin"}
          src={LOADING_1} alt=""
        />
      </div>}
      {props.children}
    </div>
  );
}

Loading.propTypes = {
  active: PropTypes.bool,
  className: PropTypes.string,
  minHeight: PropTypes.number,
};