import { useTranslation } from "react-i18next";
import clsx from "clsx";

import NO_DATA from "@src/asset/image/no-data.svg";

import "./NoData.scss";

function NoData({ children, searchNoData = false, ...props }) {
  
  const { t } = useTranslation();
  
  return <div className="no-data-container">
    <div className={clsx("no-data", { search_no_data: searchNoData })}>
      <img src={NO_DATA} alt="" className="no-data__image" />
    </div>
    <div className="no-data__text">{children || t("NO_DATA")}</div>
  </div>;
}


export default NoData;