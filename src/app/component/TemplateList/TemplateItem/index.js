import { API } from "@api";
import React, { useMemo } from "react";
import { Dropdown, Popover } from "antd";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import { useNavigate } from "react-router-dom";
import clsx from "clsx";

import { toast } from "@component/ToastProvider";
import AntButton from "@component/AntButton";
import ImageView from "@component/ImageView";

import { BUTTON, CONSTANT } from "@constant";
import { LINK } from "@link";
import { formatTimeDate } from "@common/functionCommons";

import MoreVertical from "@component/SvgIcons/MoreVertical";
import Eye from "@component/SvgIcons/Eye";
import Edit from "@component/SvgIcons/Edit";
import Trash from "@component/SvgIcons/Trash";
import REFRESH_CW from "@src/asset/icon/refresh/refresh-cw.svg";
import BUILDING_ICON from "@src/asset/icon/building/building.svg";
import USER_ICON from "@src/asset/icon/user/user.svg";
import CLICKEE_AVATAR from "@src/asset/logo/clickee-avatar.svg";
import PROJECT_DEFAULT from "@src/asset/image/project-default.svg";

import "./TemplateItem.scss";

function TemplateItem({ user, templateData, onUseTemplate, ...props }) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { handleShowModal, handleShowPreview, showUpdate, onRename, onDelete, isShowModified } = props;

  const allowAction = useMemo(() => {
    if (!templateData) return false;

    if (templateData.type === CONSTANT.ORGANIZATION) {
      return templateData.organizationId?._id === user.organizationId?._id;
    }

    if (templateData.type === CONSTANT.PERSONAL) {
      return templateData.userId === user._id || templateData.userId?._id === user._id;
    }

    if (templateData.type === CONSTANT.SYSTEM) {
      return user.isSystemAdmin;
    }
  }, [onRename]);

  function handleRedirect() {
    if (templateData?.projectId?._id) {
      navigate(LINK.PROJECT_DETAIL.format(templateData.projectId._id));
    } else {
      toast.error("AN_ERROR_OCCURRED");
    }
  }

  function renderTemplateAction(template) {
    const templateActionItems = [];
    if (allowAction) {
      if (showUpdate) {
        templateActionItems.push({
          key: "UPDATE",
          label: t("UPDATE"),
          icon: <img src={REFRESH_CW} alt="" />,
          onClick: handleRedirect,
        });
      }
      if (onRename) {
        templateActionItems.push({
          key: "RENAME",
          label: t("RENAME"),
          icon: <Edit />,
          onClick: () => handleShowModal(true, template),
        });
      }
      if (onDelete) {
        templateActionItems.push({
          key: "DELETE",
          label: t("DELETE"),
          icon: <Trash />,
          onClick: () => onDelete(template?._id),
        });
      }
    }
    if (!templateActionItems?.length) return <></>;
    return <div className="template-item__actions" onClick={(e) => e.stopPropagation()}>
      <Dropdown
        menu={{
          items: templateActionItems,
          className: "action-dropdown-menu",
        }}
        trigger={["click"]}
        placement="bottomRight"
      >
        <AntButton
          size="tiny"
          type={BUTTON.GHOST_WHITE}
          icon={<MoreVertical />}
          onClick={(e) => e.stopPropagation()}
        />
      </Dropdown>

    </div>;
  }

  const getSrcTemplateIcon = (template) => {
    if (template?.type === CONSTANT.ORGANIZATION) {
      const imageId = template?.organizationId?.avatarId?.thumbnailFileId || template?.organizationId?.avatarId?.imageId;
      return (imageId ? API.STREAM_ID.format(imageId) : BUILDING_ICON);
    }
    if (template?.type === CONSTANT.SYSTEM) {
      return (CLICKEE_AVATAR);
    }
    return (USER_ICON);
  }

  return <div className="template-item">
    <div className="template-item__preview">
      {/*<img*/}
      {/*  className="template-item__preview-inner"*/}
      {/*  src={API.STREAM_ID.format(templateData?.imageId?.thumbnailFileId)}*/}
      {/*  alt=""*/}
      {/*/>*/}
      
      <ImageView
        className="template-item__preview-inner"
        src={API.STREAM_ID.format(templateData?.imageId?.thumbnailFileId)}
        srcDefault={PROJECT_DEFAULT}
      />
      
      <div
        className="template-item__backdrop-hover"
        onClick={() => handleShowPreview(true, templateData)}
      >
        <div className="template-item__backdrop-preview">
          <Eye />
          {t("PREVIEW")}
        </div>
        <AntButton
          size="small"
          type={BUTTON.DEEP_NAVY}
          onClick={(e) => {
            e.stopPropagation();
            onUseTemplate(templateData);
          }}
        >
          {t("USE_TEMPLATE")}
        </AntButton>
      </div>
    </div>
    <div className="template-item__content">
      <div className="template-item__info">
        <div className="template-item__title">
          <img
            className={clsx("template-item__icon", { "image-icon": templateData?.type === CONSTANT.ORGANIZATION })}
            src={getSrcTemplateIcon(templateData)}
            alt=""
          />
          <Popover className="template-item__title_text" placement="topLeft" title={templateData?.name} trigger="hover">
            {templateData?.name}
          </Popover>
        </div>
        {isShowModified && <div className="template-item__last-modified">
          {`${t("UPDATE_AT")}: ${formatTimeDate(templateData?.updatedAt)}`}
        </div>}
      </div>

      {renderTemplateAction(templateData)}
    </div>
  </div>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(TemplateItem);