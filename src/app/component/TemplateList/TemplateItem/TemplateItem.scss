.template-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 8px;
  padding: 12px;
  gap: 14px;
  position: relative;

  &:not(.template-item__show-more) {
    background: var(--white);
    box-shadow: var(--shadow-level-2);
  }

  &.template-item__show-more {
    cursor: pointer;
    border: 1px solid var(--primary-colours-blue);
    display: flex;
    gap: 8px;
    flex-direction: row;
    align-items: center;
    place-content: center;
    font-weight: 600;
    color: var(--typo-colours-support-blue);
    user-select: none;

    svg path {
      stroke: var(--typo-colours-support-blue);
    }
  }

  &:hover {
    //background: var(--blue-light-2);
    //border-color: var(--background-light-background-blue);
  }

  .template-item__preview {
    cursor: pointer;
    position: relative;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 156 / 190;
    border: 1px solid var(--background-light-background-grey);

    &:not(:hover) .template-item__backdrop-hover {
      display: none;
    }

    .template-item__backdrop-hover {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(0, 0, 0, 0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      gap: 16px;
      z-index: 1;

      .template-item__backdrop-preview {
        color: var(--white);
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }


    .template-item__preview-inner {
      object-fit: cover;
      width: 100%;
      height: 100%;

      .image-view__image-default {
        img {
          height: 80px;
          width: unset;
        }
      }
    }
  }


  .template-item__content {
    display: flex;
    justify-content: space-between;
    width: 100%;
    gap: 8px;

    .template-item__info {
      display: flex;
      flex-direction: column;
      word-break: break-word;

      .template-item__title {
        display: flex;
        gap: 4px;

        .template-item__icon {
          display: flex;
          justify-content: center;
          align-self: center;
          width: 24px;
          height: 24px;
          border-radius: 50%;

          &.image-icon {
            border: 1px solid var(--support-colours-grey-light);
          }
        }

        .template-item__title_text {
          font-size: 16px;
          font-weight: 600;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          align-self: center;
        }
      }

      .template-item__last-modified {
        font-size: 10px;
        color: var(--typo-colours-support-blue-light);
      }

      .template-item__owner {
        font-size: 13px;
        color: var(--typo-colours-support-blue-light);
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }

  .template-item__actions {
    padding-top: 4px;
    width: 16px;
  }
}