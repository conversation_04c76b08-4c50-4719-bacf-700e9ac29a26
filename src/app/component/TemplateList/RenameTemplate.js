import React, { useEffect, useRef } from "react";
import { Form, Input } from "antd";
import { useTranslation } from "react-i18next";

import { AntForm } from "@component/AntForm";
import AntModal from "@component/AntModal";

import RULE from "@rule";


function RenameTemplate({ isOpen, handleCancel, handleOk, templateSelected }) {
  const { t } = useTranslation();
  const [renameTemplateForm] = Form.useForm();
  const renameInput = useRef(null);
  
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        renameInput.current.focus();
      }, 100);
      renameTemplateForm.resetFields();
      renameTemplateForm.setFieldsValue({ name: templateSelected?.name });
    }
  }, [isOpen]);

  return <AntModal
    onCancel={handleCancel}
    open={isOpen}
    okText={t("RENAME")}
    formId="form-rename-template"
    closeIcon={null}
  >
    <AntForm layout="vertical" form={renameTemplateForm} onFinish={handleOk} id="form-rename-template">
      <AntForm.Item
        label={<label className="!font-bold">
          {t("RENAME_TEMPLATE")}
        </label>}
        name="name"
        rules={[RULE.REQUIRED]}
      >
        <Input size="large" placeholder={t("PLEASE_INPUT_NAME_TEMPLATE")} allowClear ref={renameInput}></Input>
      </AntForm.Item>
    </AntForm>
  </AntModal>;
}

export default RenameTemplate;