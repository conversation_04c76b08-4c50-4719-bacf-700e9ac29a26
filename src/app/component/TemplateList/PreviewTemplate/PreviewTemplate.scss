.preview-template-container {

  @media screen and (max-height: 720px) {
    top: 24px !important;

    .modal-content {
      padding: 16px 32px;
    }
  }

  width: unset !important;
  max-width: 900px;

  .preview-template-header {
    display: flex;
    justify-content: space-between;
    color: var(--typo-colours-primary-black);
    align-items: center;
  }

  .preview-template__img {
    max-width: 100%;
    box-shadow: var(--shadow-level-2);
    border-radius: 8px;

    &.preview-template__img-error {
      display: none;
    }
  }
}