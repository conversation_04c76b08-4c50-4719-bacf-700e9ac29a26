import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import AntModal from "@component/AntModal";
import AntButton from "@component/AntButton";

import { BUTTON, CONSTANT } from "@constant";

import "./PreviewTemplate.scss";
import { API } from "@api";
import clsx from "clsx";
import NoData from "@component/NoData";

function PreviewTemplate({ isOpen, handleCancel, templateSelected, onUseTemplate, useTemplateLabel }) {
  const { t } = useTranslation();
  
  const [imageStatus, setImageStatus] = useState(undefined);
  
  useEffect(() => {
    if (!isOpen) setImageStatus(undefined);
  }, [isOpen]);
  
  function onLoad() {
    setImageStatus(CONSTANT.SUCCESS);
  }
  
  function onError() {
    setImageStatus(CONSTANT.ERROR);
  }

  if(!isOpen) return null;
  
  return <>
    <AntModal
      title={<div className="preview-template-header">
        {t('PREVIEW_TEMPLATE')}
        <AntButton
          size="large"
          type={BUTTON.DEEP_NAVY}
          onClick={() => onUseTemplate(templateSelected)}
        >
          {useTemplateLabel ||t("USE_TEMPLATE")}
        </AntButton>
      </div>}
      onCancel={handleCancel}
      open={isOpen}
      className="preview-template-container"
      okText={t("RENAME")}
      closeIcon={null}
      style={{ top: 40 }}
      //width={1112}
      footerless
    >
      <img
        className={clsx("preview-template__img", { "preview-template__img-error": imageStatus === CONSTANT.ERROR })}
        src={API.STREAM_ID.format(templateSelected?.imageId?.imageFileId)}
        alt=""
        onLoad={onLoad}
        onError={onError}
      />
      {imageStatus === CONSTANT.ERROR && <NoData />}
    </AntModal>
  </>;
}

export default PreviewTemplate;