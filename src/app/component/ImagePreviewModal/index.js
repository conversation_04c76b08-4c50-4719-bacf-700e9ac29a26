import React from "react";

import AntModal from "../AntModal";

import { API } from "@api";

import "./ImagePreviewModal.scss";

const ImagePreviewModal = ({ isOpen, handleCancel, imageFileId, ...props }) => {
  
  return <AntModal
    width={1280}
    className="image-preview-modal"
    open={isOpen}
    onCancel={handleCancel}
    footerless
    closeIcon={null}
  >
    <div id="js-image-preview-container" className="image-preview-container" onClick={handleCancel}>
      <div className="image-preview-inner">
        <img
          id="js-image-preview-img"
          src={API.STREAM_ID.format(imageFileId)}
          alt=""
          onClick={e => e.stopPropagation()}
        />
      </div>
    </div>
  </AntModal>;
};

export default ImagePreviewModal;