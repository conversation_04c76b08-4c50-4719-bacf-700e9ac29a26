import { Button, Dropdown } from 'antd';
import SPEAKING_PAUSE from '@src/asset/icon/pause/speaking-pause.svg';
import SPEAKING_PLAY from '@src/asset/icon/play/speaking-play.svg';
import VOLUME_ON from '@src/assets/icons/volume-on.svg';
import VOLUME_OFF from '@src/assets/icons/volume-off.svg';
import THREE_DOT_MENU from '@src/assets/icons/three-dot-menu.svg';
import DOWNLOAD from '@src/assets/icons/arrow-down.svg';
import PLAYBACK_SPEED from '@src/assets/icons/playback-speed.svg';
import { renderAudioDuration } from '@common/functionCommons';
import Slider from 'rc-slider';
import ReactPlayer from 'react-player';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import './AudioPlayer.scss';
import { API } from '@src/constants/api';

function AudioPlayer({
  audioFile,
  audioUrl,
  audioId,
  startTime = 0,
  endTime = 0,
  playbackSpeed = 1,
  autoPlay = false,
  onPlaybackSpeedChange,
  onProgress,
}) {
  const playerRef = useRef(null);
  const [isPlaying, setPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(startTime);
  const currentTimeRef = useRef(startTime);
  const [displayEndTime, setDisplayEndTime] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);
  const [audioSource, setAudioSource] = useState(null);
  const [isMuted, setIsMuted] = useState(false);
  const [speed, setSpeed] = useState(playbackSpeed);
  const { t } = useTranslation();

  useEffect(() => {
    setSpeed(playbackSpeed);
  }, [playbackSpeed]);

  useEffect(() => {
    setPlaying(autoPlay);
    setCurrentTime(startTime);
    currentTimeRef.current = startTime;
    playerRef.current?.seekTo(startTime);
    setDisplayEndTime((endTime || audioDuration) - startTime);
  }, [startTime, endTime]);

  useEffect(() => {
    clearData();

    if (audioFile) {
      setTimeout(() => {
        setAudioSource(URL.createObjectURL(audioFile));
      }, 100);
    } else if (audioId) {
      setAudioSource(API.STREAM_MEDIA.format(audioId));
    } else if (audioUrl) {
      setAudioSource(audioUrl);
    }

    return () => clearData();
  }, [audioFile, audioUrl, audioId]);

  function clearData() {
    setAudioSource(null);
    playerRef.current?.seekTo(startTime);
    setPlaying(false);
    setCurrentTime(startTime);
    currentTimeRef.current = startTime;
    setAudioDuration(0);
    setDisplayEndTime(0);
  }

  const convertProgressToDisplay = value => {
    return value * 100;
  };

  const convertDisplayToProgress = value => {
    return value / 100;
  };

  const handleSeek = useCallback(
    value => {
      if (!audioDuration) return;
      value = convertDisplayToProgress(value);

      // Giới hạn seek trong khoảng startTime và endTime
      const seekTime = Math.min(value + startTime, endTime || audioDuration);

      setCurrentTime(seekTime);
      currentTimeRef.current = seekTime;
      playerRef.current.seekTo(seekTime);
    },
    [audioDuration, startTime, endTime],
  );

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const handlePlaybackSpeedChange = speedValue => {
    setSpeed(speedValue);
    if (onPlaybackSpeedChange) {
      onPlaybackSpeedChange(speedValue);
    }
  };

  const handleDownload = () => {
    if (!audioSource) return;

    // Create an anchor element and trigger download
    const a = document.createElement('a');
    a.href = audioUrl || audioSource;
    a.setAttribute('download', audioFile?.name || audioId || 'audio-file.mp3'); // Use the file name if available
    a.download = 'audio-file.mp3'; // Default filename
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const getPlaybackSpeedMenuItems = () => {
    const speedOptions = [0.5, 0.75, 1, 1.5, 2];

    return speedOptions.map(speedValue => ({
      key: speedValue.toString(),
      label: <span className={`submenu-item-text ${speedValue === speed ? 'active' : ''}`}>{speedValue}x</span>,
      className: speedValue === speed ? 'speed-item-active' : '',
      onClick: () => handlePlaybackSpeedChange(speedValue),
    }));
  };

  const actionMenuItems = [
    {
      key: 'download',
      label: (
        <span className="menu-item-content">
          <span className="menu-item-icon">
            <img src={DOWNLOAD} alt="Download" />
          </span>
          <span className="menu-item-text">{t('DOWNLOAD')}</span>
        </span>
      ),
      onClick: handleDownload,
    },
    {
      key: 'playback-speed',
      label: (
        <span className="menu-item-content">
          <span className="menu-item-icon">
            <img src={PLAYBACK_SPEED} alt="Playback Speed" />
          </span>
          <span className="menu-item-text">{t('PLAYBACK_SPEED')}</span>
        </span>
      ),
      popupClassName: 'audio-player__action-submenu',
      children: getPlaybackSpeedMenuItems(),
    },
  ];

  return (
    <>
      <div className="audio-player">
        <div className="audio-player__play">
          <Button
            shape="circle"
            className="ant-btn-compact"
            icon={isPlaying ? <img src={SPEAKING_PAUSE} alt="" /> : <img src={SPEAKING_PLAY} alt="" />}
            onClick={() => {
              if (!audioDuration) return;
              if (!isPlaying && currentTimeRef.current !== startTime) {
                playerRef.current?.seekTo(startTime);
                currentTimeRef.current = startTime;
              }
              setPlaying(prevState => !prevState);
            }}
          />
        </div>

        <div className="audio-player__time">{renderAudioDuration(currentTime - startTime > 0 ? currentTime - startTime : 0)}</div>
        <div className="audio-player__seek-bar">
          <Slider
            min={0}
            max={convertProgressToDisplay(displayEndTime)}
            value={convertProgressToDisplay(currentTime - startTime)}
            onChange={handleSeek}
          />
        </div>
        <div className="audio-player__time">{renderAudioDuration(displayEndTime)}</div>
        <div className="audio-player__volume">
          <Button
            type="text"
            className="volume-button"
            icon={isMuted ? <img src={VOLUME_OFF} alt="Unmute" /> : <img src={VOLUME_ON} alt="Mute" />}
            onClick={toggleMute}
          />
        </div>
        <div className="audio-player__menu">
          <Dropdown
            menu={{
              items: actionMenuItems,
            }}
            placement="topRight"
            trigger={['click']}
            overlayClassName="audio-player__action-menu"
          >
            <Button type="text" className="menu-button" icon={<img src={THREE_DOT_MENU} alt="Menu" />} />
          </Dropdown>
        </div>
      </div>

      {audioSource && (
        <div style={{ display: 'none' }}>
          <ReactPlayer
            ref={playerRef}
            url={audioSource}
            playing={isPlaying}
            width="0"
            height="0"
            progressInterval={0}
            playbackRate={speed}
            muted={isMuted}
            onDuration={duration => {
              setAudioDuration(duration);
              playerRef.current.seekTo(startTime);
              setDisplayEndTime((endTime || duration) - startTime);

              if (autoPlay) {
                setTimeout(() => {
                  setPlaying(true);
                }, 500);
              }
            }}
            onProgress={({ playedSeconds }) => {
              if (endTime && playedSeconds >= endTime) {
                setPlaying(false);
              }
              setCurrentTime(playedSeconds);
              currentTimeRef.current = playedSeconds;
              onProgress && onProgress(playedSeconds - startTime);
            }}
            onEnded={() => {
              setCurrentTime(startTime);
              currentTimeRef.current = startTime;
              setPlaying(false);
              playerRef.current.seekTo(startTime);
              setDisplayEndTime((endTime || audioDuration) - startTime);
            }}
          />
        </div>
      )}
    </>
  );
}

export default AudioPlayer;
