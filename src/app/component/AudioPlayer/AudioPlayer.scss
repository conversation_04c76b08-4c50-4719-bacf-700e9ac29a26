.audio-player {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;

  height: 32px;
  background-color: #f6f8fa;
  border-radius: 16px;
  padding: 8px;

  .audio-player__play {
    height: 28px;

    .ant-btn {
      overflow: hidden;

      .ant-btn-icon img {
        width: 32px;
        height: 32px;
      }
    }
  }

  .audio-player__time {
    align-self: center;
  }

  .audio-player__seek-bar {
    flex: 1;
    align-self: center;
    padding: 0 4px;
  }

  .audio-player__volume {
    .volume-button {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      height: 20px;
      width: 20px;
      border: none;
      background: transparent;

      img {
        width: 20px;
        height: 20px;
      }
    }
  }

  .audio-player__menu {
    .menu-button {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      height: 20px;
      width: 20px;
      border: none;
      background: transparent;

      img {
        width: 20px;
        height: 20px;
      }
    }
  }
}

// Action menu dropdown styling
.audio-player__action-menu,
.audio-player__action-submenu {
  .ant-dropdown-menu {
    border-radius: 12px;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.16);
    padding: 0;
    overflow: hidden;
    background-color: #ffffff;
    min-width: 180px;
    width: 226px;
    gap: 0px !important;

    .menu-item-content {
      display: flex;
      align-items: center;
      width: 100%;

      .menu-item-icon {
        margin-right: 8px;
        display: flex;
        align-items: center;
      }

      .menu-item-text {
        flex: 1;
      }

      .menu-item-chevron {
        margin-left: auto;
        display: flex;
        align-items: center;
      }
    }

    .ant-dropdown-menu-item,
    .ant-dropdown-menu-submenu-title {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      font-family: 'Inter', sans-serif;
      font-weight: 500;
      font-size: 16px;
      line-height: 1.3;
      color: #000000;
      margin: 0;
      border-radius: 0;

      svg {
        color: #000000;
      }

      &:hover {
        background-color: #09196b;
        color: #ffffff;

        .menu-item-content {
          .menu-item-text {
            color: #ffffff;
          }

          img {
            filter: brightness(0) invert(1);
          }
        }

        svg {
          color: #ffffff;
        }

        .submenu-item-text {
          color: #ffffff;
        }
      }

      .ant-dropdown-menu-title-content {
        display: flex;
        align-items: center;
        width: 100%;
      }
    }

    .ant-dropdown-menu-submenu {
      .ant-dropdown-menu-submenu-title {
        display: flex;
        justify-content: space-between;
        padding-right: 16px;

        &:hover {
          .ant-dropdown-menu-submenu-arrow-icon {
            color: #ffffff;
          }
        }
      }
    }
  }
}

// Specific styling for submenu
.audio-player__action-submenu {
  .ant-dropdown-menu {
    border-radius: 12px;
    margin-top: 4px;

    .ant-dropdown-menu-item {
      padding: 10px 16px;

      &.speed-item-active {
        background-color: #09196b;

        .submenu-item-text {
          color: #ffffff;

          &.active {
            color: #ffffff;
          }
        }

        &:hover {
          background-color: #09196b;
        }
      }

      &:hover {
        background-color: #09196b;
        color: #ffffff;

        .submenu-item-text {
          color: #ffffff;
        }
      }

      .ant-dropdown-menu-title-content {
        display: flex;
        justify-content: flex-end;
        width: 100%;

        .submenu-item-text {
          text-align: right;
          font-family: 'Inter', sans-serif;
          font-weight: 500;
          font-size: 16px;
          line-height: 1.3;
          width: 100%;
        }
      }
    }
  }
}
