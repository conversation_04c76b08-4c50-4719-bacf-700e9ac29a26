:root {
  --time-stepper-color-disable: #858585;
}

.time-stepper {
  display: flex;

  .time-stepper-content {
    display: flex;
    flex-direction: row;
    padding-right: 23px;
    padding-left: 23px;
    height: 40px;

    border-radius: 8px;
    border: 1px solid var(--lighttheme-content-background-stroke);
    background: var(--lighttheme-content-background);

    .time-stepper-item {
      height: 20px;
      display: flex;
      align-items: center;
      align-self: center;

      &:not(:last-child):after {
        content: ':';
        user-select: none;
      }

      .time-stepper-item__input {
        height: 20px;
        display: flex;
        align-items: center;
        user-select: none;
        cursor: text;

        &.time-stepper-item__selected {
          background: var(--typo-colours-support-blue);
          color: var(--typo-colours-support-white);
        }
      }
    }
  }

  &.time-stepper-disabled {
    * {
      cursor: not-allowed !important;
    }

    .time-stepper-content {
      color: var(--time-stepper-color-disable);
      background: var(--background-2nd);
    }
  }
}