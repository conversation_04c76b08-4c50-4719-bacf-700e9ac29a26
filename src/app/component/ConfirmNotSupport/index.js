import React from "react";
import { createRoot } from "react-dom/client"; // Sử dụng createRoot
import { Checkbox, Form, Modal } from "antd";

import { useTranslation } from "react-i18next";

const ConfirmModal = ({ onOk, onCancel }) => {
  const { t } = useTranslation();
  const [formConfirm] = Form.useForm(); // Sử dụng hook trong component
  
  return (
    <Modal
      open
      title={t("CKEDITOR_WARNING")}
      onOk={() => {
        const values = formConfirm.getFieldsValue();
        onOk(!!values.agreement); // Gọi onOk với giá trị checkbox
      }}
      onCancel={onCancel}
      closeIcon={null}
      okButtonProps={{
        className: "ant-btn-lg ant-btn-deep-navy",
      }}
      
      cancelButtonProps={{
        className: "ant-btn-lg ant-btn-white",
      }}
    >
      <Form form={formConfirm}>
        <Form.Item name="agreement" valuePropName="checked">
          <Checkbox>
            {t("DO_NOT_SHOW_THIS_MESSAGE_AGAIN_IN_THIS_BROWSER")}
          </Checkbox>
        </Form.Item>
      </Form>
    </Modal>
  );
};


const confirmNotSupport = () => {
  return new Promise((resolve, reject) => {
    const div = document.createElement("div");
    document.body.appendChild(div);
    
    const root = createRoot(div); // Tạo root
    
    const handleOk = (value) => {
      resolve(value);
      root.unmount(); // Unmount component
      div.remove();
    };
    
    const handleCancel = () => {
      reject("Cancelled");
      root.unmount(); // Unmount component
      div.remove();
    };
    
    root.render( // Sử dụng root.render
      <ConfirmModal onOk={handleOk} onCancel={handleCancel} />,
    );
  });
};

export default confirmNotSupport