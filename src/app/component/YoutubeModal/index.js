import { useEffect, useMemo } from "react";

import AntModal from "../AntModal";

import { CONSTANT } from "@constant";

import { getYoutubeVideoId } from "@common/functionCommons";

import "./YoutubeModal.scss";

const YoutubeModal = ({ linkYoutube, isOpen, handleCancel, ...props }) => {
  
  const videoId = useMemo(() => {
    return getYoutubeVideoId(linkYoutube);
  }, [linkYoutube]);
  
  useEffect(() => {
    const iframe = document.getElementById(`js-ytb-iframe`);
    if (!iframe) return;
    if (isOpen) {
      iframe.contentWindow.postMessage("{\"event\":\"command\",\"func\":\"playVideo\",\"args\":\"\"}", "*");
    } else {
      iframe.contentWindow.postMessage("{\"event\":\"command\",\"func\":\"pauseVideo\",\"args\":\"\"}", "*");
    }
  }, [isOpen]);
  
  
  if (!linkYoutube) return null;
  return <AntModal
    width={1280}
    className="youtube-preview-modal"
    open={isOpen}
    onCancel={handleCancel}
    footerless
    closeIcon={null}
  >
    <div id="js-youtube-preview" className="youtube-preview-container">
      <iframe
        id="js-ytb-iframe"
        width="100%" height="100%"
        src={CONSTANT.YTB_EMBED_URL.format(videoId) + "&autoplay=1&enablejsapi=1"}
        allow="autoplay"
        //allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        allowFullScreen
        frameBorder="0"
      />
    </div>
  </AntModal>;
};

export default YoutubeModal;