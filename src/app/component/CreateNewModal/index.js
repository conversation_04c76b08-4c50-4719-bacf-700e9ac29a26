import { useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Form, Input, Select } from "antd";

import { AntForm } from "../AntForm";
import AntModal from "../AntModal";

import { CONSTANT, getGradingTypeOptions } from "@constant";
import RULE from "@rule";

const ModalCreateNew = (props) => {
  const { t } = useTranslation();
  const { open, onCancel, onSubmit, createType } = props;
  const [formCreateNew] = Form.useForm();

  useEffect(() => {
    if (open) formCreateNew.resetFields();
  }, [open]);

  const { inputName, inputLabel, inputPlaceholder, title } = useMemo(() => {
    if (createType === CONSTANT.FOLDER) {
      return ({
        title: t("CREATE_FOLDER"),
        inputName: "folderName",
        inputLabel: t("FOLDER_NAME"),
        inputPlaceholder: t("FOLDER_NAME_PLACEHOLDER")
      });
    }

    if (createType === CONSTANT.PROJECT_LESSON) {
      return ({
        title: t("CREATE_PROJECT_LESSON"),
        inputName: "projectName",
        inputLabel: t("LESSON_NAME"),
        inputPlaceholder: t("PROJECT_LESSON_NAME_PLACEHOLDER")
      });
    }

    return ({
      title: t("CREATE_PROJECT_GRADING"),
      inputName: "projectName",
      inputLabel: t("GRADING_NAME"),
      inputPlaceholder: t("GRADING_NAME_PLACEHOLDER")
    });
  }, [createType, t]);

  return <AntModal
    open={open}
    onCancel={onCancel}
    onOk={onSubmit}
    formId="form-create-new"
    okText={t("CREATE")}
    closable={false}
    title={title}
    alignFooter="right"
  >
    <AntForm form={formCreateNew} id={"form-create-new"} layout="vertical" onFinish={onSubmit} >
      <AntForm.Item
        name={inputName}
        label={inputLabel}
        rules={[
          () => ({
            validator(_, value) {
              if (value && value.trim()) {
                return Promise.resolve();
              }
              return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
            },
          }),
        ]}>
        <Input
          placeholder={inputPlaceholder}
          size="large"
        />
      </AntForm.Item>

      {createType === CONSTANT.PROJECT_GRADING_ASSIGNMENT &&
        <AntForm.Item
          name={"type"}
          rules={[RULE.REQUIRED]}
          label={t("GRADING_TYPE")}
        >
          <Select
            options={getGradingTypeOptions()}
            size="large"
            placeholder={t("SELECT_PROJECT_EXAM_TYPE")}
          />
        </AntForm.Item>
      }
    </AntForm>
  </AntModal>
};

export default ModalCreateNew;