import React from "react";
import { Pie } from "@ant-design/plots";
import { useTranslation } from "react-i18next";
import { CONSTANT } from "@constant";
import "./PieChart.scss";

function PieChart({ total = 100, usage = 0, width = 120, height = 120 }) {
  const colorNormal = "#2196F3";
  const colorWarning = "#FD9F12";
  const colorNotUsage = "#E8EAED";
  const percentageUsage = total === CONSTANT.UNLIMITED ? 0 : parseInt(((usage / parseInt(total)) * 100));
  const { t } = useTranslation();
  const data = [
    {
      type: t("USED"),
      value: parseInt(usage),
    },
    {
      type: t("REMANING"),
      value: total === CONSTANT.UNLIMITED ? 100 : parseInt(total) - usage,
    },
  ];

  const config = {
    data,
    width: width,
    height: height,
    angleField: "value",
    colorField: "type",
    innerRadius: 0.8,
    color: [percentageUsage >= 90 ? colorWarning : colorNormal, colorNotUsage],
    label: false,
    tooltip: total === CONSTANT.UNLIMITED ? false : true,
    statistic: {
      title: false,
      content: {
        content: `<span class="pie-chart-percentage">${total === CONSTANT.UNLIMITED ? "∞" : `${percentageUsage}%</br><span class="pie-chart-usage">${t("USED")}</span></span>`}`,
      },
    },
    legend: false,
  };

  return <Pie {...config} />;
}

export default PieChart;