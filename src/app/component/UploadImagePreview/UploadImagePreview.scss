.upload-image-preview {
  width: 86px;
  height: 86px;
  padding: 5px;
  border: 1px dashed #d9d9d9;
  flex-shrink: 0;

  &:hover {
    border-color: var(--primary-colours-blue-navy);
  }

  .upload-image-preview__inner {
    width: 100%;
    height: 100%;
    background: #FAFAFA;
    position: relative;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    &:not(:hover) {
      .upload-image-preview__backdrop {
        display: none;
      }
    }

    .upload-image-preview__backdrop {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, .4);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .upload-image-preview__upload {
      cursor: pointer;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      gap: 5px;
      font-size: 14px;
      user-select: none;

      svg path {
        stroke: #000000;
      }
    }
  }
}