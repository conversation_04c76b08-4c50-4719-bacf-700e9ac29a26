import React from "react";
import { useTranslation } from "react-i18next";
import { useDropzone } from "react-dropzone";

import AntButton from "@component/AntButton";

import { API } from "@api";
import { BUTTON } from "@constant";

import PlusIcon from "@component/SvgIcons/PlusIcon";
import Close from "@component/SvgIcons/Close";
import Reload from "@component/SvgIcons/Reload";

import "./UploadImagePreview.scss";

function UploadImagePreview({ loading, imageId, ...props }) {
  const { t } = useTranslation();
  
  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop: handleUpload,
    noClick: true,
    accept: {
      "image/jpg": [".jpg"],
      "image/jpeg": [".jpeg"],
      "image/png": [".png"],
    },
    multiple: false,
    //disabled,
  });
  
  function handleUpload(files) {
    if (!files?.[0]) return;
    props.onDrop(files[0]);
  }
  
  return <>
    
    <div {...getRootProps()} className="upload-image-preview">
      <input {...getInputProps()} />
      
      <div className="upload-image-preview__inner">
        {imageId
          ? <img src={API.STREAM_ID.format(imageId)} alt="" />
          : <div className="upload-image-preview__upload" onClick={open}>
            <PlusIcon />
            {t("UPLOAD")}
          </div>}
        
        
        {imageId && <div className="upload-image-preview__backdrop">
          <AntButton
            size="mini"
            type={BUTTON.LIGHT_NAVY}
            icon={<Reload />}
            onClick={open}
          />
          {!!props.onClear && <AntButton
            size="mini"
            type={BUTTON.LIGHT_RED}
            icon={<Close />}
            onClick={props.onClear}
          />}
        </div>}
      </div>
    </div>
  </>;
}

export default UploadImagePreview;