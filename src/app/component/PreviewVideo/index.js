import { useLayoutEffect } from "react";
import ReactPlayer from "react-player";

import AntModal from "../AntModal";
import { API } from "@api";

import "./PreviewVideo.scss";

const PreviewVideo = ({ videoFileId, isOpen, handleCancel, ...props }) => {
  
  useLayoutEffect(() => {
    const jsReactPlayer = document.getElementById("js-react-player");
    if (jsReactPlayer?.children?.[0]) {
      if (isOpen) {
        jsReactPlayer.children[0].play();
      } else {
        jsReactPlayer.children[0].pause();
      }
    }
  }, [isOpen]);
  
  return <AntModal
    width={1280}
    className="preview-video-modal"
    open={isOpen}
    onCancel={handleCancel}
    footerless
    closeIcon={null}
  >

    <div className="preview-video-container">
      <ReactPlayer
        id="js-react-player"
        url={API.STREAM_MEDIA.format(videoFileId)}
        controls
        playing
        width="100%"
        height="100%"
        progressInterval={0}
      />
    </div>
  </AntModal>;
};

export default PreviewVideo;