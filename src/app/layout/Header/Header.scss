#header {
  display: flex;
  justify-content: space-between;
  position: relative;
  box-shadow: var(--shadow-level-1);
  z-index: 1;

  .header-logo {
    display: flex;
    margin: 8px 24px;

    img {
      height: 56px;
    }
  }

  .header-left {
    display: flex;
    align-items: center;
    overflow: hidden;

    .header-toggle-menu {
      text-align: center;
      box-shadow: var(--shadow-level-1);
      cursor: pointer;
      user-select: none;
      width: 72px;
      height: 72px;

      .ant-btn-icon svg {
        height: 24px;
        width: 24px;
      }
    }

    .header-breadcrumb {
      margin-left: 24px;

      .ant-breadcrumb {
        ol {
          flex-wrap: nowrap;

          .ant-breadcrumb-separator {
            display: flex;
            align-items: center;

            img {
              width: 4px;
              height: 4px;
            }
          }
        }
      }

      .ant-breadcrumb-link {
        a {
          height: 20px;
        }
      }
    }
  }

  .header-right {
    display: flex;
    gap: 40px;
    margin-right: 24px;
  }
}