import { useMemo, useState } from "react";
import { connect } from "react-redux";
import { Drawer, Layout } from "antd";
import { Link, matchPath, useLocation } from "react-router-dom";

import useWindowDimensions from "@common/windowDimensions";

import HeaderBreadcrumb from "@app/layout/Header/HeaderBreadcrumb";
import HeaderAction from "@app/layout/Header/HeaderAction";

import { LINK } from "@link";

import SCBank_logo from "@src/asset/logo/SCBank-logo.svg";
import BARS_24 from "@src/asset/icon/bars/bars-24.svg";

import * as app from "@src/ducks/app.duck";

import "./Header.scss";
import clsx from "clsx";
import AntButton from "@component/AntButton";
import Bars24 from "@component/SvgIcons/Bars";
import { BUTTON, SCREEN_PC } from "@constant";

const Header = ({ isShowDrawerMenu, ...props }) => {
  const { pathname } = useLocation();
  const { width } = useWindowDimensions();

  const isHideBreadcrumb = useMemo(() => {
    return [LINK.SUBSCRIPTION, LINK.PAYMENT_ID.format(":id"), LINK.PAYMENT_VNPAY].some((item) => matchPath(item, pathname));
  }, [pathname]);

  const isShowMenuBars = useMemo(() => width < SCREEN_PC, [width]);

  function handleShowDrawer() {
    if (!isShowDrawerMenu) props.setShowDrawerMenu(true);
  }

  return (<>
      <Layout.Header id="header">
        <div className="header-left">
          {isShowMenuBars &&
            <AntButton
              className='header-toggle-menu'
              icon={<Bars24 />}
              onClick={handleShowDrawer}
              type={isShowDrawerMenu ? BUTTON.DEEP_NAVY : BUTTON.WHITE}
            />}
          {isHideBreadcrumb
            ? <Link to={LINK.HOMEPAGE} className="header-logo">
              <img src={SCBank_logo} alt="" />
            </Link>
            : <HeaderBreadcrumb />}
        </div>
        <div className="header-right">
          <HeaderAction />
        </div>
      </Layout.Header>
    </>
  );
};

function mapStateToProps(store) {
  const { isShowDrawerMenu } = store.app;
  return { isShowDrawerMenu };
}

const mapDispatchToProps = { ...app.actions };
export default connect(mapStateToProps, mapDispatchToProps)(Header);
