:root {
  --account-btn-color: var(--primary-colours-blue);
  --account-btn-bg-color: #FFF;
  --account-btn-border-color: #F1F1F1;

  --header-menu-bg-color: #FFFFFF;
  --header-menu-item-selected-bg-color: var(--primary-colours-blue);
  --header-menu-item-active-bg-color: #BABAFF;
}

[data-theme='dark'] {
  --account-btn-color: #FFF;
  --account-btn-bg-color: rgba(56, 53, 53, 0.50);
  --account-btn-border-color: #4F4F4F;


}

.header-action {
  display: flex;
  align-items: center;
  gap: 24px;

  >.ant-btn {
    box-shadow: var(--shadow-level-1)
  }

  .account-button-text{
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.language-menu {
  .ant-dropdown-menu-item {
    padding: 6px 24px !important;
  }
}