import { connect } from "react-redux";

import { THEME_TYPE } from "@constant";

import ThemeLight from "@component/SvgIcons/ThemeLight";
import ThemeDark from "@component/SvgIcons/ThemeDark";
import AntButton from "@component/AntButton";

import * as app from "@src/ducks/app.duck";


function ToggleTheme({ theme, ...props }) {
  
  return null;
  return <AntButton
    size="large"
    icon={theme === THEME_TYPE.DARK ? <ThemeLight /> : <ThemeDark />}
    onClick={props.toggleTheme}
  />;
}

function mapStateToProps(store) {
  const { theme } = store.app;
  return { theme };
}

const mapDispatchToProps = { ...app.actions };

export default connect(mapStateToProps, mapDispatchToProps)(ToggleTheme);