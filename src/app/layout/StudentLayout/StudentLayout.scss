:root {
  --clickee-title-medium-font-family: "Inter", Helvetica;
  --clickee-title-medium-font-size: 16px;
  --clickee-title-medium-font-style: normal;
  --clickee-title-medium-font-weight: 500;
  --clickee-title-medium-letter-spacing: 0px;
  --clickee-title-medium-line-height: 24px;
  --variable-collection-clickee-text-color-body: rgba(0, 0, 0, 1);
  --variable-collection-clickee-text-color-primary: rgba(9, 25, 107, 1);

}

.student-layout {
  height: 100vh;


  &.student-layout-has-aside {
    .student-layout__content {
      padding: 0 32px 32px 16px;
    }
  }


  .student-layout__content {
    padding: 0 16px 32px 16px;
  }

  .student-owllee {
    .toggle-owllee {
      width: 50px !important;
      height: 50px !important;
      right: 16px;
      bottom: 16px;

      .toggle-owllee__icon {
        &:not(.toggle-owllee__icon-close) {
          width: 50px !important;
          height: 50px !important;
        }
      }
    }
  }

}