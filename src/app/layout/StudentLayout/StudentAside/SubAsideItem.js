import clsx from "clsx";
import { useMemo } from "react";
import { Link, useLocation } from "react-router-dom";

const SubAsideItem = ({ title, linkTo }) => {
  const location = useLocation();
  const isActive = useMemo(() => {
    if (!linkTo) return false;
    return (location.pathname === linkTo);
  }, [location, linkTo]);

  return (
    <Link
      to={linkTo}
      className={clsx("studen-aside__sub-item", { "studen-aside__sub-item-active": isActive })}
      title={title}
    >
      <div className="sub-item__title">{title}</div>
    </Link>
  );
};

export default SubAsideItem;