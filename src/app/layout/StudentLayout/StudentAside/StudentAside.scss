.student-aside.ant-layout-sider {
  padding: 80px 16px 0 32px;
  overflow: hidden;

  .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .student-aside__body {
      display: flex;
      flex-direction: column;
      gap: 24px;
      align-items: center;
      transition: all 0.2s ease;

      .student-aside-item {
        border-radius: 12px;
        width: 100%;
        overflow: hidden;

        &.student-aside-item-has-sub-active,
        &.student-aside-item-active {
          background: #E7E5FF;
        }

        &.student-aside-item-has-sub-active {
          .student-aside-item__content {
            .student-aside-item__title {
              color: var(--primary-colours-blue-navy);
              font-weight: 700;
            }
          }
        }

        &.student-aside-item-active {
          .student-aside-item__title {
            line-height: 24px;
            color: #3A18CE;
            font-weight: 700;
          }
        }

        .student-aside-item__content {
          display: flex;
          padding: 12px 16px;
          gap: 8px;
          border-radius: 12px;
          font-size: 18px;
          overflow: hidden;
          white-space: nowrap;

          .student-aside-item__title {
            line-height: 24px;
            color: var(--navy);
          }
        }

        .student-aside__sub-item {
          display: flex;
          padding: 12px 16px 12px 48px;

          &.student-aside__sub-item-active {
            color: #3A18CE;
          }
        }
      }
    }

    .student-aside__footer {
      display: flex;
      flex-direction: column;
      gap: 40px;
      align-items: center;
      padding: 32px 0;

      .student-aside__video-tutorial {
        padding: 16px;
        gap: 8px;
        border-radius: 16px;
        background: linear-gradient(180deg, #3A18CE 0%, #8E74FF 100%);
        width: 184px;
        color: #FFFFFF;
        display: flex;
        flex-direction: column;

        .student-aside__video-tutorial__title {
          font-size: 22px;
          font-weight: 600;
          line-height: 30px;
        }

        .student-aside__video-tutorial__description {
          font-size: 14px;
          line-height: 20px;
        }

        .student-aside__video-tutorial__btn {
          padding: 8px 16px;
          border-radius: 12px;
          justify-self: center;

          span {
            font-size: 14px;
            font-weight: 600;
            line-height: 20px;
          }
        }
      }

      .student-aside__resize-button {
        padding: 6px 22px;
        background: none;
        border: none;
        height: 32px;
        width: 64px;

        &.closed {
          .ant-btn-icon {
            rotate: 180deg;
          }
        }
      }
    }
  }
}