import { useEffect, useMemo, useState } from "react";
import { Layout } from "antd";
import { connect, useDispatch } from "react-redux";
import { LINK } from "@link";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";

import AsideItem from "./AsideItem";
import HEADPHONE_ICON from "@src/asset/aside/headphone.svg";
import DoubleArrow from "@component/SvgIcons/DoubleArrow";
import USER_24 from "@src/asset/icon/user/student-avatar-24.svg";
import WALLET_ICON from "@src/asset/icon/wallet/wallet-white.svg";

import AntButton from "@src/app/component/AntButton";

import "./StudentAside.scss";

import { BUTTON } from "@constant";
import clsx from "clsx";
import { getVideoTutorial } from "@src/app/services/Settings";

function StudentAside({  ...props }) {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const isSettingPage = [LINK.ACCOUNT, LINK.EXCHANGE].includes(location.pathname);

  const [menuWidth, setMenuWidth] = useState(232);
  const [urlTutorial, setUrlTutorial] = useState('');
  const dispatch = useDispatch()

  useEffect(() => {

    getUrlTutorial();
  }, []);

  const getUrlTutorial = async () => {
    const response = await getVideoTutorial();
    if (response) {
      setUrlTutorial(response.tutorialUrl);
    }
  }

  const isCollapse = useMemo(() => {
    return menuWidth === 104;
  }, [menuWidth]);

  const onResizeMenu = () => {
    setMenuWidth(isCollapse ? 232 : 104);
  };

  const onOpenVideoTutorial = () => {
    window.open(urlTutorial, "_blank");
  }

  const asideItems = useMemo(() => {
    const items = [];

    // Always show courses
    items.push({
      linkTo: LINK.COURSES,
      title: t("COURSES_MENU"),
      img: HEADPHONE_ICON,
    });

    // Show account and exchange if on setting pages
    if (isSettingPage) {
      items.push(
        {
          linkTo: LINK.ACCOUNT,
          title: t("ACCOUNT"),
          img: USER_24,
        },
        // {
        //   linkTo: LINK.EXCHANGE,
        //   title: t("EXCHANGE"),
        //   img: WALLET_ICON,
        // }
      );
    }

    return items;
  }, [isSettingPage, i18n.language]);

  return <Layout.Sider className="student-aside" width={menuWidth}>
    <div className="student-aside__body">
      {asideItems.map((item, index) => <AsideItem key={index} {...item} showTitle={!isCollapse} />)}
    </div>

    {!isSettingPage && <div className="student-aside__footer">
      {/* {!isCollapse && urlTutorial && <div className="student-aside__video-tutorial">
        <div className="student-aside__video-tutorial__title">{t("VIDEO_TUTORIAL_TITLE")}</div>
        <div className="student-aside__video-tutorial__description">
          {t("VIDEO_TUTORIAL_DESCRIPTION")}
        </div>
        <AntButton
          size='small'
          type={BUTTON.WHITE_NAVY}
          className="student-aside__video-tutorial__btn"
          onClick={onOpenVideoTutorial}
        >
          {t("VIDEO_TUTORIAL")}
        </AntButton>
      </div>} */}

      <AntButton
        size="mini"
        className={clsx("student-aside__resize-button", { "closed": isCollapse })}
        icon={<DoubleArrow />}
        type={BUTTON.WHITE_NAVY}
        onClick={onResizeMenu}
      />
    </div>}

  </Layout.Sider>;
}

const mapStateToProps = (state) => ({
});

const mapDispatchToProps = {
};

export default (connect(mapStateToProps, mapDispatchToProps)(StudentAside));
