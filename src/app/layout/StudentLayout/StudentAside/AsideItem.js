import { useMemo } from "react";
import { Link, useLocation, matchPath } from "react-router-dom";
import clsx from "clsx";
import { LINK } from "@link";
import SubAsideItem from "./SubAsideItem";
import { useDispatch } from "react-redux";


function StudentAsideItem({ title, linkTo, img, imgActive, subAside, showTitle, ...props }) {
  const location = useLocation();
  const dispatch = useDispatch();

  const isActive = useMemo(() => {
    if (!linkTo) return false;
    return !!matchPath(linkTo, location.pathname);
  }, [location, linkTo]);

  const isSubActive = useMemo(() => {
    if (!subAside) return false;
    return subAside.some((item) => item.linkTo === location.pathname);
  }, [location, subAside]);

  const renderAsideContent = () => {
    return <>
      <div className="student-aside-item__content">
        <img src={img} alt="" />
        {showTitle && <div className="student-aside-item__title" style={{ wordWrap: 'break-word', wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', overflow: 'hidden' }}>
          {title}
        </div>}
      </div>
      {subAside?.map((sub, index) => <SubAsideItem key={index} title={sub.title} linkTo={sub.linkTo} />)}
    </>
  }

  return (<>{
    linkTo
      ? <Link
        to={linkTo}
        className={clsx("student-aside-item", { "student-aside-item-active": isActive })}
      // title={title}
      >
        {renderAsideContent()}
      </Link>
      : <div
        // title={title}
        className={clsx("student-aside-item", { "student-aside-item-has-sub-active": isSubActive })}>
        {renderAsideContent()}
      </div>
  }</>);
}

export default StudentAsideItem;
