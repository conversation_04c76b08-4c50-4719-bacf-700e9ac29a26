import { <PERSON>IN<PERSON> } from '@link';

const STUDENT_BREADCRUMB = [
  {
    path: LINK.COURSES,
    items: [{ lang: 'COURSES', url: LINK.COURSES }],
  },
  {
    path: LINK.ACCOUNT,
    items: [{ lang: 'ACCOUNT_SETTINGS', url: LINK.ACCOUNT }],
  },
  {
    path: LINK.EXCHANGE,
    items: [{ lang: 'EXCHANGE', url: LINK.EXCHANGE }],
  },
  {
    path: LINK.ROLE_PLAY_COURSE_DETAIL.format(":id"),
    items: [
      { lang: "COURSES", url: LINK.COURSES },
      { lang: "COURSE_DETAIL" }
    ],
  },
  {
    path: LINK.ROLE_PLAY_SESSION.format(":courseId", ":taskId"),
    items: [
      { lang: "COURSES", url: LINK.COURSES },
      { lang: "COURSE_DETAIL", url: LINK.ROLE_PLAY_COURSE_DETAIL.format(":courseId") },
      { lang: "SESSION" }
    ],
  },
  {
    path: LINK.ROLE_PLAY_RESULTS.format(":courseId", ":taskId"),
    items: [
      { lang: "COURSES", url: LINK.COURSES },
      { lang: "COURSE_DETAIL", url: LINK.ROLE_PLAY_COURSE_DETAIL.format(":courseId") },
      { lang: "RESULTS" }
    ],
  },
];

export default STUDENT_BREADCRUMB;
