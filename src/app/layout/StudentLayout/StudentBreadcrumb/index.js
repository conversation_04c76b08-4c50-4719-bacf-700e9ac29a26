import { useMemo } from "react";
import { Breadcrumb } from "antd";
import { Link, matchPath, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import STUDENT_BREADCRUMB from "./studentBreadcrumb";

import BREADCRUMB_ARROW from "@src/asset/icon/breadcrumb-arrow.svg";

import "./StudentBreadcrumb.scss";

const SEPARATOR = <img src={BREADCRUMB_ARROW} alt="" />;

function StudentBreadcrumb({ subTitle, ...props }) {
  const { t } = useTranslation();
  const { pathname } = useLocation();

  const breadcrumbItems = useMemo(() => {
    const breadcrumbList = [];

    // Check if STUDENT_BREADCRUMB is defined
    if (!STUDENT_BREADCRUMB || !Array.isArray(STUDENT_BREADCRUMB)) {
      return breadcrumbList;
    }

    const breadcrumb = STUDENT_BREADCRUMB.find(breadcrumb => {
      if (!breadcrumb || typeof breadcrumb.path !== 'string') {
        return false;
      }
      return matchPath(breadcrumb.path, pathname);
    });

    if (breadcrumb && breadcrumb.items) {
      const match = matchPath(breadcrumb.path, pathname);

      breadcrumb.items.forEach(item => {
        if (!item) return;

        let url = item.url;
        let title = item.lang ? t(item.lang) : '';

        // Handle dynamic URLs with parameters
        if (url && typeof url === 'string' && url.includes(':id') && match && match.params && match.params.id) {
          url = url.replace(':id', match.params.id);
        }

        if (url && typeof url === 'string' && url.includes(':courseId') && match && match.params && match.params.courseId) {
          url = url.replace(':courseId', match.params.courseId);
        }

        if (url && typeof url === 'string' && url.includes(':taskId') && match && match.params && match.params.taskId) {
          url = url.replace(':taskId', match.params.taskId);
        }

        if (title) {
          breadcrumbList.push({
            title: url ? <Link to={url}>{title}</Link> : title,
          });
        }
      });

      if (subTitle) {
        breadcrumbList.push({
          title: subTitle,
        });
      }
    }

    return breadcrumbList;
  }, [t, pathname, subTitle]);

  if (!breadcrumbItems.length) return null;

  return <div className="student-breadcrumb">
    <Breadcrumb
      separator={SEPARATOR}
      items={breadcrumbItems}
    />
  </div>;
}

function mapStateToProps(store) {
  return {};
}

export default connect(mapStateToProps)(StudentBreadcrumb);