import { useEffect, useMemo } from "react";
import { Link, matchPath, useLocation } from "react-router-dom";
import clsx from "clsx";
import { connect, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";

import { LINK } from "@link";

import "./HeaderStudentMenu.scss";


const HeaderStudentMenu = ({ ...props }) => {
  const location = useLocation();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  useEffect(() => {

  }, []);

  const studentMenu = useMemo(() => {
    return [
      { lang: 'COURSES_MENU', links: [LINK.COURSES] },
      // { lang: "PRICING", links: [LINK.PRICING] },
    ];
  }, []);

  return (
    <div className="header-student-menu">
      {studentMenu.map((item, index) => {
        const isActive = item.links.some((link) => !!matchPath(link, location.pathname));
        const linkTo = item.links[0];
        return (
          <Link
            to={linkTo}
            className={clsx("header-student-menu__item", { "header-student-menu__item-active": isActive })}
            key={index}
          >
            {t(item.lang)}
          </Link>
        );
      })}
    </div>
  );
};
const mapStateToProps = (state) => ({
});

const mapDispatchToProps = {
};

export default (connect(mapStateToProps, mapDispatchToProps)(HeaderStudentMenu));
