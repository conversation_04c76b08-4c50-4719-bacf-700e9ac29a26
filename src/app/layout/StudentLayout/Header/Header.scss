.student-header {
  display: flex;
  justify-content: center;
  // padding: 0 156px;
  background-color: #0c4da2;
  height: 56px;

  .student-header__left {
    display: flex;
    align-items: center;

    .student-header__logo {
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      img {
        // width: 140px;
        // height: 40px;
      }

      span {
        background: linear-gradient(to right, #93FFE6, #00E5B0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 13px;
        line-height: 16px;
        font-weight: 400;
      }
    }
  }

  .student-header__right {
    display: flex;
    align-items: center;
  }
}