import { connect } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Breadcrumb as AntBreadcrumb } from 'antd';
import { LINK } from '@link';

const Breadcrumb = ({ user }) => {
  const { t } = useTranslation();
  const location = useLocation();

  const getBreadcrumbItems = () => {
    const pathname = location.pathname;
    const items = [];

    // Home item
    if (user?.type === 'student') {
      items.push({
        title: t('COURSES'),
        href: LINK.COURSES
      });
    } else {
      items.push({
        title: t('ADMIN'),
        href: LINK.ADMIN.SETTING
      });
    }

    // Add specific page items based on current path
    if (pathname.includes('/admin/setting')) {
      items.push({ title: t('SETTINGS') });
    } else if (pathname.includes('/admin/role-play-course-statistics/')) {
      // Course statistics should show Course Management > Course Statistics
      items.push({ title: t('COURSE_MANAGEMENT'), href: LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT });
      items.push({ title: t('COURSE_STATISTICS') });
    } else if (pathname.includes('/admin/role-play-statistics-overview')) {
      items.push({ title: t('STATISTICS') });
    } else if (pathname.includes('/admin/role-play-course-management')) {
      items.push({ title: t('COURSE_MANAGEMENT') });
    } else if (pathname.includes('/admin/role-play-ai-persona-management')) {
      items.push({ title: t('AI_PERSONA_MANAGEMENT') });
    } else if (pathname.includes('/admin/role-play-instruction-management')) {
      items.push({ title: t('INSTRUCTION_MANAGEMENT') });
    } else if (pathname.includes('/student/exchange')) {
      items.push({ title: t('EXCHANGE') });
    } else if (pathname.includes('/account')) {
      items.push({ title: t('ACCOUNT_SETTINGS') });
    } else if (pathname.includes('/pricing')) {
      items.push({ title: t('PRICING') });
    } else if (pathname.includes('/payment')) {
      items.push({ title: t('PAYMENT') });
    }

    return items;
  };

  return (
    <div id="breadcrumb">
      <AntBreadcrumb items={getBreadcrumbItems()} />
    </div>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(Breadcrumb);
