@import "src/app/styles/scroll";

:root {
  --btn-previous-bg-color: var(--primary-colours-blue);
  --btn-previous-border-color: transparent;
}

[data-theme='dark'] {
  --btn-previous-bg-color: rgb(42, 40, 40);
  --btn-previous-border-color: #4F4F4F;
}

#aside {
  height: 100%;
  box-shadow: var(--shadow-level-1);
  z-index: 3;

  .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
    gap: 40px;

    .aside-header {
      padding: 16px 24px 0 24px;
      height: 72px;

      .aside-header__logo {
        height: 56px;
      }
    }

    .aside-divider {
      display: flex;
      padding: 16px 0;

      &:before {
        width: 100%;
        content: '';
        background: #D9D9D9;
        height: 1px;
      }
    }

    .aside-body {
      @extend .scrollbar;
      @extend .scrollbar-show;

      scrollbar-gutter: stable both-edges;
      display: flex;
      flex-direction: column;
      flex: 1;
      padding: 0 24px 24px 24px; // padding 24px scrollbar both-edges -> 18px

      .aside-item {
        display: flex;
        flex-direction: row;
        padding: 12px 16px;
        gap: 8px;
        border-radius: 8px;

        &.aside-item__active {
          box-shadow: var(--shadow-level-1);
          font-weight: 600;
        }

        .aside-item__icon {
          border-radius: 8px;
          box-shadow: var(--shadow-level-1);
        }

        .aside-item__title {
          align-self: center;
          color: #000000;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .aside-collapse {
        .ant-collapse-item {
          .ant-collapse-header {
            padding: 16px;

            .ant-collapse-header-text {
              font-weight: 700;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }

            .ant-collapse-expand-icon {
              display: flex;
              padding-inline-start: 4px;
              height: 24px;
              align-self: center;

              img {
                margin: -4px 0;
                transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              }

            }
          }

          &.ant-collapse-item-active {
            .ant-collapse-expand-icon img {
              rotate: -180deg;
            }
          }
        }

        .ant-collapse-content-box {
          display: flex;
          flex-direction: column;
          padding: 0;
        }

        // Admin aside group styles
        &.admin-aside-groups {
          .ant-collapse-item {
            border: none;

            .ant-collapse-header {
              padding: 12px 8px;
              align-items: center;

              &:hover {
                background-color: rgba(0, 0, 0, 0.04);
                border-radius: 8px;
              }

              .ant-collapse-expand-icon {
                color: rgba(0, 0, 0, 0.65);
              }
            }

            .ant-collapse-content {
              border-top: none;

              .ant-collapse-content-box {
                padding: 0 0 0 16px;
              }
            }
          }

          .group-header {
            display: flex;
            align-items: center;
            gap: 12px;

            .group-icon {
              font-size: 18px;
              color: rgba(0, 0, 0, 0.65);
            }

            .group-title {
              font-weight: 600;
              font-size: 14px;
            }
          }

          .group-items {
            display: flex;
            flex-direction: column;

            .aside-item {
              margin-bottom: 4px;

              &:hover {
                background-color: rgba(0, 0, 0, 0.04);
              }
            }
          }
        }

        .organization-collapse {
          .ant-collapse-header-text {
            display: flex !important;
            gap: 4px;

            .organization-avatar {
              display: flex;
              justify-content: center;
              align-self: center;
              width: 32px;
              height: 32px;
              border-radius: 50%;
              overflow: hidden;
              flex-shrink: 0;

              img {
                display: flex;
                align-self: center;
                max-width: 100%;
                max-height: 100%;
              }

              &.default-avatar{
                img{
                  height: 24px;
                  width: 24px;
                }
              }
            }

            .organization-name {
              font-weight: 700;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }
        }
      }

    }
  }
}

