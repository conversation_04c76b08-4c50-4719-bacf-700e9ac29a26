import { connect } from "react-redux";
import { useTranslation } from "react-i18next";

import { USER_TYPE } from "@constant";

import { updateUserPersona } from "@src/app/services/User";

import EDUCATOR_ICON from '@src/asset/icon/userType/educator.svg';
import LEARNER_ICON from '@src/asset/icon/userType/learner.svg';

import * as auth from "@src/ducks/auth.duck";

import './SelectUserType.scss';

const SelectUserType = ({ user, ...props }) => {
  const { t } = useTranslation();

  const onSelectUserType = async (type) => {
    const apiResponse = await updateUserPersona({ _id: user._id, type });
    if (apiResponse) {
      props.userLoaded({ ...user, type: apiResponse.type });
    }
  }
  return (
    <>
      <div className="auth-content__title">{t("USER_TYPE")}</div>
      <div className="select-user-type-list">
        <div className="select-user-type-item" onClick={() => onSelectUserType(USER_TYPE.TEACHER)}>
          <img src={EDUCATOR_ICON} alt='' />
          <div className='select-user-type-item__content'>
            <div className="select-user-type-item__content-title">{t("EDUCATOR")}</div>
            <div className='select-user-type-item__content-description'>
              {t("EDUCATOR_DESCRIPTION")}
            </div>
          </div>
        </div>
        <div className="select-user-type-item" onClick={() => onSelectUserType(USER_TYPE.STUDENT)}>
          <img src={LEARNER_ICON} alt='' />
          <div className='select-user-type-item__content'>
            <div className="select-user-type-item__content-title">{t("LEARNER")}</div>
            <div className='select-user-type-item__content-description'>
              {t("LEARNER_DESCRIPTION")}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = { ...auth.actions };

export default connect(mapStateToProps, mapDispatchToProps)(SelectUserType);