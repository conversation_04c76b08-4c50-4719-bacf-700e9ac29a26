.select-user-type-list {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .select-user-type-item {
    display: flex;
    padding: 24px;
    gap: 10px;
    border-radius: 16px;
    background: #FFFFFF;
    border: 1px solid #EFF2FF;
    box-shadow: 0px 4px 24px 0px #E7E5FF80;
    align-items: flex-start;
    cursor: pointer;

    .select-user-type-item__content {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .select-user-type-item__content-title {
        font-size: 22px;
        font-weight: 600;
        line-height: 30px;
        text-align: left;
        color: var(--primary-colours-blue-navy);
      }

      .select-user-type-item__content-description {
        font-weight: 400;
        line-height: 20px;
        text-align: left;
        color: #B3B3B3;
      }
    }

    &:active {
      background: var(--primary-colours-blue-navy);

      .select-user-type-item__content-title,
      .select-user-type-item__content-description {
        color: #FFFFFF;
      }
    }
  }
}