import { useEffect, useState, useMemo } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import dayjs from "dayjs";
import moment from "moment";
import { usePageViewTracker } from "@src/ga";

import { toast } from "@component/ToastProvider";

import AntButton from "@component/AntButton";
import DescriptionWorkspace from "./DescriptionWorkspace";
import { formatDate } from "@common/functionCommons";

import { BUTTON, CONSTANT, USER_TYPE, WORKSPACE_TYPE } from "@constant";
import { LINK } from "@link";

import { getOnePermissionID } from "@services/User";
import { updateWorkspace } from "@services/Workspace";
import { requestForgetPassword, updateInfoUser } from "@services/Auth";
import PieChartUsageAccount from "@app/pages/User/PieChartUsageAccount";
import RenameDescription from "@app/component/RenameDescription";

import ArrowUpRight from "@component/SvgIcons/ArrowUpRight";
import EditPen from "@src/app/component/SvgIcons/Edit/EditPen";

import * as auth from "@src/ducks/auth.duck";

import "./User.scss";
import ModalChangePassword from "./ModalChangePassword";

const User = ({  user, ...props }) => {
  usePageViewTracker("User");
  const { t } = useTranslation();

  const [limitPermissionUser, setLimitPermissionUser] = useState([]);
  const [isShowModalRename, setIsShowModalRename] = useState(false);
  const [isShowChangePassword, setShowChangePassword] = useState(false);

  const linkUpgrade = user?.type === USER_TYPE.STUDENT ? LINK.PRICING : LINK.SUBSCRIPTION;


  useEffect(() => {
    getPermissionUser();
  }, []);

  const [subscriptionName, isFree] = useMemo(() => {
    return [
      user?.subscription?.packageId?.name,
      user?.subscription?.packageId?.order === 1,
    ];
  }, [user]);

  const onChangePassword = async () => {
    if (user?.hasPassword) {
      onToggleChangePassword();
      return;
    }
    const apiResponse = await requestForgetPassword({ email: user.email });
    if (apiResponse) {
      toast.success(t("AUTH_MESSAGE_FORGOT_PASSWORD_SUCCESS").format(t('YOUR_EMAIL_ADDRESS')), { unique: true });
    }
  };


  const handleExpireDate = (date) => {
    const today = moment();
    const expirationDate = moment(date);
    return expirationDate.isBefore(today.subtract(3, "days"));
  };

  const submitNameUser = async (newUserName) => {
    const apiResponse = await updateInfoUser({ fullName: newUserName });
    if (apiResponse) {
      props.requestUser();
      toast.success("UPDATE_NAME_SUCCESS");
      setIsShowModalRename(false);
    }
  };

  const getPermissionUser = async () => {
    const apiResponse = await getOnePermissionID(user?._id);
    if (apiResponse) {
      setLimitPermissionUser(apiResponse);
    }
  };

  const onToggleChangePassword = () => {
    setShowChangePassword(pre => !pre);
  };

  return <div id="account">
    <div className="account__content">
      <div className="account__content__item account__content__item__name">
        <div className="account__content__item__name__left">
          <div className="item__label">{t("NAME")}</div>
          <div className="item__value">
            {user?.fullName}
          </div>
        </div>
        <div className="edit-icon" onClick={() => setIsShowModalRename(true)}>
          <EditPen />
        </div>
      </div>
      <div className="account__content__item">
        <div className="item__label">{t("EMAIL")}</div>
        <div className="item__value">{user?.email}</div>
      </div>
      <div className="account__content__item">
        <div className="item__label">{t("PASSWORD")}</div>
        <div className="item__value password">
          {user?.hasPassword && <div className="asterisks-password">***********</div>}
          <div className="link-change-password" onClick={onChangePassword}>
            {t(user?.hasPassword ? "CHANGE_PASSWORD" : "SET_PASSWORD")}
          </div>
        </div>
      </div>

    </div>
    <RenameDescription
      isShowModal={isShowModalRename}
      initialValue={user?.fullName}
      title={t('EDIT_NAME')}
      handleClose={() => setIsShowModalRename(!isShowModalRename)}
      handleSubmit={submitNameUser}
      required
    />
    <ModalChangePassword open={isShowChangePassword} onCancel={onToggleChangePassword} />

  </div>;
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {
  ...auth.actions,
};

export default connect(mapStateToProps, mapDispatchToProps)(User);
