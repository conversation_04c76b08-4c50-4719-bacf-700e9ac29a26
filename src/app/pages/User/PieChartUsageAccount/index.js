import React from "react";
import PropTypes from "prop-types";
import "./PieChartUsageAccount.scss";
import { Pie } from "@ant-design/plots";
import { useTranslation } from "react-i18next";
import { CONSTANT } from "@constant";


PieChartUsageAccount.defaultProps = {
  total: 100,
  usage: 0,
  width: 120,
  height: 120,
};

function PieChartUsageAccount({ total, usage, width, height }) {
  const colorNormal = "#2196F3";
  const colorWarning = "#FD9F12";
  const colorNotUsage = "#E8EAED";
  const percentageUsage = isNaN(total) ? 2 : parseInt(((usage / parseInt(total)) * 100));
  const { t } = useTranslation();
  const data = [
    {
      type: t("USED"),
      value: isNaN(total) ? 2 : parseInt(usage),
    },
    {
      type: t("REMANING"),
      value: isNaN(total) ? 100 : parseInt(total) - usage,
    },
  ];
  
  const config = {
    data,
    width: width,
    height: height,
    angleField: "value",
    colorField: "type",
    innerRadius: 0.8,
    color: [percentageUsage >= 90 ? colorWarning : colorNormal, colorNotUsage],
    label: false,
    tooltip: isNaN(total) ? false: true,
    statistic: {
      title: false,
      content: {
        content: `<span class="pie-chart-usage-account-percentage">${isNaN(total) ? "∞" : `${percentageUsage}%`}</br><span class="pie-chart-usage-account-usage">${t("USED")}</span></span>`,
      },
    },
    legend: false,
  };
  
  return <Pie {...config} />;
}

export default PieChartUsageAccount;