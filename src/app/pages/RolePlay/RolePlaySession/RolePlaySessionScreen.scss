.role-play-session-screen {
  padding: 4px 16px;
  margin-bottom: -32px;
  margin-left: -16px;
  margin-right: -16px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(100vw - 6px);
  min-height: calc(100vh - 56px);

  &__content-container {
    flex: 1;
    overflow-y: auto;
    width: 100%;
    max-width: 1200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    overflow-x: hidden;
  }

  &__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80vh;
    width: 100%;
  }

  &__start-task-button {
    width: 100%;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    border: none;

  }

  &__introduction {

    width: 744px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 16px;
    border: none;

    .ant-card-body {
      padding: 0;
    }

    .role-play-session-screen__header {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      width: 100%;
      border: none;

      .ant-btn {
        padding: 0;
        border: none;

        &:hover,
        &:focus {
          background: transparent !important;
          color: #0c4da2 !important;
        }
      }

      .role-play-session-screen__back-button {
        flex-shrink: 0;
        color: #0c4da2 !important;
        font-size: 14px;
        line-height: 18px;
        font-weight: 400;
        text-align: center;
      }
    }

    .role-play-session-screen__title {
      flex-grow: 1;
      text-align: start;
      margin: 0;
      font-size: 24px;
      line-height: 32px;
      font-weight: 600;
    }

    .time-progress-wrapper {
      display: flex;
      gap: 8px;
      align-items: center;
      margin: 8px 0 24px 0;
    }

    .course-time,
    .course-progress {
      margin: 0;
      font-family: Inter, sans-serif;
      font-size: 13px; // Smaller text for meta info
      line-height: 18px;
      font-weight: 400;
      display: flex;
      align-items: center;
      padding: 2px 8px;
      border-radius: 16px;
    }

    .course-time {
      background-color: #ddfff2;
      color: #17b177;
    }

    .course-progress {
      background-color: #e4dffe;
      color: #4d45be;
    }

    .select-btn {
      padding: 8px 24px;
      border-radius: 8px;
      font-size: 16px;
      line-height: 24px;
      font-weight: 400;
      border: none;
      cursor: pointer;
      color: #0c4da2;
      background-color: #dbf2ff;
      box-shadow: 0px 4px 8px 0px rgba(231, 229, 255, 0.16);

      &.active {
        font-weight: 600;
        color: #fff;
        background-color: #0c4da2;
      }
    }
  }

  &__session-list {
    max-width: 360px;
    width: 100%;

    display: flex;
    flex-direction: column;
    // gap: 16px;
    box-shadow: 0 4px 20px 0 #0000001a;
    border: none;
    border-radius: 16px;
    margin-top: 16px;

    &__wrapper {
      max-width: 360px;
      width: 100%;
      margin-top: 55px;
    }

    &__completed-status {
      width: 100%;
      box-shadow: 0 4px 20px 0 #0000001a;
      padding: 20px 0;
      border-radius: 16px;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .ant-typography {
      margin: 8px 0 0 0;
      font-weight: 700;
    }

    .ant-card-body {
      padding: 0 16px 20px 16px;

      .ant-list-item {
        padding: 8px 0;
        cursor: pointer;
      }

      .ant-list-item-meta {
        margin: 0;
        padding: 8px 8px;
        border-radius: 8px;

        &:hover {
          background-color: #efefef;
        }

        .ant-list-item-meta-content {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .ant-list-item-meta-title {
            margin: 0;
            font-size: 16px;
            font-weight: 600;

          }

          .ant-list-item-meta-description {
            line-height: 0.7rem;
            // .ant-space{
            //   gap:0;
            // }
            .ant-space-item {
              .ant-typography {
                margin: 0;
                font-size: 14px;
                font-weight: 400;
                line-height: 18px;
                color: #000;
              }

              .ant-tag {
                padding: 2px 8px;
                border-radius: 16px;
                border: none;
              }
            }
          }
        }
      }

      .ant-list-pagination {
        margin-top: 16px;
      }
    }
  }

  &__title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
  }

  &__task-info {
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
  }

  &__task-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
  }

  &__task-description {
    font-size: 14px;
    margin-bottom: 10px;
  }

  &__estimated-time {
    font-size: 14px;
    color: #666;
  }

  &__persona-info {
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f0f7ff;
    border-radius: 8px;
  }

  &__persona-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
  }

  &__persona-details {
    font-size: 14px;

    p {
      margin-bottom: 5px;
    }
  }

  &__warning {
    margin-bottom: 20px;
    padding: 10px;
    background-color: #fff9e6;
    border-left: 4px solid #ffc53d;
    border-radius: 4px;
  }

  &__start-button {
    display: block;
    margin: 0 auto;
    padding: 0 30px;
    height: 40px;
  }

  &__chat-with-AI {
    max-width: 1128px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 40px;
    padding: 40px;
    border-radius: 24px;
    background: linear-gradient(to top, #f2fafe, #fff);
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
  }

  &__chat-with-AI-footer {
    margin-top: 24px;
    max-width: 1128px;
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 40px;
    border-radius: 24px;
    gap: 50px;
    border: 1px solid #e2e8ef;
    background-color: #FFF;
  }

  &__task-selection-area {
    display: flex;
    flex-direction: row;
    gap: 24px;
    padding: 16px;
    align-items: flex-start;
    width: 100%;
    margin-bottom: 24px;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  &__task-selection-component-wrapper {
    flex: 3;
    min-width: 0;
  }

  &__completed-sessions-component-wrapper {
    flex: 1;
    min-width: 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: start;
    width: 100%;
  }

  &__course-info {
    display: flex;
    flex-direction: column;
    max-width: 900px;
    width: 100%;
    gap: 8px;
  }

  &__course-title {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 5px;
    background: linear-gradient(to right, #0c4da2, #9c3de4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    margin: 0;
  }

  &__date-time {
    padding: 2px 8px;
    border-radius: 16px;
    background-color: #ddfff2;
    color: #17b177;
    font-size: 13px;
    font-weight: 400;
    line-height: 18px;
  }

  &__pass-score {
    font-size: 14px;
    color: #666;
  }

  &__video-area {
    display: flex;
    max-width: 1048px;
    width: 100%;
    // margin-bottom: 30px;
    height: 380px;
    gap: 24px;
    justify-content: center;
    // box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
  }

  &__participant-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    // align-items: center;
    justify-content: flex-start;
    background-color: #fff;
    border-radius: 24px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
    position: relative;
    max-width: 50%;
  }

  &__participant-video {
    width: 100%;
    height: calc(100% - 90px);
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #000;
    border-radius: 0px;
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
  }

  &__participant-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__video-element {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__visualizer-wrapper {
    padding: 12px 24px;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  &__participant-name {
    margin-top: 0;
    font-size: 14px;
    font-weight: 500;
    color: #000;
    // text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__participant-role {
    margin-top: 0;
    font-size: 14px;
    font-weight: 400;
    color: #000;
    // text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__transcript {
    width: 100%;

    font-size: 16px;
    // text-align: center;
  }

  &__current-message {
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    color: #10315D;

    &.interrupted {
      color: #ff7875;
      opacity: 0.8;
    }
  }

  &__controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 0;
    background-color: #f3f5f9;
    border-radius: 24px;
    flex-direction: column;
    align-self: center;
    width: 100%;
    gap: 13px;
  }

  &__timer-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
  }

  &__timer {
    font-size: 24px;
    font-weight: 700;
    color: #333;
  }

  &__total-duration {
    font-size: 14px;
    font-weight: 500;
    color: #666;
  }

  &__mic-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__mic-button {
    margin-bottom: 5px;
    padding: 0;
    border: none;
    background: transparent;
    height: auto;
    border-radius: 20px;

    &:hover {
      background: transparent;
    }

    img {
      transition: all 0.3s ease;
      border-radius: 20px;
    }
  }

  &__mic-status {
    font-size: 12px;
    text-align: center;
    color: #666;
  }

  &__end-button {
    font-weight: 500;
    border-radius: 8px;
  }

  &__exit-button {
    padding: 8px 24px;
    border-radius: 8px;
    background-color: #0c4da2;
    font-weight: 600;
  }

  &__record-controls {
    position: fixed;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;

    &.recording {
      .role-play-session-screen__record-button {
        animation: pulse 1.5s infinite;
      }
    }
  }

  &__record-button {
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  &__reference-materials {
    padding: 16px 24px;
    display: flex;
    flex-direction: column;
    // gap: 16px;
    max-width: 756px;
    width: 100%;
    background-color: #f2faff;

    .reference-content {
      padding: 16px 24px;
      border-radius: 12px;
      background-color: #dbf2ff;
      border: 1px dashed #0c4da2;

      &__title {
        font-size: 18px;
        font-weight: 600;
        color: #0c4da2;
        font-family: 'Segoe UI';
      }

      &__close {
        padding: 2px 16px;
        border-radius: 16px;
        background-color: #0c4da2;
        color: #FFFFFF;
        font-size: 13px;
        line-height: 18px;
        font-weight: 400;
        border: none;
        cursor: pointer;
      }
    }
  }

  &__reference-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #0c4da2;
  }

  &__camera-option {
    .ant-switch-checked {
      background-color: #26d06d;

      &:hover {
        background-color: #26d06d !important;
      }
    }
  }

  &__device-note {
    color: #10315D;
    margin-top: 16px;
  }

  .session-introduction-layout {
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: start;
    gap: 24px;
    width: 100%;
    max-width: 1144px;

    > :first-child {
      flex: 1;
    }

    > :last-child {
      // width: 300px;
    }
  }
}

.role-play-session-screen__ai-processing-indicator {
  font-style: italic;
  color: #666;
  margin-top: 5px;
}

.role-play-session-screen__name-indicator {
  font-weight: 500;
  color: #000;
  margin-bottom: 8px;

  // .role-play-session-screen__audio-progress {
  //   margin-top: 8px;
  //   width: 200px;
  //   margin-left: auto;
  //   margin-right: auto;
  // }
}

.role-play-session-screen__ai-speaking-indicator {
  font-style: italic;
  color: #17b177;
  margin-top: 5px;
  animation: fadeInOut 2s ease-in-out;
}

.role-play-session-screen__ai-interrupted-indicator {
  font-style: italic;
  color: #ff7875;
  margin-top: 5px;
  animation: fadeInOut 2s ease-in-out;
}

.persona-card {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  border: 1px solid #dff3ff;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  background: linear-gradient(to bottom, #f2fafe, #fff);
  // -webkit-background-clip: text;
  // -webkit-text-fill-color: transparent;

  &__avatar-wrapper {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    padding: 1px;
    background: linear-gradient(to bottom, #0c4da2, #70d9ff);
    display: inline-block;

    .persona-card__avatar {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
      display: block;
    }
  }

  &__background {
    margin: 0;
    font-size: 16px;
    font-weight: 400;
    font-style: italic;
    font-family: 'Segoe UI';

  }

  &__name {
    color: #0c4da2;
    font-family: 'Segoe UI';
    font-weight: 600;
    font-size: 16px;
  }

  &__timer {
    padding: 2px 8px;
    border-radius: 16px;
    background-color: #ddfff2;
    color: #17b177;
    font-size: 13px;
    font-weight: 400;
    line-height: 18px;
  }

  &__completed {
    padding: 2px 8px;
    border-radius: 16px;
    background-color: #e4dffe;
    color: #4d45be;
    font-size: 13px;
    font-weight: 400;
    line-height: 18px;

    &.persona-card__unfinished {
      background-color: #ffefe0;
      color: #ff834d;
    }
  }

  &__concern {
    display: flex;
    flex-direction: column;
    gap: 8px;

    p {
      font-family: 'Segoe UI';
      font-size: 16px;

      text-align: center;
      margin-bottom: 0px;
    }

    strong {
      font-family: 'Segoe UI';
      font-size: 16px;

      text-align: center;
      margin-bottom: 0px;
    }

    span {
      font-family: 'Segoe UI';
      font-size: 16px;

      text-align: center;
      margin-bottom: 0px;
    }

  }

  &__start-button {
    padding: 8px 24px;
    border-radius: 8px;
    color: #fff;
    background-color: #0c4da2;
    font-size: 16px;
    line-height: 24px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    box-shadow: 0px 4px 8px rgba(231, 229, 255, 0.16);
  }
}

.ant-modal-wrap:has(.role-play-session-screen__device-selector-modal) {
  .ant-modal {
    .ant-modal-content {
      .ant-modal-close {
        margin-top: 10px;
        margin-right: 8px;
        color: #000;
      }

      .ant-modal-header {
        .ant-modal-title {
          color: #000;
          font-size: 22px;
          font-weight: 600;
          line-height: 30px;
        }
      }

      .ant-modal-footer {
        display: flex;
        justify-content: center;
      }
    }
  }
}

.modal-session-end {
  .ant-modal-header {
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 12px;
    padding-bottom: 12px;
    .ant-modal-title {
      color: #000;
      font-size: 22px;
      font-weight: 600;
      line-height: 30px;
    }
  }
  
  .ant-modal-body {
    font-weight: 400;
  }

  .ant-modal-footer {
    text-align: center;

    .ant-btn {
      border-radius: 8px;
    }

    .ant-btn-white {
      background-color: #dbf2ff;
      color: #0c4da2;
      border: none;
    }
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.7;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7);
  }

  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(255, 77, 79, 0);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
  }
}