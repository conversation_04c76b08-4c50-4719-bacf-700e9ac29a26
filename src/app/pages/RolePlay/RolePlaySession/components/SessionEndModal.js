import React from 'react';
import { Modal } from 'antd';
import { useTranslation } from 'react-i18next';
import AntButton from "@component/AntButton";
import { BUTTON } from '@constant';

const SessionEndModal = ({
  open,
  onCancel,
  onBackToTasks,
  onViewResults,
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      title={t('SESSION_ENDED_AI_TITLE', 'Kết thúc trò chuyện')}
      open={open}
      onCancel={onCancel}
      footer={[
        <AntButton key="back" type={BUTTON.TEXT} onClick={onBackToTasks}>
          {t('RETURN_TO_TASKS', 'Quay lại danh sách')}
        </AntButton>,
        <AntButton key="submit" type={BUTTON.DEEP_NAVY2} onClick={onViewResults}>
          {t('VIEW_RESULTS_', 'Xem kết quả')}
        </AntButton>,
      ]}
      closable={true}
      maskClosable={false}
      className='modal-session-end'
    >
      <p>{t('SESSION_COMPLETED_MESSAGE_', 'Bạn đã hoàn thành phiên role play. Bạn có thể xem kết quả hoặc quay lại danh sách nhiệm vụ.')}</p>
    </Modal>
  );
};

export default SessionEndModal;
