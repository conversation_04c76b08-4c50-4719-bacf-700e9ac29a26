import React from 'react';
import { But<PERSON>, Card, List, Space, Tag } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import DefaultPersonaImageUrl from '@src/asset/image/project-default.svg';
import AntButton from '@src/app/component/AntButton';

const TaskSelectionComponent = ({
  course,
  persona,
  tasksList,
  onSelectTask,
  onBack,
}) => {
  const { t } = useTranslation();

  return (
    <div className="role-play-session-screen__task-selection-container">
      <Card className="role-play-session-screen__tasks-list-container">
        <div className="role-play-session-screen__header">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={onBack}
            type="text"
            className="role-play-session-screen__back-button"
          >
            {t('BACK', 'Quay lại')}
          </Button>
          <h1 className="role-play-session-screen__title">{course?.name}</h1>
        </div>

        {persona && (
          <div className="role-play-session-screen__persona-info">
            <h3 className="role-play-session-screen__persona-name">
              {t('YOU_WILL_TALK_WITH', 'Bạn sẽ nói chuyện với')}: {persona?.name}
            </h3>
            <div className="role-play-session-screen__persona-image">
              <img
                src={persona?.avatar || DefaultPersonaImageUrl}
                alt={persona?.name}
                style={{ maxWidth: '100px', borderRadius: '50%' }}
              />
            </div>
            <div className="role-play-session-screen__persona-details">
              <p><strong>{t('ROLE', 'Vai trò')}:</strong> {persona?.role || 'N/A'}</p>
              <p><strong>{t('ORGANIZATION', 'Tổ chức')}:</strong> {persona?.organization || 'N/A'}</p>
            </div>
          </div>
        )}

        <h2 className="role-play-session-screen__tasks-title">
          {t('SELECT_TASK', 'Chọn nhiệm vụ để bắt đầu')}
        </h2>

        {tasksList.length === 0 ? (
          <div className="role-play-session-screen__no-tasks">
            {t('NO_TASKS_AVAILABLE', 'Không có nhiệm vụ nào cho khóa học này.')}
          </div>
        ) : (
          <List
            className="role-play-session-screen__tasks-list"
            itemLayout="vertical"
            dataSource={tasksList}
            renderItem={item => (
              <List.Item
                key={item._id}
                className="role-play-session-screen__task-item"
                actions={[
                  <AntButton
                    type="primary"
                    onClick={() => onSelectTask(item)}
                    className="role-play-session-screen__start-task-button"
                  >
                    {t('START_TASK', 'Bắt đầu')}
                  </AntButton>
                ]}
              >
                <List.Item.Meta
                  title={item.name}
                  description={
                    <Space direction="vertical">
                      <div>{item.description}</div>
                      <div className="role-play-session-screen__task-meta">
                        <Tag color="blue">
                          {t('ESTIMATED_TIME', 'Thời gian dự kiến')}: {item.estimated_duration || 'N/A'} {t('MINUTES', 'phút')}
                        </Tag>
                        {item.difficulty && (
                          <Tag color="orange">
                            {t('DIFFICULTY', 'Độ khó')}: {item.difficulty}
                          </Tag>
                        )}
                        {item.passScore && (
                          <Tag color="green">
                            {t('PASS_SCORE', 'Điểm đạt')}: {item.passScore}
                          </Tag>
                        )}
                      </div>
                    </Space>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </Card>
    </div>
  );
};

export default TaskSelectionComponent;
