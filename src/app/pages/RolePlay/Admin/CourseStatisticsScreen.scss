.course-statistics-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .statistics-header-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .statistics-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        flex: 1;

        .header-info {
          .statistics-title {
            margin: 0 0 4px 0;
            font-weight: 600;
            font-size: 24px;
          }

          .course-name {
            color: #666;
            font-size: 14px;
            margin: 0;
          }
        }
      }

      .header-right {
        .back-button {
          display: flex;
          align-items: center;
          gap: 8px;
          min-width: 120px;
          height: 40px;
          border-radius: 8px;
          font-weight: 500;
          border: 1px solid #d9d9d9 !important;
          background: linear-gradient(135deg, #f5f5f5 0%, #ffffff 100%);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
            border-color: #40a9ff !important;
            background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
          }

          &:focus {
            border-color: #1890ff !important;
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
          }

          &:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          }

          .anticon {
            font-size: 16px;
          }
        }
      }
    }
  }

  .statistics-filter-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .form-filter {
      .filter-form-item {
        margin-bottom: 0;
      }

      .filter-info {
        display: flex;
        align-items: center;
        height: 40px;
        padding-left: 8px;

        .filter-label {
          font-size: 14px;
          font-weight: 500;
          display: flex;
          align-items: center;
        }
      }

      .filter-buttons-col {
        .filter-buttons {
          display: flex;
          gap: 12px;
          justify-content: flex-end;

          .ant-btn {
            min-width: 120px;
            height: 42px;
            border-radius: 8px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            border: 1px solid #d9d9d9 !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
              opacity: 0;
              transition: opacity 0.3s ease;
            }

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
              border-color: #40a9ff !important;

              &::before {
                opacity: 1;
              }
            }

            &:focus {
              border-color: #1890ff !important;
              box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
            }

            &:active {
              transform: translateY(-1px);
              transition: all 0.1s ease;
            }

            // Specific styling for different button types
            &.ant-btn-default {
              background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
              color: #595959;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

              &:hover {
                background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
                color: #262626;
              }
            }

            &.ant-btn-primary {
              background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
              border-color: #1890ff !important;
              color: #fff;
              box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);

              &:hover {
                background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
                border-color: #096dd9 !important;
                box-shadow: 0 6px 16px rgba(24, 144, 255, 0.3);
              }
            }

            .anticon {
              font-size: 16px;
            }
          }
        }
      }

      // Custom DatePicker styling
      .ant-picker {
        height: 40px;
        border-radius: 6px;
        border: 1px solid #d9d9d9;

        &:hover {
          border-color: #40a9ff;
        }

        &:focus,
        &.ant-picker-focused {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .ant-picker-input > input {
          font-size: 14px;
        }
      }
    }
  }

  .statistics-overview-card,
  .completion-rate-card,
  .top-students-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-card-head-title {
      font-weight: 600;
      color: #1890ff;
    }
  }

  .completion-progress {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
  }

  .completion-details {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .detail-item {
      display: flex;
      align-items: center;
      padding: 20px;
      border-radius: 12px;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      border: 1px solid #e8e8e8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        border-color: currentColor;

        &::before {
          opacity: 1;
        }

        .detail-icon {
          transform: scale(1.1);
        }
      }

      .detail-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        font-size: 20px;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;
      }

      .detail-content {
        flex: 1;
        position: relative;
        z-index: 1;

        .detail-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 4px;
          font-weight: 500;
        }

        .detail-value {
          font-size: 24px;
          font-weight: 700;
          line-height: 1.2;
        }
      }

      // Enrolled Students - Blue theme
      &.enrolled-item {
        border-left: 4px solid #1890ff;
        color: #1890ff;

        .detail-icon {
          background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
          color: #1890ff;
        }

        .detail-value {
          color: #1890ff;
        }

        &:hover {
          background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
        }
      }

      // Completed Students - Green theme
      &.completed-item {
        border-left: 4px solid #52c41a;
        color: #52c41a;

        .detail-icon {
          background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
          color: #52c41a;
        }

        .detail-value {
          color: #52c41a;
        }

        &:hover {
          background: linear-gradient(135deg, #f9fff7 0%, #f6ffed 100%);
        }
      }

      // Completion Rate - Purple theme
      &.rate-item {
        border-left: 4px solid #722ed1;
        color: #722ed1;

        .detail-icon {
          background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
          color: #722ed1;
        }

        .detail-value {
          color: #722ed1;
        }

        &:hover {
          background: linear-gradient(135deg, #fafafa 0%, #f9f0ff 100%);
        }
      }
    }
  }

  .rank-cell {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .student-name {
    display: flex;
    align-items: center;
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 16px;

    .statistics-header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      .header-left {
        .header-info {
          text-align: center;

          .statistics-title {
            font-size: 20px;
          }
        }
      }

      .header-right {
        .back-button {
          width: 100%;
          justify-content: center;
          min-width: auto;
        }
      }
    }

    .statistics-filter-card {
      .form-filter {
        .filter-buttons-col {
          margin-top: 16px;

          .filter-buttons {
            justify-content: stretch;

            .ant-btn {
              flex: 1;
            }
          }
        }

        .filter-info {
          justify-content: center;
          margin-top: 8px;
        }
      }
    }

    .completion-details {
      padding: 16px;
      gap: 12px;

      .detail-item {
        padding: 16px;

        .detail-icon {
          width: 40px;
          height: 40px;
          font-size: 16px;
          margin-right: 12px;
        }

        .detail-content {
          .detail-label {
            font-size: 13px;
          }

          .detail-value {
            font-size: 20px;
          }
        }
      }
    }
  }

  @media (max-width: 576px) {
    .statistics-filter-card {
      .form-filter {
        .filter-buttons {
          flex-direction: column;
          gap: 8px;
        }
      }
    }

    .completion-details {
      padding: 12px;
      gap: 10px;

      .detail-item {
        padding: 12px;
        border-radius: 8px;

        .detail-icon {
          width: 36px;
          height: 36px;
          font-size: 14px;
          margin-right: 10px;
        }

        .detail-content {
          .detail-label {
            font-size: 12px;
          }

          .detail-value {
            font-size: 18px;
          }
        }
      }
    }
  }

  // Table customization
  .ant-table {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }
  }

  // Statistics cards
  .ant-statistic {
    .ant-statistic-title {
      font-weight: 500;
      margin-bottom: 8px;
    }

    .ant-statistic-content {
      font-weight: 600;
    }
  }

  // Progress circle customization
  .ant-progress-circle {
    .ant-progress-text {
      font-weight: 600;
      font-size: 24px;
    }
  }

  // Tag customization
  .ant-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
  }
}
