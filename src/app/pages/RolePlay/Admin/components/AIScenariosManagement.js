import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Button, Space, Typography, Empty, Spin, message, Modal } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, StarOutlined, StarFilled } from '@ant-design/icons';

import { BUTTON } from '@constant';
import AntButton from '@component/AntButton';
import { toast } from '@component/ToastProvider';

import { 
  getScenariosByCourse, 
  deleteAIScenario, 
  setScenarioAsDefault 
} from '@services/RolePlay/AIScenarioService';

import AIScenarioForm from './AIScenarioForm';

import './AIScenariosManagement.scss';

const { Title, Text } = Typography;

const AIScenariosManagement = ({ courseId, onScenariosChange }) => {
  const { t } = useTranslation();
  const [scenarios, setScenarios] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [editingScenario, setEditingScenario] = useState(null);

  useEffect(() => {
    if (courseId) {
      fetchScenarios();
    }
  }, [courseId]);

  const fetchScenarios = async () => {
    setLoading(true);
    try {
      const response = await getScenariosByCourse(courseId, ['aiPersonaId', 'roleplayInstructionId', 'taskIds'], false);
      if (response) {
        setScenarios(response);
        if (onScenariosChange) {
          onScenariosChange(response);
        }
      }
    } catch (error) {
      console.error('Error fetching scenarios:', error);
      toast.error(t('ERROR_FETCHING_SCENARIOS'));
    } finally {
      setLoading(false);
    }
  };

  const handleCreateScenario = () => {
    setEditingScenario(null);
    setIsFormVisible(true);
  };

  const handleEditScenario = (scenario) => {
    setEditingScenario(scenario);
    setIsFormVisible(true);
  };

  const handleDeleteScenario = (scenario) => {
    Modal.confirm({
      title: t('CONFIRM_DELETE_SCENARIO'),
      content: t('DELETE_SCENARIO_CONFIRMATION', { name: scenario.name }),
      okText: t('DELETE'),
      cancelText: t('CANCEL'),
      okType: 'danger',
      onOk: async () => {
        try {
          await deleteAIScenario(scenario._id, false);
          toast.success(t('DELETE_SCENARIO_SUCCESS'));
          fetchScenarios();
        } catch (error) {
          console.error('Error deleting scenario:', error);
          toast.error(t('DELETE_SCENARIO_ERROR'));
        }
      },
    });
  };

  const handleSetDefault = async (scenario) => {
    try {
      await setScenarioAsDefault(scenario._id, false);
      toast.success(t('SET_DEFAULT_SCENARIO_SUCCESS'));
      fetchScenarios();
    } catch (error) {
      console.error('Error setting default scenario:', error);
      toast.error(t('SET_DEFAULT_SCENARIO_ERROR'));
    }
  };

  const handleFormSuccess = () => {
    setIsFormVisible(false);
    setEditingScenario(null);
    fetchScenarios();
  };

  const handleFormCancel = () => {
    setIsFormVisible(false);
    setEditingScenario(null);
  };

  const renderScenarioCard = (scenario) => {
    const isDefault = scenario.isDefault;
    
    return (
      <Card
        key={scenario._id}
        className={`scenario-card ${isDefault ? 'scenario-card--default' : ''}`}
        size="small"
        title={
          <div className="scenario-card__header">
            <div className="scenario-card__title">
              {isDefault && <StarFilled className="scenario-card__default-icon" />}
              <span>{scenario.name}</span>
            </div>
            {isDefault && (
              <span className="scenario-card__default-badge">
                {t('DEFAULT')}
              </span>
            )}
          </div>
        }
        extra={
          <Space>
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditScenario(scenario)}
            />
            {!isDefault && (
              <Button
                type="text"
                size="small"
                icon={<StarOutlined />}
                onClick={() => handleSetDefault(scenario)}
                title={t('SET_AS_DEFAULT')}
              />
            )}
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteScenario(scenario)}
              disabled={isDefault}
              title={isDefault ? t('CANNOT_DELETE_DEFAULT_SCENARIO') : t('DELETE_SCENARIO')}
            />
          </Space>
        }
      >
        <div className="scenario-card__content">
          {scenario.description && (
            <Text type="secondary" className="scenario-card__description">
              {scenario.description}
            </Text>
          )}
          
          <div className="scenario-card__details">
            {scenario.aiPersonaId && (
              <div className="scenario-card__detail">
                <Text strong>{t('AI_PERSONA')}: </Text>
                <Text>{scenario.aiPersonaId.name}</Text>
              </div>
            )}
            
            {scenario.taskIds && scenario.taskIds.length > 0 && (
              <div className="scenario-card__detail">
                <Text strong>{t('TASKS')}: </Text>
                <Text>{scenario.taskIds.length} {t('TASKS_COUNT')}</Text>
              </div>
            )}
            
            {scenario.estimatedCallTimeInMinutes && (
              <div className="scenario-card__detail">
                <Text strong>{t('ESTIMATED_TIME')}: </Text>
                <Text>{scenario.estimatedCallTimeInMinutes} {t('MINUTES')}</Text>
              </div>
            )}
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className="ai-scenarios-management">
      <div className="ai-scenarios-management__header">
        <Title level={4}>{t('AI_SCENARIOS')}</Title>
        <AntButton
          type={BUTTON.DEEP_NAVY}
          icon={<PlusOutlined />}
          onClick={handleCreateScenario}
        >
          {t('ADD_SCENARIO')}
        </AntButton>
      </div>

      <div className="ai-scenarios-management__content">
        {loading ? (
          <div className="ai-scenarios-management__loading">
            <Spin size="large" />
          </div>
        ) : scenarios.length === 0 ? (
          <Empty
            description={t('NO_SCENARIOS_FOUND')}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <AntButton
              type={BUTTON.DEEP_NAVY}
              icon={<PlusOutlined />}
              onClick={handleCreateScenario}
            >
              {t('CREATE_FIRST_SCENARIO')}
            </AntButton>
          </Empty>
        ) : (
          <div className="ai-scenarios-management__grid">
            {scenarios.map(renderScenarioCard)}
          </div>
        )}
      </div>

      {/* AI Scenario Form Modal */}
      <AIScenarioForm
        visible={isFormVisible}
        courseId={courseId}
        scenario={editingScenario}
        onSuccess={handleFormSuccess}
        onCancel={handleFormCancel}
      />
    </div>
  );
};

export default AIScenariosManagement;
