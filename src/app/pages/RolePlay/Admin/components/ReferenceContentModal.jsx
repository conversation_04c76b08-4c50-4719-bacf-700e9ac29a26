import React from 'react';
import { Modal } from 'antd';
import { useTranslation } from 'react-i18next';
import ReactMarkdown from 'react-markdown';
import AntButton from '@component/AntButton';
import { BUTTON } from '@constant';

import './ReferenceContentModal.scss';

const ReferenceContentModal = ({ isOpen, onClose, reference }) => {
  const { t } = useTranslation();

  return (
    <Modal
      title={reference?.name || t('REFERENCE_CONTENT')}
      open={isOpen}
      onCancel={onClose}
      width={800}
      footer={[
        <AntButton
          key="close"
          size={'large'}
          type={BUTTON.DEEP_NAVY}
          onClick={onClose}
        >
          {t('CLOSE')}
        </AntButton>,
      ]}
    >
      <div className="reference-content-container">
        {reference?.content ? (
          <ReactMarkdown>
            {reference.content}
          </ReactMarkdown>
        ) : (
          <div className="no-content-message">{t('NO_CONTENT_AVAILABLE')}</div>
        )}
      </div>
    </Modal>
  );
};

export default ReferenceContentModal;
