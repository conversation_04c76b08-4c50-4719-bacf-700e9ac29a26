.ai-scenarios-management {
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    h4 {
      margin: 0;
    }
  }

  &__content {
    min-height: 200px;
  }

  &__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 16px;
  }
}

.scenario-card {
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &--default {
    border: 2px solid #1890ff;
    background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  &__title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
  }

  &__default-icon {
    color: #1890ff;
    font-size: 16px;
  }

  &__default-badge {
    background: #1890ff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }

  &__content {
    padding-top: 8px;
  }

  &__description {
    display: block;
    margin-bottom: 12px;
    line-height: 1.4;
  }

  &__details {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  &__detail {
    display: flex;
    align-items: center;
    font-size: 13px;
    
    .ant-typography {
      margin: 0;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .ai-scenarios-management {
    &__header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
      
      h4 {
        text-align: center;
      }
    }

    &__grid {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 480px) {
  .scenario-card {
    &__header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    &__title {
      width: 100%;
    }

    &__default-badge {
      align-self: flex-start;
    }
  }
}
