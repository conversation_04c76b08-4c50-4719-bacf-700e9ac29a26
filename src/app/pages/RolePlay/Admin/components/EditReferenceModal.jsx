import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Checkbox, Button } from 'antd';
import { useTranslation } from 'react-i18next';
import { toast } from '@component/ToastProvider';
import { updateReference } from '@services/RolePlay/ReferenceService';
import AntButton from '@component/AntButton';
import { BUTTON } from '@constant';

const EditReferenceModal = ({ isOpen, onClose, reference, onReferenceUpdated }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen && reference) {
      form.setFieldsValue({
        name: reference.name,
        isPublic: reference.isPublic || false,
      });
    }
  }, [isOpen, reference, form]);

  const handleSubmit = async (values) => {
    if (!reference) return;

    setIsLoading(true);
    try {
      const updateData = {
        _id: reference._id,
        name: values.name,
        isPublic: values.isPublic,
      };

      const result = await updateReference(updateData);

      if (result) {
        toast.success(t('UPDATE_REFERENCE_SUCCESS', 'Cập nhật tài liệu tham khảo thành công'));
        onReferenceUpdated({ ...reference, ...updateData });
      } else {
        toast.error(t('UPDATE_REFERENCE_FAILED', 'Cập nhật tài liệu tham khảo thất bại'));
      }
    } catch (error) {
      console.error('Error updating reference:', error);
      toast.error(t('AN_ERROR_OCCURRED', 'Đã xảy ra lỗi'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      title={t('EDIT_REFERENCE', 'Chỉnh sửa tài liệu tham khảo')}
      open={isOpen}
      onCancel={handleCancel}
      width={600}
      footer={[
        <AntButton
          key="cancel"
          size="large"
          onClick={handleCancel}
          disabled={isLoading}
        >
          {t('CANCEL', 'Hủy')}
        </AntButton>,
        <AntButton
          key="submit"
          size="large"
          type={BUTTON.DEEP_NAVY}
          onClick={() => form.submit()}
          loading={isLoading}
        >
          {t('SAVE_CHANGES', 'Lưu thay đổi')}
        </AntButton>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          name="name"
          label={t('REFERENCE_NAME', 'Tên tài liệu')}
          rules={[
            { required: true, message: t('REFERENCE_NAME_REQUIRED', 'Vui lòng nhập tên tài liệu') },
            { max: 255, message: t('REFERENCE_NAME_TOO_LONG', 'Tên tài liệu không được vượt quá 255 ký tự') }
          ]}
        >
          <Input
            size="large"
            placeholder={t('ENTER_REFERENCE_NAME', 'Nhập tên tài liệu')}
          />
        </Form.Item>

        <Form.Item
          name="isPublic"
          valuePropName="checked"
        >
          <Checkbox>
            {t('MAKE_REFERENCE_PUBLIC')}
          </Checkbox>
        </Form.Item>

        <div className="reference-info" style={{ marginTop: '16px', padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
          <p><strong>{t('REFERENCE_TYPE', 'Loại tài liệu')}:</strong> {reference?.type}</p>
          {reference?.url && (
            <p><strong>{t('URL')}:</strong> <a href={reference.url} target="_blank" rel="noopener noreferrer">{reference.url}</a></p>
          )}
          <p><strong>{t('CREATED_AT', 'Ngày tạo')}:</strong> {reference?.createdAt ? new Date(reference.createdAt).toLocaleDateString() : ''}</p>
        </div>
      </Form>
    </Modal>
  );
};

export default EditReferenceModal;
