.ai-persona-detail-container {
  .ai-persona-detail-header-card {
    margin-bottom: 24px;

    .ai-persona-detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .ai-persona-detail-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }

      .ai-persona-detail-description {
        margin: 4px 0 0;
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }

  .ai-persona-detail-form-card {
    // Overwrite Ant Form Card default padding if necessary
    // .ant-card-body {
    //   padding: 24px;
    // }

    .ant-card-head-title {
      font-weight: 600;
    }

    // Style for UploadImage component if needed
    .ant-upload-picture-card-wrapper {
      // display: inline-block;
    }
    .ant-upload.ant-upload-select-picture-card {
      width: 104px; // default antd size
      height: 104px;
      margin-right: 8px;
      margin-bottom: 8px;
      background-color: #fafafa;
      border: 1px dashed #d9d9d9;
      border-radius: 2px;
      cursor: pointer;
      transition: border-color 0.3s;
      &:hover {
        border-color: #1890ff;
      }
      .ant-upload-disabled {
        cursor: not-allowed;
      }
    }
    .ant-upload-list-picture-card .ant-upload-list-item {
        width: 104px;
        height: 104px;
        padding: 0; // remove default padding from item
    }

    .ant-upload-list-item-info {
        // To make sure image covers the item area
        > span {
            display: block;
            width: 100%;
            height: 100%;
        }
        img {
            object-fit: cover;
        }
    }

  }

  .btn-cancel {
    // Custom styles if needed
  }

  .btn-save {
    min-width: 100px;
  }
}
