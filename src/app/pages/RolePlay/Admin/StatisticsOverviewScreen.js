import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  DatePicker,
  Button as AntButton,
  Space,
  Typography,
  Progress,
  Tag,
  List,
  Avatar,
  Form as AntForm,
  message
} from "antd";
import {
  CalendarOutlined,
  UserOutlined,
  TrophyOutlined,
  BookOutlined,
  StarOutlined,
  PlayCircleOutlined,
  PercentageOutlined,
  ReloadOutlined,
  FilterOutlined
} from "@ant-design/icons";
import dayjs from "dayjs";

import Loading from "@app/component/Loading";
import { BUTTON } from "@constant";
import { getOverallStatistics } from "@services/RolePlay";

import "./StatisticsOverviewScreen.scss";

const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

const StatisticsOverviewScreen = ({ user }) => {
  const { t } = useTranslation();

  const [isLoading, setIsLoading] = useState(false);
  const [statisticsData, setStatisticsData] = useState(null);
  const [dateRange, setDateRange] = useState([
    dayjs().subtract(30, 'day'),
    dayjs()
  ]);
  const [formFilter] = AntForm.useForm();

  // Fetch overall platform statistics
  const fetchOverallStatistics = async () => {
    setIsLoading(true);
    try {
      const params = {
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD')
      };

      const data = await getOverallStatistics(params);
      setStatisticsData(data);
    } catch (error) {
      console.error("Error fetching overall statistics:", error);
      message.error(t("FETCH_STATISTICS_ERROR") || "Có lỗi xảy ra khi tải dữ liệu thống kê");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOverallStatistics();
  }, [dateRange]);

  const handleDateRangeChange = (dates) => {
    if (dates && dates.length === 2) {
      setDateRange(dates);
      formFilter.setFieldsValue({
        dateRange: dates
      });
    }
  };

  const handleRefresh = () => {
    fetchOverallStatistics();
  };

  const onSubmitFilter = (values) => {
    if (values.dateRange && values.dateRange.length === 2) {
      setDateRange(values.dateRange);
    }
  };

  const onClearFilter = () => {
    const defaultRange = [dayjs().subtract(30, 'day'), dayjs()];
    setDateRange(defaultRange);
    formFilter.setFieldsValue({
      dateRange: defaultRange
    });
  };

  // Initialize form with current date range
  useEffect(() => {
    formFilter.setFieldsValue({
      dateRange: dateRange
    });
  }, []);

  // Popular courses table columns
  const popularCoursesColumns = [
    {
      title: t("RANK"),
      width: 80,
      align: "center",
      render: (_, __, index) => (
        <div className="rank-cell">
          {index === 0 && <TrophyOutlined style={{ color: '#FFD700', marginRight: 4 }} />}
          {index === 1 && <TrophyOutlined style={{ color: '#C0C0C0', marginRight: 4 }} />}
          {index === 2 && <TrophyOutlined style={{ color: '#CD7F32', marginRight: 4 }} />}
          <Text strong>{index + 1}</Text>
        </div>
      )
    },
    {
      title: t("COURSE_NAME"),
      dataIndex: "courseName",
      key: "courseName",
      render: (name) => (
        <div className="course-name">
          <BookOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          <Text>{name}</Text>
        </div>
      )
    },
    {
      title: t("ENROLLMENTS"),
      dataIndex: "uniqueEnrollments",
      key: "uniqueEnrollments",
      align: "center",
      render: (count) => (
        <Tag color="blue">
          <UserOutlined style={{ marginRight: 4 }} />
          {count}
        </Tag>
      )
    },
    {
      title: t("TOTAL_SESSIONS"),
      dataIndex: "totalSessions",
      key: "totalSessions",
      align: "center",
      render: (count) => (
        <Tag color="green">
          <PlayCircleOutlined style={{ marginRight: 4 }} />
          {count}
        </Tag>
      )
    }
  ];

  // Top students table columns
  const topStudentsColumns = [
    {
      title: t("RANK"),
      width: 80,
      align: "center",
      render: (_, __, index) => (
        <div className="rank-cell">
          {index === 0 && <TrophyOutlined style={{ color: '#FFD700', marginRight: 4 }} />}
          {index === 1 && <TrophyOutlined style={{ color: '#C0C0C0', marginRight: 4 }} />}
          {index === 2 && <TrophyOutlined style={{ color: '#CD7F32', marginRight: 4 }} />}
          <Text strong>{index + 1}</Text>
        </div>
      )
    },
    {
      title: t("STUDENT_NAME"),
      dataIndex: "studentName",
      key: "studentName",
      render: (name) => (
        <div className="student-name">
          <UserOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          <Text>{name}</Text>
        </div>
      )
    },
    {
      title: t("AVERAGE_SCORE"),
      dataIndex: "averageScore",
      key: "averageScore",
      align: "center",
      render: (score) => (
        <Tag color={parseFloat(score) >= 90 ? 'green' : parseFloat(score) >= 80 ? 'blue' : 'orange'}>
          <StarOutlined style={{ marginRight: 4 }} />
          {score}
        </Tag>
      )
    },
    {
      title: t("TOTAL_SESSIONS"),
      dataIndex: "totalSessions",
      key: "totalSessions",
      align: "center"
    },
    {
      title: t("HIGHEST_SCORE"),
      dataIndex: "highestScore",
      key: "highestScore",
      align: "center",
      render: (score) => (
        <Text strong style={{ color: '#52c41a' }}>{score}</Text>
      )
    }
  ];

  if (!statisticsData && !isLoading) {
    return (
      <div className="statistics-overview-container">
        <Card>
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Text>{t("NO_STATISTICS_DATA")}</Text>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <Loading active={isLoading} transparent>
      <div className="statistics-overview-container">
        {/* Header */}
        <Card className="statistics-header-card">
          <div className="statistics-header">
            <div className="header-left">
              <div className="header-info">
                <Title level={2} className="statistics-title">
                  {t("PLATFORM_STATISTICS_OVERVIEW")}
                </Title>
                <Text className="statistics-description">
                  {t("PLATFORM_STATISTICS_DESCRIPTION")}
                </Text>
              </div>
            </div>
          </div>
        </Card>

        {/* Filter Card */}
        <Card className="statistics-filter-card">
          <AntForm
            form={formFilter}
            layout="horizontal"
            size="large"
            className="form-filter"
            onFinish={onSubmitFilter}
          >
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={18}>
                <Row gutter={16} align="middle">
                  <Col xs={24} sm={12} md={10} lg={8}>
                    <AntForm.Item name="dateRange" className="filter-form-item">
                      <RangePicker
                        value={dateRange}
                        onChange={handleDateRangeChange}
                        format="DD/MM/YYYY"
                        suffixIcon={<CalendarOutlined />}
                        placeholder={[t("START_DATE"), t("END_DATE")]}
                        style={{ width: '100%' }}
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} sm={12} md={6} lg={4}>
                    <div className="filter-info">
                      <Text type="secondary" className="filter-label">
                        <FilterOutlined style={{ marginRight: 4 }} />
                        {t("FILTER_PERIOD")}
                      </Text>
                    </div>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={6} className="filter-buttons-col">
                <div className="admin-filter-buttons">
                  <AntButton
                    type={BUTTON.GHOST_WHITE}
                    size="large"
                    onClick={onClearFilter}
                    icon={<CalendarOutlined />}
                  >
                    {t("CLEAR_FILTER")}
                  </AntButton>
                  <AntButton
                    type={BUTTON.DEEP_NAVY}
                    size="large"
                    htmlType="submit"
                    icon={<ReloadOutlined />}
                  >
                    {t("SEARCH_FILTER")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        {/* Platform Overview Statistics */}
        <Card className="platform-overview-card" title={t("PLATFORM_OVERVIEW")}>
          <Row gutter={[24, 24]}>
            <Col xs={24} sm={12} lg={6}>
              <Statistic
                title={t("TOTAL_COURSES")}
                value={statisticsData?.totalCourses}
                prefix={<BookOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Statistic
                title={t("ACTIVE_STUDENTS")}
                value={statisticsData?.totalActiveStudents}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Statistic
                title={t("TOTAL_SESSIONS")}
                value={statisticsData?.totalSessions}
                prefix={<PlayCircleOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Statistic
                title={t("PLATFORM_AVERAGE_SCORE")}
                value={statisticsData?.averagePlatformScore}
                prefix={<StarOutlined />}
                // suffix="%"
                valueStyle={{ color: '#fa8c16' }}
              />
            </Col>
          </Row>
        </Card>

        {/* Completion Rate */}
        <Card className="completion-rate-card" title={t("OVERALL_COMPLETION_RATE")}>
          <Row gutter={24} align="middle">
            <Col xs={24} md={12}>
              <div className="completion-progress">
                <Progress
                  type="circle"
                  percent={parseFloat(statisticsData?.overallCompletionRate || 0)}
                  format={(percent) => `${percent}%`}
                  size={200}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              </div>
            </Col>
            <Col xs={24} md={12}>
              <div className="completion-details">
                <div className="detail-item sessions-item">
                  <div className="detail-icon">
                    <PlayCircleOutlined />
                  </div>
                  <div className="detail-content">
                    <div className="detail-label">{t("TOTAL_SESSIONS")}</div>
                    <div className="detail-value">{statisticsData?.totalSessions}</div>
                  </div>
                </div>
                <div className="detail-item completed-sessions-item">
                  <div className="detail-icon">
                    <TrophyOutlined />
                  </div>
                  <div className="detail-content">
                    <div className="detail-label">{t("COMPLETED_SESSIONS")}</div>
                    <div className="detail-value">{statisticsData?.totalCompletedSessions}</div>
                  </div>
                </div>
                <div className="detail-item rate-item">
                  <div className="detail-icon">
                    <PercentageOutlined />
                  </div>
                  <div className="detail-content">
                    <div className="detail-label">{t("COMPLETION_RATE")}</div>
                    <div className="detail-value">{statisticsData?.overallCompletionRate}%</div>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Popular Courses and Top Students */}
        <Row gutter={24}>
          <Col xs={24} lg={24}>
            <Card className="popular-courses-card" title={t("MOST_POPULAR_COURSES")}>
              <Table
                columns={popularCoursesColumns}
                dataSource={statisticsData?.mostPopularCourses || []}
                rowKey="courseId"
                pagination={false}
                size="middle"
              />
            </Card>
          </Col>
          <Col xs={24} lg={24}>
            <Card className="top-students-card" title={t("TOP_PERFORMING_STUDENTS")}>
              <Table
                columns={topStudentsColumns}
                dataSource={statisticsData?.topPerformingStudents || []}
                rowKey="studentId"
                pagination={false}
                size="middle"
              />
            </Card>
          </Col>
        </Row>
      </div>
    </Loading>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(StatisticsOverviewScreen);
