import React, { useRef } from 'react';
import { Card, Typography, Button, Space } from 'antd';
import AIScenarioTabs from './components/AIScenarioTabs';

const { Title } = Typography;

// Component test đơn giản để kiểm tra AI Scenario Tabs
const TestCourseDetail = () => {
  const testCourseId = "test-course-id";
  const scenarioTabsRef = useRef();
  const [scenarios, setScenarios] = React.useState([]);

  const handleScenariosChange = (scenarios) => {
    console.log('Scenarios changed:', scenarios);
    setScenarios(scenarios);
  };

  const handleTaskAdd = (task) => {
    console.log('Task added:', task);
  };

  const handleTaskUpdate = (task) => {
    console.log('Task updated:', task);
  };

  const handleTaskDelete = (taskId) => {
    console.log('Task deleted:', taskId);
  };

  const handleDebugTasks = () => {
    if (scenarioTabsRef.current) {
      console.log('=== DEBUG TASKS ===');
      scenarios.forEach((scenario, index) => {
        console.log(`Scenario ${index + 1} (${scenario.name}):`);
        if (scenario.taskIds && scenario.taskIds.length > 0) {
          scenario.taskIds.forEach((task, taskIndex) => {
            const taskId = typeof task === 'object' ? task._id : task;
            const taskName = typeof task === 'object' ? task.name : 'String ID';
            const isUnsaved = taskId?.startsWith('temp_') || !taskId;
            console.log(`  Task ${taskIndex + 1}: ${taskName || 'Unnamed'} (ID: ${taskId}) ${isUnsaved ? '❌ UNSAVED' : '✅ SAVED'}`);
          });
        } else {
          console.log('  No tasks');
        }
      });
      console.log('===================');
    }
  };

  const handleCheckUnsavedTasks = () => {
    if (scenarioTabsRef.current && scenarioTabsRef.current.checkUnsavedTasks) {
      const unsaved = scenarioTabsRef.current.checkUnsavedTasks();
      console.log('Current unsaved tasks:', unsaved);
      console.log('Unsaved tasks count:', unsaved.length);
      if (unsaved.length > 0) {
        console.log('❌ Save button should be disabled');
      } else {
        console.log('✅ Save button should be enabled');
      }
    }
  };

  const handleSaveAll = async () => {
    if (scenarioTabsRef.current) {
      console.log('Testing save all scenarios...');
      const result = await scenarioTabsRef.current.saveAllScenarios();
      console.log('Save result:', result);
      if (result) {
        console.log('✅ All scenarios saved successfully');
      } else {
        console.log('❌ Failed to save scenarios (likely due to unsaved tasks)');
      }
    }
  };

  return (
    <div style={{ padding: '24px', background: '#fff', minHeight: '100vh' }}>
      <Title level={2}>Test AI Scenario Tabs - Giao diện đơn giản</Title>

      <Space style={{ marginBottom: 16 }}>
        <Button type="primary" onClick={handleSaveAll}>
          Test Save All Scenarios
        </Button>
        <Button onClick={() => console.log('Current scenarios:', scenarios)}>
          Log Current Scenarios
        </Button>
        <Button onClick={handleDebugTasks}>
          Debug Tasks Status
        </Button>
        <Button onClick={handleCheckUnsavedTasks}>
          Check Unsaved Tasks
        </Button>
      </Space>

      <Card
        title="AI Scenarios Management"
        style={{
          borderRadius: '4px',
          boxShadow: '0 1px 4px rgba(0, 0, 0, 0.05)'
        }}
      >
        <AIScenarioTabs
          ref={scenarioTabsRef}
          courseId={testCourseId}
          onScenariosChange={handleScenariosChange}
          selectedSimulationType="Sale"
          onTaskAdd={handleTaskAdd}
          onTaskUpdate={handleTaskUpdate}
          onTaskDelete={handleTaskDelete}
        />
      </Card>
    </div>
  );
};

export default TestCourseDetail;
