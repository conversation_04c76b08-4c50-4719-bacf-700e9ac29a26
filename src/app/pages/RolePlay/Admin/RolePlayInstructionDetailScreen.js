import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { Card, Form, Input, Select, Button, Row, Col } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import Loading from "@component/Loading";
import { toast } from "@component/ToastProvider";

import { LINK } from "@link";
import { BUTTON } from "@constant";

import { createInstruction, getInstructionDetail, updateInstruction } from "@app/services/RolePlay/RolePlayInstructionService";

import "./RolePlayInstructionDetailScreen.scss";

// Định nghĩa các loại mô phỏng
const SIMULATION_TYPES = ['Sale', 'Service', 'HR', 'Education', 'Other'];

// <PERSON><PERSON><PERSON> nghĩa các biến có sẵn
const AVAILABLE_VARIABLES = [
  {name: "{personaInfo}", description: "Thông tin persona", defaultValue: "Tên: Nguyễn Văn A, Tuổi: 35, Chức vụ: Giảng viên"},
  {name: "{referenceInfo}", description: "Thông tin tham khảo", defaultValue: "Danh sách các 200 từ vựng tiếng Anh cơ bản : learn, book..."},
  {name: "{courseInfo}", description: "Thông tin khóa học", defaultValue: "Khóa học Tiếng Anh cơ bản, Khóa học kéo dài 3 tháng, học 3 buổi/tuần"},
];

const RolePlayInstructionDetailScreen = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams();
  const isCreateMode = !id;

  const [form] = Form.useForm();
  const [isLoading, setLoading] = useState(false);
  const [initialValues, setInitialValues] = useState({});
  const [conversationContent, setConversationContent] = useState("");
  const [analyzeContent, setAnalyzeContent] = useState("");
  useEffect(() => {
    if (!isCreateMode) {
      fetchInstructionDetail();
    }
  }, [id]);

  const fetchInstructionDetail = async () => {
    setLoading(true);
    try {
      const response = await getInstructionDetail(id);
      if (response) {
        const { name, simulationType, personaInstruction, conversationInstruction, analyzeInstruction } = response;
        const formValues = { name, simulationType, personaInstruction };
        setInitialValues(formValues);
        form.setFieldsValue(formValues);
        setConversationContent(conversationInstruction || "");
        setAnalyzeContent(analyzeInstruction || "");
      }
    } catch (error) {
      console.error("Error fetching instruction:", error);
      toast.error(t("ERROR_FETCHING_INSTRUCTION"));
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values) => {
    setLoading(true);
    try {
      const saveData = {
        ...values,
        conversationInstruction: conversationContent,
        analyzeInstruction: analyzeContent
      };

      if (isCreateMode) {
        const response = await createInstruction(saveData);
        if (response) {
          toast.success(t("CREATE_INSTRUCTION_SUCCESS"));
          navigate(LINK.ADMIN.ROLE_PLAY_INSTRUCTION_MANAGEMENT);
        } else {
          toast.error(t("CREATE_INSTRUCTION_ERROR"));
        }
      } else {
        const response = await updateInstruction(id, saveData);
        if (response) {
          toast.success(t("UPDATE_INSTRUCTION_SUCCESS"));
          navigate(LINK.ADMIN.ROLE_PLAY_INSTRUCTION_MANAGEMENT);
        } else {
          toast.error(t("UPDATE_INSTRUCTION_ERROR"));
        }
      }
    } catch (error) {
      console.error("Error saving instruction:", error);
      toast.error(t("ERROR_SAVING_INSTRUCTION"));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(LINK.ADMIN.ROLE_PLAY_INSTRUCTION_MANAGEMENT);
  };

  const insertVariable = (variable, field) => {
    if (field === 'conversation') {
      setConversationContent((prevContent) => {
        return prevContent + " " + variable.name + " ";
      });
    } else if (field === 'analyze') {
      setAnalyzeContent((prevContent) => {
        return prevContent + " " + variable.name + " ";
      });
    } else if (field === 'persona') {
      // Lấy giá trị hiện tại của personaInstruction
      const currentValue = form.getFieldValue('personaInstruction') || '';
      // Cập nhật giá trị với biến được chèn vào
      form.setFieldsValue({
        personaInstruction: currentValue + " " + variable.name + " "
      });
    }
  };

  return (
    <Loading active={isLoading} transparent>
      <div className="roleplay-instruction-detail-container">
        <div className="roleplay-instruction-detail-header">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={handleCancel}
            type="link"
            className="back-button"
          >
            {t("BACK")}
          </Button>
          <h1 className="roleplay-instruction-detail-title">
            {isCreateMode ? t("CREATE_NEW_INSTRUCTION") : t("EDIT_INSTRUCTION")}
          </h1>
        </div>

        <Card className="instruction-information-card">
          <h2 className="section-title">{t("INSTRUCTION_INFORMATION")}</h2>
          <AntForm
            form={form}
            layout="vertical"
            initialValues={initialValues}
            onFinish={handleSave}
            className="instruction-form"
          >
            <Row gutter={16}>
              <Col xs={24} md={12}>
                <AntForm.Item
                  name="name"
                  label={t("INSTRUCTION_NAME")}
                  rules={[
                    {
                      required: true,
                      message: t("PLEASE_ENTER_INSTRUCTION_NAME"),
                    },
                  ]}
                >
                  <Input placeholder={t("ENTER_INSTRUCTION_NAME")} />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={12}>
                <AntForm.Item
                  name="simulationType"
                  label={t("SIMULATION_TYPE")}
                  rules={[
                    {
                      required: true,
                      message: t("PLEASE_SELECT_SIMULATION_TYPE"),
                    },
                  ]}
                >
                  <Select placeholder={t("SELECT_SIMULATION_TYPE")}>
                    {SIMULATION_TYPES.map((type) => (
                      <Select.Option key={type} value={type}>
                        {type}
                      </Select.Option>
                    ))}
                  </Select>
                </AntForm.Item>
              </Col>
            </Row>

            <div className="persona-instruction-container">
              <h3 className="instruction-subtitle">{t("PERSONA_INSTRUCTION")}</h3>

              <Card className="variables-card" size="small">
                <div className="card-header">
                  <h4 className="card-title">
                    <span className="card-icon">🔧</span>
                    {t("AVAILABLE_VARIABLES")}
                  </h4>
                  <p className="card-description">{t("CLICK_TO_INSERT_VARIABLE")}</p>
                </div>
                <div className="variables-grid">
                  {AVAILABLE_VARIABLES.map((variable, index) => (
                    <AntButton
                      key={index}
                      onClick={() => insertVariable(variable, 'persona')}
                      className="variable-button"
                      type={BUTTON.GHOST_WHITE}
                      size="small"
                    >
                      {variable.name}
                    </AntButton>
                  ))}
                </div>
              </Card>

              <AntForm.Item
                name="personaInstruction"
                rules={[
                  {
                    required: true,
                    message: t("PLEASE_ENTER_PERSONA_INSTRUCTION"),
                  },
                ]}
              >
                <Input.TextArea
                  placeholder={t("ENTER_PERSONA_INSTRUCTION")}
                  rows={6}
                  className="persona-instruction-textarea"
                />
              </AntForm.Item>
            </div>

            <div className="conversation-instruction-container">
              <h3 className="instruction-subtitle">{t("CONVERSATION_INSTRUCTION")}</h3>

              <Card className="variables-card" size="small">
                <div className="card-header">
                  <h4 className="card-title">
                    <span className="card-icon">🔧</span>
                    {t("AVAILABLE_VARIABLES")}
                  </h4>
                  <p className="card-description">{t("CLICK_TO_INSERT_VARIABLE")}</p>
                </div>
                <div className="variables-grid">
                  {AVAILABLE_VARIABLES.map((variable, index) => (
                    <AntButton
                      key={index}
                      onClick={() => insertVariable(variable, 'conversation')}
                      className="variable-button"
                      type={BUTTON.GHOST_WHITE}
                      size="small"
                    >
                      {variable.name}
                    </AntButton>
                  ))}
                </div>
              </Card>

              <Input.TextArea
                value={conversationContent}
                onChange={(e) => setConversationContent(e.target.value)}
                placeholder={t("ENTER_CONVERSATION_INSTRUCTION")}
                rows={6}
                className="conversation-instruction-textarea"
              />
            </div>

            <div className="analyze-instruction-container">
              <h3 className="instruction-subtitle">{t("ANALYZE_INSTRUCTION")}</h3>

              <Card className="variables-card" size="small">
                <div className="card-header">
                  <h4 className="card-title">
                    <span className="card-icon">🔧</span>
                    {t("AVAILABLE_VARIABLES")}
                  </h4>
                  <p className="card-description">{t("CLICK_TO_INSERT_VARIABLE")}</p>
                </div>
                <div className="variables-grid">
                  {AVAILABLE_VARIABLES.map((variable, index) => (
                    <AntButton
                      key={index}
                      onClick={() => insertVariable(variable, 'analyze')}
                      className="variable-button"
                      type={BUTTON.GHOST_WHITE}
                      size="small"
                    >
                      {variable.name}
                    </AntButton>
                  ))}
                </div>
              </Card>

              <Input.TextArea
                value={analyzeContent}
                onChange={(e) => setAnalyzeContent(e.target.value)}
                placeholder={t("ENTER_ANALYZE_INSTRUCTION")}
                rows={6}
                className="analyze-instruction-textarea"
              />
            </div>

            <div className="save-button-container">
              <AntButton
                type={BUTTON.GHOST_WHITE}
                size="large"
                onClick={handleCancel}
              >
                {t("CANCEL")}
              </AntButton>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                htmlType="submit"
              >
                {t("SAVE")}
              </AntButton>
            </div>
          </AntForm>
        </Card>
      </div>
    </Loading>
  );
};

export default RolePlayInstructionDetailScreen;
