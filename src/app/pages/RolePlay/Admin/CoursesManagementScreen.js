import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import { Card, Form, Input, Row, Col, Select, Tag, Tooltip } from "antd";
import { SearchOutlined, EditOutlined, PlusOutlined, Bar<PERSON><PERSON>Outlined, CalendarOutlined } from "@ant-design/icons";

import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";
import { toast } from "@component/ToastProvider";
import { confirm } from "@component/ConfirmProvider";

import { LINK } from "@link";
import { BUTTON, PAGINATION_INIT, SIMULATION_TYPE_OPTIONS } from "@constant"; // Thêm SIMULATION_TYPE_OPTIONS nếu có

import { handlePagingData } from "@common/dataConverter";
import { handleSearchParams, navigateAfterDelete, orderColumn, paginationConfig, handleReplaceUrlSearch } from "@common/functionCommons";

import { getAllCourses, deleteCourse } from "@services/RolePlay/CourseService"; // Sử dụng CourseService

// Import SCSS nếu cần thiết
import "./CoursesManagementScreen.scss";


const CoursesManagementScreen = ({ ...props }) => {
  const { t } = useTranslation();
  const location = useLocation();

  const [coursesData, setCoursesData] = useState(PAGINATION_INIT);
  // const [isShowCreate, setShowCreate] = useState(false); // Tạm thời comment lại nếu chưa dùng đến modal tạo mới
  const [formFilter] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);


  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    fetchCoursesData(paging, query);
  }, [location.search]);


  async function fetchCoursesData(
    paging = coursesData.paging,
    query = coursesData.query,
  ) {
    setIsLoading(true);
    const apiResponse = await getAllCourses(query, paging); // Gọi API lấy danh sách khoá học
    if (apiResponse) {
      setCoursesData(handlePagingData(apiResponse, query));
    }
    setIsLoading(false);
  }

  async function handleDeleteCourse(courseId, courseName) {
    confirm.delete({
      title: t("DELETE_COURSE"), // Thay đổi text cho phù hợp
      content: t("DELETE_COURSE_CONFIRM", { title: courseName }), // Thay đổi text
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async () => {
        setIsLoading(true);
        try {
          const apiResponse = await deleteCourse(courseId); // Gọi API xoá khoá học
        if (apiResponse) {
          toast.success(t("DELETE_COURSE_SUCCESS")); // Thay đổi text
          navigateAfterDelete(coursesData); // Sử dụng fetchCoursesData để load lại
          fetchCoursesData();
        } else {
          toast.error(t("DELETE_COURSE_ERROR")); // Thay đổi text
        }
        } catch (error) {
          toast.error(t("DELETE_COURSE_ERROR")); // Thay đổi text
        } finally {
          setIsLoading(false);
        }
      },
    });
  }

  // Hàm tạo khoá học sẽ được thêm sau nếu cần
  // async function handleCreateCourse(values) {
  //   setIsLoading(true);
  //   const apiResponse = await createCourse(values); // Giả sử có hàm createCourse
  //   if (apiResponse) {
  //     toast.success(t("CREATE_COURSE_SUCCESS"));
  //     await fetchCoursesData();
  //     // setShowCreate(false);
  //   } else {
  //     toast.error(t("CREATE_COURSE_ERROR"));
  //     setIsLoading(false);
  //   }
  // }

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, coursesData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    handleReplaceUrlSearch(1, coursesData.paging.pageSize, {});
  };

  const columns = [
    orderColumn(coursesData.paging),
    {
      title: t("COURSE_NAME"), // Tên khoá học
      dataIndex: "name",
      width: 250,
      render: (text, record) => (
        <Link to={LINK.ADMIN.ROLE_PLAY_COURSE_DETAIL.format(record._id)} className="course-title-value"> {/* Thay đổi link nếu cần */}
          {text}
        </Link>
      ),
    },
    {
      title: t("DESCRIPTION"), // Mô tả
      dataIndex: "description",
      width: 300,
      ellipsis: true,
    },
    {
      title: t("SIMULATION_TYPE"), // Loại giả lập
      dataIndex: "simulationType",
      width: 120,
      render: (type) => SIMULATION_TYPE_OPTIONS.find(opt => opt.value === type)?.label || type // Hiển thị label thay vì value
    },
    {
      title: t("ESTIMATED_CALL_TIME"), // Thời gian gọi dự kiến
      dataIndex: "estimatedCallTimeInMinutes",
      width: 150,
      align: "center",
      render: (time) => time ? `${time} ${t("MINUTES")}` : "-"
    },
    {
      title: t("STATUS"), // Trạng thái
      dataIndex: "status",
      width: 120,
      align: "center",
      render: (status) => {
        const statusConfig = {
          published: { color: "green", text: t("PUBLISHED") },
          draft: { color: "orange", text: t("DRAFT") },
          archived: { color: "gray", text: t("ARCHIVED") }
        };
        const config = statusConfig[status] || { color: "default", text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: t("ACTION"),
      width: 160,
      align: "center",
      fixed: "right",
      render: (_, record) => (
        <div className="action-buttons">
          <Tooltip title={t("VIEW_STATISTICS")}>
            <Link to={LINK.ADMIN.ROLE_PLAY_COURSE_STATISTICS.format(record._id)}>
              <div className="action-btn action-btn-statistics">
                <BarChartOutlined />
              </div>
            </Link>
          </Tooltip>
          <Tooltip title={t("EDIT_COURSE")}>
            <Link to={LINK.ADMIN.ROLE_PLAY_COURSE_DETAIL.format(record._id)}>
              <div className="action-btn action-btn-edit">
                <EditOutlined />
              </div>
            </Link>
          </Tooltip>
          <Tooltip title={t("DELETE_COURSE")}>
            <div
              className="action-btn action-btn-delete"
              onClick={() => handleDeleteCourse(record._id, record.name)}
            >
              <DeleteIcon />
            </div>
          </Tooltip>
        </div>
      ),
    },
  ];


  return (
    <Loading active={isLoading} transparent>
      <div className="courses-management-container"> {/* Thay đổi class nếu cần */}
        <Card className="courses-management-info-card"> {/* Thay đổi class */}
          <div className="courses-management-info-header"> {/* Thay đổi class */}
            <div>
              <h1 className="courses-management-title">{t("ROLE_PLAY_COURSES_MANAGEMENT")}</h1> {/* Thay đổi text */}
              <p className="courses-management-description">{t("ROLE_PLAY_COURSES_MANAGEMENT_DESCRIPTION")}</p> {/* Thay đổi text */}
            </div>
            <Link to={LINK.ADMIN.ROLE_PLAY_COURSE_CREATE}> {/* Thay đổi link nếu cần */}
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                className="btn-create-course"
                icon={<PlusOutlined />}
              >
                {t("CREATE_COURSE")} {/* Thay đổi text */}
              </AntButton>
            </Link>
          </div>
        </Card>

        <Card className="courses-management-search-card"> {/* Thay đổi class */}
          <AntForm form={formFilter} layout="horizontal" size={"large"} className="form-filter" onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={12} lg={10}> {/* Điều chỉnh layout cột */}
                <Row gutter={16}>
                  <Col xs={24} md={12} >
                    <AntForm.Item name="name" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_COURSE_NAME_PLACEHOLDER")} // Thay đổi text
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={12}>
                    <AntForm.Item name="simulationType" className="search-form-item">
                      <Select
                        options={SIMULATION_TYPE_OPTIONS} // Sử dụng SIMULATION_TYPE_OPTIONS
                        allowClear
                        placeholder={t("FILTER_BY_SIMULATION_TYPE")} // Thay đổi text
                      />
                    </AntForm.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={12} lg={4} className="search-buttons-col">
                <div className="admin-filter-buttons">
                  <AntButton
                    type={BUTTON.GHOST_WHITE}
                    size="large"
                    onClick={onClearFilter}
                    icon={<CalendarOutlined />}
                  >
                    {t("CLEAR_FILTER")}
                  </AntButton>
                  <AntButton
                    type={BUTTON.DEEP_NAVY}
                    size="large"
                    htmlType="submit"
                    icon={<SearchOutlined />}
                  >
                    {t("SEARCH_FILTER")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="courses-management-table-card">
          <TableAdmin
            scroll={{ x: 1200 }} // Điều chỉnh scroll sau khi bỏ các cột
            columns={columns}
            dataSource={coursesData.rows}
            loading={isLoading}
            pagination={paginationConfig(coursesData.paging, coursesData.query)}
            rowKey="_id"
          />
        </Card>
      </div>
      {/* Modal tạo khoá học sẽ được thêm sau nếu cần */}
      {/* {isShowCreate && (
        <CreateCourseModal
          visible={isShowCreate}
          onCancel={() => setShowCreate(false)}
          onFinish={handleCreateCourse}
        />
      )} */}
    </Loading>
  );
};

export default CoursesManagementScreen;
