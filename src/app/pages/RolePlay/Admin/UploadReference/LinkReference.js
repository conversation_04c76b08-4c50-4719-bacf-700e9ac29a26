import React, { useEffect, useState } from "react";
import { Form, Input, Checkbox } from "antd";
import { useTranslation } from "react-i18next";

import Loading from "@component/Loading";
import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";

import { BUTTON, CONSTANT } from "@constant";
import RULE from "@rule";

import { cloneObj } from "@common/functionCommons";
import { createReferenceUrl } from "@services/RolePlay/ReferenceService";

import Close from "@component/SvgIcons/Close";
import Plus20 from "@component/SvgIcons/Plus/Plus20";
import { toast } from "@component/ToastProvider";

const URL_STATUS_INIT = { success: null, isNew: true };

function LinkReference({ user, courseId, onReferenceAdded, setShowUpload }) {
  const { t } = useTranslation();

  const [isLoading, setLoading] = useState(false);
  const [urlStatus, setUrlStatus] = useState([]);
  const [isPublic, setIsPublic] = useState(false);

  const [formLinkReference] = Form.useForm();

  useEffect(() => {
    formLinkReference.validateFields();
  }, [urlStatus]);

  useEffect(() => {
    formLinkReference.setFieldsValue({ links: [""] });
    setUrlStatus([URL_STATUS_INIT]);
  }, []);

  const onFinish = async (values) => {
    const { links } = values;

    if (Array.isArray(links)) {
      const isExistNull = links.findIndex((element) => !element) !== -1;
      if (isExistNull) {
        setUrlStatus(prevState => {
          return cloneObj(prevState).map(state => {
            delete state.isNew;
            return state;
          });
        });
        return;
      }

      setLoading(true);
      let allSuccess = true;
      const addedReferences = [];

      for (let i = 0; i < links.length; i++) {
        const result = await handleCreateReference(links[i], i);
        if (result.success) {
          addedReferences.push(result.data);
        } else {
          allSuccess = false;
        }
      }

      setLoading(false);

      if (addedReferences.length > 0) {
        onReferenceAdded(addedReferences);
      }

      if (allSuccess) {
        toast.success(t("CREATE_REFERENCE_SUCCESS"));
        setShowUpload(false);
      }
    }
  };

  async function handleCreateReference(url, index) {
    // Sử dụng URL làm tên nếu không có tên khác
    const name = url;

    const apiRequest = {
      url,
      name,
      courseId,
      isPublic,
    };

    const apiResponse = await createReferenceUrl(apiRequest);

    if (!apiResponse.success) {
      setUrlStatus(prevState => {
        const newState = cloneObj(prevState);
        newState[index] = apiResponse;
        return newState;
      });
    }

    return apiResponse;
  }

  return <>
    <AntForm
      form={formLinkReference}
      name="reference-link-form"
      onFinish={onFinish}
    >
      <Form.List name="links">
        {(fields, { add, remove }) => (
          <>
            <Loading active={isLoading} className="reference-link__list">
              {fields.map((field, index) => {
                return <div key={field.key} className="reference-link__item">
                  <AntForm.Item
                    name={[field.name]}
                    validateTrigger={["onChange", "onBlur"]}
                    rules={[
                      () => ({
                        validator(_, value) {
                          const fieldStatus = urlStatus[field.name];
                          if (fieldStatus) {
                            if (!fieldStatus.success && fieldStatus.message) {
                              return Promise.reject(new Error(fieldStatus.message));
                            }
                          }
                          if (!fieldStatus.isNew && (!value || !value?.trim())) {
                            return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                          }
                          console.log(value, RULE.URL.pattern.test(value));
                          if (value && !RULE.URL.pattern.test(value)) {
                            return Promise.reject(new Error(t("INVALID_ADDRESS")));
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                    className="reference-link__item-input"
                  >
                    <Input
                      size="large"
                      placeholder={t("PASTE_YOUR_REFERENCE_URL_HERE")}
                      onChange={() => {
                        if (urlStatus[index].isNew) {
                          setUrlStatus(prevState => {
                            const newState = cloneObj(prevState);
                            delete newState[index].isNew;
                            return newState;
                          });
                        }
                        if (!urlStatus[index].success && urlStatus[index].message) {
                          setUrlStatus(prevState => {
                            const newState = cloneObj(prevState);
                            newState[index] = { success: null };
                            return newState;
                          });
                        }
                      }}
                    />
                  </AntForm.Item>

                  <AntButton
                    size="small"
                    className="reference-link__item-remove"
                    type={BUTTON.WHITE}
                    icon={<Close />}
                    shape="circle"
                    onClick={() => {
                      remove(field.name);
                      setUrlStatus(prevState => prevState.filter((_, index) => index !== field.name));
                    }}
                  />
                </div>;
              })}
            </Loading>

            <div className="reference-link__item-add">
              <AntButton
                size="large"
                type={BUTTON.WHITE_NAVI}
                onClick={() => {
                  add();
                  setUrlStatus(prevState => [...prevState, URL_STATUS_INIT]);
                }}
                icon={<Plus20 />}
                shape="circle"
                disabled={isLoading}
              />
            </div>

            {/* Checkbox để đánh dấu tài liệu công khai */}
            <div className="reference-public-checkbox" style={{ margin: '16px 0' }}>
              <Checkbox
                checked={isPublic}
                onChange={(e) => setIsPublic(e.target.checked)}
              >
                {t('MAKE_REFERENCE_PUBLIC')}
              </Checkbox>
            </div>

            <div className="reference-link__action">
              <AntButton
                size="large"
                onClick={() => setShowUpload(false)}
              >
                {t("CANCEL")}
              </AntButton>
              <AntButton
                size="large"
                type={BUTTON.DEEP_NAVY}
                htmlType="submit"
                loading={isLoading}
              >
                {t("ADD_REFERENCES")}
              </AntButton>
            </div>
          </>
        )}
      </Form.List>
    </AntForm>
  </>;
}

export default LinkReference;
