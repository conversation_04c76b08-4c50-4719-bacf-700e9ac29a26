.reference__type {
  margin-bottom: 24px;
}

.reference-link__list {
  margin-bottom: 16px;
}

.reference-link__item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  
  .reference-link__item-input {
    flex-grow: 1;
    margin-bottom: 0;
    margin-right: 16px;
  }
  
  .reference-link__item-remove {
    flex-shrink: 0;
  }
}

.reference-link__item-add {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.reference-link__action {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

.upload-reference__progress-upload {
  padding: 24px;
}

.upload-reference__progress-list {
  margin-top: 24px;
  margin-bottom: 24px;
}

.upload-reference__capacity {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 24px;
  
  .upload-reference__capacity-label {
    margin-right: 8px;
    color: rgba(0, 0, 0, 0.45);
  }
  
  .upload-reference__capacity-value {
    font-weight: 500;
  }
  
  .upload-reference__capacity-exceeded {
    color: #ff4d4f;
  }
}

.upload-reference__progress-action {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}