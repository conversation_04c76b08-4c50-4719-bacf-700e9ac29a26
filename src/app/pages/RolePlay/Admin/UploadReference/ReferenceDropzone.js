import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useDropzone } from "react-dropzone";
import { useTranslation } from "react-i18next";
import { Checkbox } from "antd";
import clsx from "clsx";

import { toast } from "@component/ToastProvider";
import AntButton from "@component/AntButton";
import FileProgress from "@component/FileProgress";

import { BUTTON, CONSTANT, UPLOAD_STATUS } from "@constant";
import { uploadReferenceFile } from "@services/RolePlay/ReferenceService";
import { cloneObj, humanFileSize, randomKey } from "@common/functionCommons";

import Upload from "@component/SvgIcons/Upload";

const FILE_MAX_SIZE = 10 * 1024 * 1024; // 10MB

function ReferenceDropzone({ user, courseId, onReferenceAdded, setShowUpload }) {
  const { t } = useTranslation();

  const [referenceFiles, setReferenceFiles] = useState([]);
  const [isUploading, setUploading] = useState(false);
  const [isPublic, setIsPublic] = useState(false);

  useEffect(() => {
    if (isUploading) {
      const checkStillUpload = referenceFiles.find(resource => resource.status === UPLOAD_STATUS.UPLOADING);
      if (!checkStillUpload) {
        setUploading(false);
      }
    }
  }, [referenceFiles]);

  function removeFileItem(fileRemove) {
    setReferenceFiles(prevState => prevState.filter(file => file.key !== fileRemove.key));
  }

  async function handleUploadReference(referenceFile) {
    const fileInfo = {
      type: "file",
      courseId: courseId, // Thêm courseId vào fileInfo
      isPublic: isPublic // Thêm isPublic vào fileInfo
    };

    const axiosConfig = {
      onUploadProgress: function (progressEvent) {
        let percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        setReferenceFiles(prevState => {
          const newState = [...prevState];
          newState.forEach(state => {
            if (state.key === referenceFile.key) {
              state.status = UPLOAD_STATUS.UPLOADING;
              state.percent = Math.min(percentCompleted, 99);
            }
          });
          return newState;
        });
      },
    };

    const apiResponse = await uploadReferenceFile(referenceFile.file, fileInfo, axiosConfig);
    setReferenceFiles(prevState => {
      const newState = [...prevState];
      newState.forEach(state => {
        if (state.key === referenceFile.key) {
          state.status = apiResponse.success ? UPLOAD_STATUS.SUCCESS : UPLOAD_STATUS.ERROR;
          state.percent = apiResponse.success ? 100 : 0;
          if (apiResponse.success) {
            state.data = apiResponse.data;
          }
        }
      });
      return newState;
    });

    return apiResponse;
  }

  async function handleSaveReference() {
    setUploading(true);

    const uploadPromises = [];
    const filesToUpload = referenceFiles.filter(file =>
      ![UPLOAD_STATUS.UPLOADING, UPLOAD_STATUS.SUCCESS].includes(file.status)
    );

    for (const referenceFile of filesToUpload) {
      uploadPromises.push(handleUploadReference(referenceFile));
    }

    const results = await Promise.all(uploadPromises);
    const successResults = results.filter(result => result.success);

    if (successResults.length > 0) {
      const addedReferences = successResults.map(result => result.data);
      onReferenceAdded(addedReferences);
      toast.success(t("UPLOAD_REFERENCE_SUCCESS"));

      if (successResults.length === results.length) {
        setShowUpload(false);
      }
    }
  }

  const { getRootProps, getInputProps, open, fileRejections } = useDropzone({
    onDrop: handleDrop,
    noClick: true,
    accept: {
      'application/pdf': ['.pdf'],
      'audio/mpeg': ['.mp3'],
      'audio/wav': ['.wav'],
      'video/mp4': ['.mp4'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-powerpoint': ['.ppt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx']
    },
    maxSize: FILE_MAX_SIZE
  });

  useEffect(() => {
    const isExceededSize = fileRejections.find((fileError) => fileError?.file?.size > FILE_MAX_SIZE);
    if (isExceededSize) {
      toast.error(t("FILE_EXCEEDS_MAX_FILE_SIZE"));
    }
  }, [fileRejections.length]);

  const handlePaste = useCallback(async (event) => {
    const items = (event.clipboardData || event.originalEvent.clipboardData).items;
    handleDrop(items);
  }, []);

  const [allSelectedCapacity, notUploadedCapacity] = useMemo(() => {
    let allSize = 0, notUploadSize = 0;
    referenceFiles.forEach((resource) => {
      allSize += resource.file.size;
      if (resource.status !== UPLOAD_STATUS.SUCCESS) {
        notUploadSize += resource.file.size;
      }
    });
    return [allSize, notUploadSize];
  }, [referenceFiles]);

  function handleDrop(files) {
    setReferenceFiles(prevState => {
      const newState = Array.from(files)
        .filter(file => file.type) // Filter out non-file items
        .map(file => ({
          key: randomKey(),
          file,
          status: UPLOAD_STATUS.PENDING,
          percent: 0,
        }));
      return [...prevState, ...newState];
    });
  }

  const isDisableUpload = useMemo(() => {
    return !referenceFiles.filter(file => ![UPLOAD_STATUS.UPLOADING, UPLOAD_STATUS.SUCCESS].includes(file.status))?.length;
  }, [referenceFiles]);

  return <div {...getRootProps()} onPaste={handlePaste}>
    <input {...getInputProps()} />

    <div className="upload-reference__progress-upload">
      <div>
        <AntButton
          size="large"
          type={BUTTON.DEEP_NAVY}
          onClick={open}
          icon={<Upload />}
          iconLocation={CONSTANT.RIGHT}
        >
          {t("SELECT_REFERENCE_FILES")}
        </AntButton>
      </div>

      {!!referenceFiles.length && <div className="upload-reference__progress-list">
        {referenceFiles.map((resource, index) => {
          return <FileProgress
            key={index}
            resource={resource}
            onDelete={removeFileItem}
          />;
        })}
      </div>}

      {!!allSelectedCapacity && <div className="upload-reference__capacity">
        <div className="upload-reference__capacity-label">
          {t("TOTAL_CAPACITY")}
        </div>
        <div className="upload-reference__capacity-value">
          {humanFileSize(allSelectedCapacity)}
        </div>
      </div>}

      {/* Checkbox để đánh dấu tài liệu công khai */}
      <div className="reference-public-checkbox" style={{ margin: '16px 0' }}>
        <Checkbox
          checked={isPublic}
          onChange={(e) => setIsPublic(e.target.checked)}
        >
          {t('MAKE_REFERENCE_PUBLIC')}
        </Checkbox>
      </div>

      <div className="upload-reference__progress-action">
        <AntButton
          size="large"
          onClick={() => setShowUpload(false)}
        >
          {t("CANCEL")}
        </AntButton>
        <AntButton
          size="large"
          type={BUTTON.DEEP_NAVY}
          onClick={handleSaveReference}
          disabled={isDisableUpload}
        >
          {t("UPLOAD")}
        </AntButton>
      </div>
    </div>
  </div>;
}

export default ReferenceDropzone;
