import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Tabs } from "antd";
import { connect } from "react-redux";

import AntModal from "@component/AntModal";
import ReferenceDropzone from "./ReferenceDropzone";
import LinkReference from "./LinkReference";

import { CONSTANT } from "@constant";

import "./UploadReference.scss";

function UploadReference({ user, courseId, isShowUpload, setShowUpload, onReferenceAdded, ...props }) {
  const { t } = useTranslation();

  const [referenceType, setReferenceType] = useState(CONSTANT.LINK);

  useEffect(() => {
    if (!isShowUpload) {
      setReferenceType(CONSTANT.LINK);
    }
  }, [isShowUpload]);

  return <AntModal
    width={1058}
    title={t("UPLOAD_REFERENCE_MATERIALS")}
    open={isShowUpload}
    onCancel={() => setShowUpload(false)}
    footerless
  >
    <div className="reference__type">
      <Tabs
        activeKey={referenceType}
        items={[
          { key: CONSTANT.LINK, label: t("REFERENCE_URL") },
          { key: CONSTANT.FILE, label: t("REFERENCE_FILE") },
        ]}
        onChange={setReferenceType}
      />
    </div>

    {referenceType === CONSTANT.FILE
      ? <ReferenceDropzone user={user} courseId={courseId} onReferenceAdded={onReferenceAdded} setShowUpload={setShowUpload} />
      : <LinkReference user={user} courseId={courseId} onReferenceAdded={onReferenceAdded} setShowUpload={setShowUpload} />}

  </AntModal>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(UploadReference);
