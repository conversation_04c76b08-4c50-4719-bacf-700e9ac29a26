import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Card, Col, Form, Input, InputNumber, Row, Select, Tooltip} from 'antd';
import {useNavigate, useParams} from 'react-router-dom';
import {InfoCircleOutlined} from '@ant-design/icons';

import {LINK} from '@link';
import {BUTTON} from '@constant';

import AntButton from '@component/AntButton';
import Loading from '@component/Loading';
import {AntForm} from '@component/AntForm';
import UploadImagePreview from '@component/UploadImagePreview';

import {getAIPersonaDetails, createAIPersona, updateAIPersona} from '@services/RolePlay/AIPersonaService';
import {uploadFile, deleteFile} from '@services/File';

import {toast} from '@component/ToastProvider';

// import "./AIPersonaDetailScreen.scss";

const AIPersonaDetailScreen = () => {
  const {id} = useParams();
  const navigate = useNavigate();
  const {t} = useTranslation();
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);

  // Avatar related state
  const [avatarId, setAvatarId] = useState(null);
  const [initialAvatarIdOnLoad, setInitialAvatarIdOnLoad] = useState(null);
  const [isLoadingAvatar, setIsLoadingAvatar] = useState(false);

  useEffect(() => {
    if (id) {
      setIsEditMode(true);
      fetchPersonaDetails(id);
    } else {
      setIsEditMode(false);
      form.resetFields();
      setAvatarId(null);
      setInitialAvatarIdOnLoad(null);
      form.setFieldsValue({avatarId: null});
    }
  }, [id, form]);

  useEffect(() => {
    // Sync avatarId to the hidden form field whenever it changes
    form.setFieldsValue({avatarId: avatarId});
  }, [avatarId, form]);

  const fetchPersonaDetails = async personaId => {
    setIsLoading(true);
    try {
      const apiResponse = await getAIPersonaDetails(personaId, [{path: 'avatarId', select: 'url name _id'}]);
      if (apiResponse) {
        form.setFieldsValue({
          ...apiResponse,
          filterWords: apiResponse.filterWords || [],
          // avatarId is set in the dedicated useEffect or below
        });
        if (apiResponse.avatarId) {
          setAvatarId(apiResponse.avatarId);
          setInitialAvatarIdOnLoad(apiResponse.avatarId);
          form.setFieldsValue({avatarId: apiResponse.avatarId});
        } else {
          setAvatarId(null);
          setInitialAvatarIdOnLoad(null);
          form.setFieldsValue({avatarId: null});
        }
      } else {
        toast.error(t('ERROR_LOADING_AI_PERSONA_DATA'));
        navigate(LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT);
      }
    } catch (error) {
      console.error('Error fetching persona details:', error);
      toast.error(t('ERROR_LOADING_AI_PERSONA_DATA'));
      navigate(LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUploadAvatar = async file => {
    setIsLoadingAvatar(true);
    try {
      const uploadResponse = await uploadFile(file, {folder: 'image'}); // Use options like ExerciseDetail
      if (uploadResponse && uploadResponse._id) {
        setAvatarId(uploadResponse._id);
        // form.setFieldsValue({ avatarId: uploadResponse._id }); // Done by useEffect on avatarId change
        toast.success(t('UPLOAD_AVATAR_SUCCESS'));
      } else {
        toast.error(t('UPLOAD_AVATAR_ERROR'));
      }
    } catch (error) {
      console.error('Avatar upload error:', error);
      toast.error(t('UPLOAD_AVATAR_ERROR'));
    } finally {
      setIsLoadingAvatar(false);
    }
  };

  const handleClearAvatar = () => {
    setAvatarId(null);
    // form.setFieldsValue({ avatarId: null }); // Done by useEffect on avatarId change
  };

  const handleFormSubmit = async values => {
    setIsLoading(true);
    let oldAvatarIdToDelete = null;

    // values.avatarId is the current avatarId from the hidden Form.Item (synced from avatarId state)
    if (initialAvatarIdOnLoad && initialAvatarIdOnLoad !== values.avatarId) {
      oldAvatarIdToDelete = initialAvatarIdOnLoad;
    }

    const payloadToSend = {
      ...values,
      filterWords: values.filterWords || [], // Ensure filterWords is an array
      // avatarId is already in values from the form
    };

    try {
      let apiResponse;
      if (isEditMode) {
        apiResponse = await updateAIPersona({...payloadToSend, _id: id});
      } else {
        apiResponse = await createAIPersona(payloadToSend);
      }

      if (apiResponse) {
        // API call was successful (returned truthy response)
        if (oldAvatarIdToDelete) {
          try {
            await deleteFile(oldAvatarIdToDelete);
            console.log('Old avatar deleted successfully:', oldAvatarIdToDelete);
          } catch (deleteError) {
            console.error('Failed to delete old avatar:', oldAvatarIdToDelete, deleteError);
            // Optionally notify user about failed deletion of old avatar
          }
        }

        if (isEditMode) {
          toast.success(t('UPDATE_AI_PERSONA_SUCCESS'));
          setInitialAvatarIdOnLoad(values.avatarId); // Update initial to current after successful save
          fetchPersonaDetails(id); // Re-fetch to ensure UI consistency, especially if avatar URL is generated by backend
        } else {
          // Create mode
          toast.success(t('CREATE_AI_PERSONA_SUCCESS'));
          if (apiResponse._id) {
            navigate(LINK.ADMIN.ROLE_PLAY_AI_PERSONA_DETAIL.format(apiResponse._id));
          }
        }
      } else {
        // API call failed (returned falsy response)
        if (isEditMode) {
          toast.error(t('UPDATE_AI_PERSONA_ERROR'));
        } else {
          toast.error(t('CREATE_AI_PERSONA_ERROR'));
        }
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error(t('AN_ERROR_OCCURRED'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT);
  };

  const saveButtonDisabled = isLoading || isLoadingAvatar;

  return (
    <Loading active={isLoading} transparent>
      <div className="ai-persona-detail-container">
        <Card className="ai-persona-detail-header-card">
          <div className="ai-persona-detail-header">
            <div>
              <h1 className="ai-persona-detail-title">{isEditMode ? t('EDIT_AI_PERSONA') : t('CREATE_AI_PERSONA')}</h1>
              <p className="ai-persona-detail-description">
                {isEditMode ? t('EDIT_AI_PERSONA_DESCRIPTION') : t('CREATE_AI_PERSONA_DESCRIPTION')}
              </p>
            </div>
          </div>
        </Card>

        <AntForm form={form} layout="vertical" onFinish={handleFormSubmit} className="ai-persona-detail-form-card">
          <Card title={t('BASIC_INFORMATION')}>
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="name"
                  label={t('AI_PERSONA_NAME')}
                  rules={[{required: true, message: t('FIELD_REQUIRED', {field: t('AI_PERSONA_NAME')})}]}
                >
                  <Input placeholder={t('ENTER_AI_PERSONA_NAME')} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="age" label={t('AGE')}>
                  <Input placeholder={t('ENTER_AI_PERSONA_AGE')} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="gender" label={t('GENDER')}>
                  <Select placeholder={t('SELECT_GENDER')}>
                    <Select.Option value="male">{t('MALE')}</Select.Option>
                    <Select.Option value="female">{t('FEMALE')}</Select.Option>
                    <Select.Option value="other">{t('OTHER')}</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  name="role"
                  label={t('AI_PERSONA_ROLE')}
                  rules={[{required: true, message: t('FIELD_REQUIRED', {field: t('AI_PERSONA_ROLE')})}]}
                >
                  <Input placeholder={t('ENTER_AI_PERSONA_ROLE')} />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item name="mood" label={t('AI_PERSONA_MOOD')}>
                  <Input placeholder={t('ENTER_AI_PERSONA_MOOD')} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="organization" label={t('AI_PERSONA_ORGANIZATION')}>
                  <Input placeholder={t('ENTER_AI_PERSONA_ORGANIZATION')} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              label={t('AI_PERSONA_AVATAR')}
              // No 'name' prop here for Form.Item as UploadImagePreview is not a standard AntD input
              // The avatarId is managed by a separate hidden Form.Item
            >
              <UploadImagePreview
                imageId={avatarId}
                onDrop={handleUploadAvatar}
                onClear={handleClearAvatar}
                loading={isLoadingAvatar}
              />
            </Form.Item>
            {/* Hidden Form.Item to store avatarId and include it in form values */}
            <Form.Item name="avatarId" hidden>
              <Input />
            </Form.Item>

            <Form.Item
              name="personaBackground"
              label={t('AI_PERSONA_BACKGROUND')}
              rules={[{required: true, message: t('FIELD_REQUIRED', {field: t('AI_PERSONA_BACKGROUND')})}]}
            >
              <Input.TextArea rows={4} placeholder={t('ENTER_AI_PERSONA_BACKGROUND')} />
            </Form.Item>

            <Form.Item name="personaConcern" label={t('AI_PERSONA_CONCERNS')}>
              <Input.TextArea rows={3} placeholder={t('ENTER_AI_PERSONA_CONCERNS')} />
            </Form.Item>
          </Card>

          <Card title={t('ADVANCED_SETTINGS')} style={{marginTop: 24}}>
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="smallTalkLikely"
                  label={
                    <span>
                      {t('AI_PERSONA_SMALL_TALK_LIKELY')}
                      <Tooltip title={t('AI_PERSONA_SMALL_TALK_LIKELY_TOOLTIP')}>
                        <InfoCircleOutlined style={{marginLeft: 4, color: 'rgba(0,0,0,.45)'}} />
                      </Tooltip>
                    </span>
                  }
                  rules={[{type: 'number', min: 0, max: 100, message: t('VALUE_BETWEEN_0_100')}]}
                >
                  <InputNumber min={0} max={100} placeholder="0-100" style={{width: '100%'}} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  name="voice"
                  label={t('AI_PERSONA_VOICE')}
                >
                  <Input placeholder={t('ENTER_AI_PERSONA_VOICE')} />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="voiceStyle"
              label={t('AI_PERSONA_VOICE_STYLE')}
            >
              <Input.TextArea rows={3} placeholder={t('ENTER_AI_PERSONA_VOICE_STYLE')} />
            </Form.Item>
            <Form.Item name="filterWords" label={t('AI_PERSONA_FILTER_WORDS')}>
              <Select
                mode="tags"
                style={{width: '100%'}}
                placeholder={t('ENTER_FILTER_WORDS_PLACEHOLDER')}
                tokenSeparators={[',']} // Optional: allow comma to create a new tag
              />
            </Form.Item>
          </Card>

          <Row justify="end" style={{marginTop: 24}} gutter={16}>
            <Col>
              <AntButton type={BUTTON.TEXT} onClick={handleCancel} className="btn-cancel" disabled={saveButtonDisabled}>
                {t('CANCEL')}
              </AntButton>
            </Col>
            <Col>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                htmlType="submit"
                className="btn-save"
                loading={isLoading && !isLoadingAvatar} // Show main loading only if not avatar loading
                disabled={saveButtonDisabled}
              >
                {t('SAVE')}
              </AntButton>
            </Col>
          </Row>
        </AntForm>
      </div>
    </Loading>
  );
};

export default AIPersonaDetailScreen;
