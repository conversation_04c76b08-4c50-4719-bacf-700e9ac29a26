import React from 'react';
import {Card, Avatar, Typography, Divider, Space} from 'antd';
import AntButton from '@component/AntButton';
import {BUTTON} from '@constant';
import {EditOutlined, BookOutlined} from '@ant-design/icons'; // Example icons
import {useTranslation} from 'react-i18next';
import './AIPersonaSetupCard.scss';
import {API} from '@api';
const {Title, Text, Paragraph} = Typography;

// Mock data based on the image and PRD for standalone testing
const defaultPersona = {};

export const AIPersonaSetupCard = ({persona = defaultPersona, onEdit, onLearnMore}) => {
  const {t} = useTranslation();

  const handleEdit = () => {
    if (onEdit) {
      onEdit(persona);
    }
    console.log('Edit persona:', persona.name);
  };

  const handleLearnMore = () => {
    if (onLearnMore) {
      onLearnMore();
    }
    console.log('Learn more about AI Persona Setup');
  };

  return (
    <div>
      <div className="ai-persona-setup-card__section">
        <div className="ai-persona-setup-card__section-header">
          <Title level={5} className="ai-persona-setup-card__section-title">
            {t('AI_PERSONA_DETAILS_TITLE', 'AI Persona Details')}
          </Title>
          <AntButton
            size="small"
            type={BUTTON.DEEP_NAVY}
            icon={<EditOutlined />}
            onClick={handleEdit}
            className="ai-persona-setup-card__edit-button"
          >
            {t('EDIT', 'Edit')}
          </AntButton>
        </div>
        <div className="ai-persona-setup-card__details-content">
          <Avatar size={80} src={API.STREAM_ID.format(persona.avatarId)} className="ai-persona-setup-card__avatar" />
          <div className="ai-persona-setup-card__details-text">
            <Text strong className="ai-persona-setup-card__name">
              {persona.name || t('NAME_NOT_SET', 'Name not set')}
            </Text>
            <Text type="secondary">
              {t('ROLE_LABEL', 'Role')}: {persona.role || t('NOT_SET', 'not set')}
            </Text>
            <Text type="secondary">
              {t('ORGANIZATION_LABEL', 'Organization')}: {persona.organization || t('NOT_SET', 'not set')}
            </Text>
          </div>
        </div>
      </div>

      <div className="ai-persona-setup-card__section">
        <Title level={5} className="ai-persona-setup-card__section-title">
          {t('AI_PERSONA_CHARACTER_TITLE', 'AI Persona Character')}
        </Title>
        <Space direction="vertical" size="small" style={{width: '100%'}}>
          <Text>
            <Text strong>{t('MOOD_LABEL', 'Mood')}:</Text> {persona.mood || t('NOT_SPECIFIED', 'No Mood Specified')}
          </Text>
          <Text>
            <Text strong>{t('SMALL_TALK_LIKELY_LABEL', 'Small Talk Likely')}:</Text>{' '}
            {persona.smallTalkLikely ? `${persona.smallTalkLikely}%` : t('NOT_SET', 'not set')}
          </Text>
          <Text>
            <Text strong>{t('FILTER_WORDS_LABEL', 'Filter Words')}:</Text>{' '}
            {persona.filterWords && persona.filterWords.length > 0
              ? persona.filterWords.join(', ')
              : t('NOT_SET', 'not set')}
          </Text>
        </Space>
      </div>

      <div className="ai-persona-setup-card__section">
        <Title level={5} className="ai-persona-setup-card__section-title">
          {t('AI_PERSONA_BACKGROUND_TITLE', 'AI Persona Background')}
        </Title>
        <Paragraph className="ai-persona-setup-card__paragraph">
          {persona.personaBackground || t('BACKGROUND_NOT_SET', 'Background information not set.')}
        </Paragraph>
      </div>

      <div className="ai-persona-setup-card__section">
        <Title level={5} className="ai-persona-setup-card__section-title">
          {t('AI_PERSONA_CONCERNS_TITLE', 'AI Persona Concerns')}
        </Title>
        <Paragraph className="ai-persona-setup-card__paragraph">
          {persona.personaConcern || t('CONCERNS_NOT_SET', 'Concerns not set.')}
        </Paragraph>
      </div>
    </div>
  );
};
