import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, Upload, InputNumber, Row, Col, Typography, Card, Space, message } from 'antd';
import { UploadOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid'; // For generating unique keys for new tasks
import { AIPersonaSetupCard } from '../AIPersona/AIPersonaSetupCard';
import { TaskEvaluationDisplayCard } from '../Tasks/TaskEvaluationDisplayCard';
// import { createCourse } from '@src/app/services/RolePlay/courseService'; // Placeholder
// import { getAllPersonas } from '@src/app/services/RolePlay/aiPersonaService'; // Placeholder
// import { getAllTasks } from '@src/app/services/RolePlay/taskService'; // Placeholder
import './CourseCreationScreen.scss';

const { TextArea } = Input;
const { Option } = Select;
const { Title } = Typography;

const SIMULATION_TYPES = ['Sale', 'Service', 'HR', 'Education', 'Other'];

// Updated mock data for AI Personas to include details for the card
const MOCK_AI_PERSONAS_FULL = [
  {
    _id: 'persona1',
    name: 'Linda (Sales Expert)',
    avatarUrl: 'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
    role: 'Senior Sales Manager',
    organization: 'Tech Solutions Inc.',
    mood: 'Assertive & Friendly',
    responseLength: 'Medium to Detailed',
    smallTalkRapport: 'Engages in rapport-building small talk',
    fillerWords: 'Minimal filler words',
    background: 'Linda has 10 years of experience in B2B tech sales. She is knowledgeable about the industry and expects a professional interaction.',
    concerns: 'Primary concern is ROI and integration facilidad of new products.'
  },
  {
    _id: 'persona2',
    name: 'David (Customer Support)',
    avatarUrl: 'https://gw.alipayobjects.com/zos/antfincdn/LlvErxo8H9/photo-1503185912284-5271ff81b9a8.webp',
    role: 'Customer Support Lead',
    organization: 'Global Retail Corp.',
    mood: 'Patient & Empathetic',
    responseLength: 'Concise and clear',
    smallTalkRapport: 'Polite, but focused on resolving issues',
    fillerWords: 'Some, to ensure clarity',
    background: 'David handles escalated customer complaints. He is skilled in de-escalation and finding solutions under pressure.',
    concerns: 'Wants to ensure customer satisfaction and efficient problem resolution.'
  }
];

// Expanded Mock Data for Tasks to include all details for TaskEvaluationDisplayCard
const MOCK_TASKS_FULL = [
  {
    id: 'task1',
    topicName: 'Introduction',
    evaluationGuidelines: 'To successfully cover this topic, the trainee needs to:\n* Introduce themselves.\n* Explain their role in the company.\n* Ask for permission to continue the conversation by asking if the prospect has 5 minutes to talk.',
    weight: 25.0,
    exampleVideos: '', // Initialize as empty string or null
    helpfulLinks: '',  // Initialize as empty string or null
    makeOrBreak: false,
  },
  {
    id: 'task2',
    topicName: 'Pitch',
    evaluationGuidelines: 'To successfully cover this topic, the trainee needs to:\n* Explain how their product/service can solve their problems.\n* Provide clear and concise information about the features and benefits of their product/service.\n* Provide evidence to support their claims.',
    weight: 50.0,
    exampleVideos: '',
    helpfulLinks: '',
    makeOrBreak: true,
  },
  {
    id: 'task3',
    topicName: 'Objection Handling',
    evaluationGuidelines: 'To successfully cover this topic, the trainee needs to:\n* Acknowledge the prospect\'s concerns.\n* Show genuine understanding and empathize.\n* Reframe the objection or offer a solution.',
    weight: 25.0,
    exampleVideos: '',
    helpfulLinks: '',
    makeOrBreak: false,
  },
];

// Default structure for a new task
const createNewTaskItem = () => ({
  id: uuidv4(), // Use uuid for unique client-side key before saving to DB
  topicName: '',
  evaluationGuidelines: '',
  weight: 0,
  exampleVideos: '',
  helpfulLinks: '',
  makeOrBreak: false,
});

export const CourseCreationScreen = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [aiPersonas, setAiPersonas] = useState([]);
  const [fileList, setFileList] = useState([]);
  const [selectedPersonaForPreview, setSelectedPersonaForPreview] = useState(null);
  const [taskDetailsList, setTaskDetailsList] = useState([createNewTaskItem()]); // Start with one empty task row

  useEffect(() => {
    // Simulate fetching dropdown data
    // In a real app, replace this with API calls
    setAiPersonas(MOCK_AI_PERSONAS_FULL);
  }, []);

  const handleAiPersonaChange = (value) => {
    if (value) {
      const selected = aiPersonas.find(p => p._id === value);
      setSelectedPersonaForPreview(selected);
    } else {
      setSelectedPersonaForPreview(null);
    }
  };

  // Handler for adding a new task to the list
  const handleAddTask = () => {
    setTaskDetailsList(prevTasks => [...prevTasks, createNewTaskItem()]);
  };

  // Handler for updating an existing task in the list
  const handleTaskUpdate = (updatedTaskRow) => {
    setTaskDetailsList(prevTasks =>
      prevTasks.map(task => (task.id === updatedTaskRow.id ? { ...task, ...updatedTaskRow } : task))
    );
  };

  // Handler for deleting a task from the list
  const handleTaskDelete = (taskIdToDelete) => {
    setTaskDetailsList(prevTasks => prevTasks.filter(task => task.id !== taskIdToDelete));
  };

  const handleUploadChange = ({ fileList: newFileList }) => {
    setFileList(newFileList);
    // Implement actual upload logic here if needed, e.g., calling an API
    // For now, just updating the local state
  };

  const onFinish = async (values) => {
    setLoading(true);
    // Validate that all task topics and guidelines are filled if tasks exist
    if (taskDetailsList.some(task => !task.topicName || !task.evaluationGuidelines)) {
        message.error(t('VALIDATE_TASK_DETAILS_ERROR', 'Please fill in Topic Name and Evaluation Guidelines for all tasks.'));
        setLoading(false);
        return;
    }
    // Validate sum of weights
    const totalWeight = taskDetailsList.reduce((sum, task) => sum + (Number(task.weight) || 0), 0);
    if (taskDetailsList.length > 0 && totalWeight !== 100) {
        message.error(t('VALIDATE_TASK_WEIGHT_ERROR', 'The sum of weights for all tasks must be 100%.'));
        setLoading(false);
        return;
    }

    try {
      const courseData = {
        ...values,
        referenceFiles: fileList.map(file => file.uid), // Or file.response if upload is handled
        tasks: taskDetailsList.map(({ id, ...taskData }) => taskData), // Submit the taskDetailsList, removing client-side id
      };
      console.log('Submitting Course Data:', courseData);
      // await createCourse(courseData); // Uncomment when service is ready
      message.success(t('COURSE_CREATED_SUCCESSFULLY', 'Course created successfully!'));
      form.resetFields();
      setFileList([]);
      setSelectedPersonaForPreview(null);
      setTaskDetailsList([createNewTaskItem()]); // Reset to one empty task
    } catch (error) {
      console.error('Failed to create course:', error);
      message.error(t('COURSE_CREATION_FAILED', 'Failed to create course.'));
    } finally {
      setLoading(false);
    }
  };

  const onFinishFailed = (errorInfo) => {
    console.log('Failed:', errorInfo);
    message.error(t('FORM_SUBMISSION_FAILED', 'Please check the form for errors.'));
  };

  const normFile = (e) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  // Placeholder for edit persona action
  const handleEditPersona = (persona) => {
    console.log("Editing persona:", persona);
    message.info(`Edit action for ${persona.name} (not implemented)`);
  };

  // Placeholder for learn more action
  const handleLearnMorePersona = () => {
    console.log("Learn more about AI Persona Setup");
    message.info("Learn more action (not implemented)");
  };

  return (
    <div className="course-creating-screen">
      <Card>
        <Title level={3} style={{ textAlign: 'center', marginBottom: '24px' }}>{t('CREATE_NEW_COURSE', 'Create New Course')}</Title>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          className="course-creating-form"
        >
          <Row gutter={24}>
            <Col xs={24} md={12}>
              <Form.Item
                name="name"
                label={t('COURSE_NAME', 'Course Name')}
                rules={[{ required: true, message: t('PLEASE_INPUT_COURSE_NAME', 'Please input the course name!') }, {max: 255, message: t('COURSE_NAME_TOO_LONG', 'Course name cannot exceed 255 characters')}]}
              >
                <Input placeholder={t('ENTER_COURSE_NAME', 'Enter course name')} />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="estimatedCallTimeInMinutes"
                label={t('ESTIMATED_CALL_TIME', 'Estimated Call Time (minutes)')}
                rules={[{ type: 'number', min: 0, message: t('TIME_MUST_BE_POSITIVE', 'Time must be a positive number') }]}
              >
                <InputNumber style={{ width: '100%' }} placeholder={t('ENTER_ESTIMATED_TIME', 'Enter estimated time in minutes')} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label={t('COURSE_DESCRIPTION', 'Course Description')}
            rules={[{max: 5000, message: t('DESCRIPTION_TOO_LONG', 'Description cannot exceed 5000 characters')}]}
          >
            <TextArea rows={4} placeholder={t('ENTER_COURSE_DESCRIPTION', 'Enter course description')} />
          </Form.Item>

          <Form.Item
            name="introduction"
            label={t('COURSE_INTRODUCTION', 'Course Introduction (Tasks & Overview)')}
            rules={[{max: 5000, message: t('INTRODUCTION_TOO_LONG', 'Introduction cannot exceed 5000 characters')}]}
          >
            <TextArea rows={4} placeholder={t('ENTER_COURSE_INTRODUCTION', 'Enter course introduction and task overview')} />
          </Form.Item>

          <Row gutter={24}>
            <Col xs={24} md={12}>
              <Form.Item
                name="aiPersonaId"
                label={t('AI_PERSONA', 'AI Persona')}
                rules={[{ required: true, message: t('PLEASE_SELECT_AI_PERSONA', 'Please select an AI Persona!') }]}
              >
                <Select
                  placeholder={t('SELECT_AI_PERSONA', 'Select AI Persona')}
                  loading={!aiPersonas.length}
                  allowClear
                  onChange={handleAiPersonaChange}
                >
                  {aiPersonas.map(persona => (
                    <Option key={persona._id} value={persona._id}>{persona.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="simulationType"
                label={t('SIMULATION_TYPE', 'Simulation Type')}
                rules={[{ required: true, message: t('PLEASE_SELECT_SIMULATION_TYPE', 'Please select simulation type!') }]}
              >
                <Select placeholder={t('SELECT_SIMULATION_TYPE', 'Select simulation type')} allowClear>
                  {SIMULATION_TYPES.map(type => (
                    <Option key={type} value={type}>{t(type.toUpperCase() + '_SIMULATION', type)}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* Display AIPersonaSetupCard if a persona is selected */}
          {selectedPersonaForPreview && (
            <Row gutter={24} style={{ marginBottom: '24px' }}>
                <Col xs={24}>
                    <AIPersonaSetupCard
                        persona={selectedPersonaForPreview}
                        onEdit={handleEditPersona}
                        onLearnMore={handleLearnMorePersona}
                    />
                </Col>
            </Row>
          )}

          <Title level={4} style={{marginTop: '30px', marginBottom: '10px'}}>{t('COURSE_EVALUATION_CRITERIA', 'Course Evaluation Criteria')}</Title>
          <TaskEvaluationDisplayCard
              dataSource={taskDetailsList}
              onTaskAdd={handleAddTask}
              onTaskUpdate={handleTaskUpdate}
              onTaskDelete={handleTaskDelete}
          />

          <Form.Item
            label={t('REFERENCE_URLS', 'Reference URLs')}
          >
            <Form.List name="referenceUrls">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                      <Form.Item
                        {...restField}
                        name={[name]}
                        style={{ flexGrow: 1, marginBottom: 0 }}
                        rules={[{ type: 'url', message: t('INVALID_URL', 'This is not a valid URL!') }]}
                      >
                        <Input placeholder={t('ENTER_REFERENCE_URL', 'Enter reference URL')} />
                      </Form.Item>
                      <DeleteOutlined onClick={() => remove(name)} />
                    </Space>
                  ))}
                  <Form.Item>
                    <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                      {t('ADD_REFERENCE_URL', 'Add Reference URL')}
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
          </Form.Item>

          <Form.Item
            name="referenceFiles"
            label={t('REFERENCE_FILES', 'Reference Files (PDF, Audio)')}
            valuePropName="fileList"
            getValueFromEvent={normFile}
            extra={t('MAX_FILE_SIZE_INFO', 'Max file size: 5MB. Allowed types: PDF, MP3, WAV')}
          >
            <Upload
              name="files"
              action="/api/files/upload" // Replace with your actual upload endpoint
              listType="picture" // Using 'picture' for better preview if images are uploaded, or 'text'
              fileList={fileList}
              onChange={handleUploadChange}
              // beforeUpload={beforeUpload} // Add validation for file type and size
            >
              <Button icon={<UploadOutlined />}>{t('CLICK_TO_UPLOAD', 'Click to Upload')}</Button>
            </Upload>
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginTop: '24px' }}>
            <Button type="primary" htmlType="submit" loading={loading}>
              {t('CREATE_COURSE', 'Create Course')}
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

// Note: For a production app, you would replace mock data/placeholders
// with actual API calls and data fetching logic in useEffect.
// Also, implement proper error handling and user notifications.
// The Upload component's 'action' prop needs a real backend endpoint.
// File validation (beforeUpload) for type and size should be added.
