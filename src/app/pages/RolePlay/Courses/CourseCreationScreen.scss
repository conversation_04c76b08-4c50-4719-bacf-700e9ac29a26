.course-creating-screen {
  padding: 24px;
  background-color: #f0f2f5; // A light background for the page
  min-height: 100vh;

  .ant-card {
    max-width: 1200px; // Increased for wider layout
    margin: 0 auto;
    border-radius: 8px;
  }

  .course-creating-form {
    .ant-form-item-label > label {
      font-weight: 500; // Make labels a bit bolder
    }

    .ant-upload-list-item-name {
        width: calc(100% - 30px); // Ensure file name doesn't overflow with action icons
    }
  }

  // Add more specific styles if needed, for example, for the Form.List items
  .ant-space {
    width: 100%; // Ensure the Space component takes full width for URL inputs
    .ant-form-item {
        flex-grow: 1;
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .ant-card {
      margin: 0 16px;
    }
  }
}
