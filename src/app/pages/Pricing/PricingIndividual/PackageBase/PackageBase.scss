.pricing-package-base {
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
  
  .pricing-package-base__price-unit {
    display: flex;
    gap: 16px;
    align-items: center;

    .price-unit__item {
      font-size: 16px;
      line-height: 24px;
      transform: all 0.3s ease-in-out;
      cursor: pointer;

      &.price-unit__item-active {
        font-size: 22px;
        font-weight: 600;
        line-height: 30px;
        color: #3A18CE;
      }
    }
  }

  .pricing-package-base__content {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    justify-content: center;

    .pricing-package-base__item {
      width: 258px;
      padding: 24px;
      border-radius: 24px;
      box-shadow: 0px 4px 20px 0px #00000026;
      display: flex;
      flex-direction: column;
      gap: 24px;

      .pricing-package-base__item__content {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .item__content__info {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .item__content__info__name {
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
          }

          .item__content__info__price {
            display: flex;
            gap: 6px;
            align-items: baseline;

            del {
              font-weight: 600;
              font-size: 16px;
              line-height: 19.36px;
              letter-spacing: 0px;
              color: #FF0307;
            }

            .price {
              font-size: 24px;
              font-weight: 600;
              line-height: 29.05px;
              align-items: baseline;
            }

            .currency {
              font-family: Inter;
              font-size: 16px;
              font-weight: 600;
              line-height: 19.36px;
            }
          }

          .item__content__info__period {
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0px;
          }

          &::after {
            content: '';
            display: block;
            margin-top: 8px;
            width: 100%;
            height: 1px;
            background-color: #E8EAED;
          }
        }

        .item__content__features {
          display: flex;
          flex-direction: column;
          gap: 24px;

          .item__content__features__item {
            display: flex;
            gap: 16px;
            align-items: flex-start;
          }
        }
      }

      a {
        margin-top: auto;
      }

      .pricing-package-base__item__btn {
        padding: 8px 24px;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        width: 100%;

        &:not(:disabled) {
          background: #3A18CE !important;
          color: #FFF !important;
        }
      }
    }
  }
}