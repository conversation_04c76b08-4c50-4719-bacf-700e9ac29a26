

import { useState } from "react";
import "./PackageBase.scss";
import { useTranslation } from "react-i18next";
import clsx from "clsx";
import PackageBaseItem from "./PackageBaseItem";
import { renderTextTime } from "@src/common/functionCommons";

const PackageBase = ({ packagesBase }) => {

  const [t, i18n] = useTranslation();
  const [activeKey, setActiveKey] = useState("1");

  if (!packagesBase.length) return null;
  return (
    <div className="pricing-package-base">
      <div className="pricing-package-base__price-unit">{["1", "3", "6", "12"].map((value, index) =>
        <div
          key={index}
          onClick={() => setActiveKey(value)}
          className={clsx("price-unit__item", { "price-unit__item-active": activeKey === value })}>
          {renderTextTime(value, t("MONTH"), i18n.language)}
        </div>)}
      </div>
      <div className="pricing-package-base__content">
        {
          packagesBase.map((item, index) => (
            <PackageBaseItem
              key={index}
              packageData={item}
              activeKey={activeKey}
            />
          ))}
      </div>

    </div>
  );
}

export default PackageBase;
