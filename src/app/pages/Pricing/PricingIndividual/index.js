import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";

import { getAllFeature, getAllPackage } from "@src/app/services/Package";

import { CONSTANT, PRICING_TYPE } from "@constant";
import PackageBase from "./PackageBase";
import PackageAddon from "./PackageAddon";
const PricingIndividual = ({ pricingType }) => {
  const { t } = useTranslation();

  const [dataPackages, setDataPackages] = useState([]);

  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    const query = { customerTarget: "student" };
    const [
      packageResponse,
      featuresResponse,] = await Promise.all([
        getAllPackage(query),
        getAllFeature(),
      ]);
    if (packageResponse.length) {
      const reparePackage = packageResponse.sort((a, b) => a.order - b.order).map((item) => {
        const featuresData = [];
        if (featuresResponse.length) {
          Object.entries(item.features).forEach(([featureId, value]) => {
            const feature = featuresResponse.find((item) => item._id === featureId);
            featuresData.push({
              name: feature?.localization?.description,
              unit: feature?.unit,
              count: feature?.type === "Boolean" ? 0 : value,
            });
          });
        }
        return {
          ...item,
          features: featuresData,
        };
      });
      setDataPackages(reparePackage);
    }
  };

  const { packagesBase, packagesAddon } = useMemo(() => {
    let packagesBase = [];
    let packagesAddon = [];
    dataPackages.forEach((item) => {
      if (!!item?.features && item?.type === 'base') {
        packagesBase.push(item);
      } else if (!!item?.features && item?.type === CONSTANT.ADDON) {
        packagesAddon.push(item);
      }
    });
    return { packagesBase, packagesAddon };
  }, [dataPackages]);

  if (pricingType !== PRICING_TYPE.INDIVIDUAL) return null;

  return (
    <>
      <PackageBase packagesBase={packagesBase} />
      {!!packagesAddon.length && <div className="pay-per-submission">
        <span className="pay-per-submission__title">{t("PAY_PER_SUBMISSION")}</span>
        <span className="pay-per-submission__text">{t("PAY_PER_SUBMISSION_DESCRIPTION")}</span>
      </div>}
      <PackageAddon packagesAddon={packagesAddon} />
    </>
  );
}

export default PricingIndividual;
