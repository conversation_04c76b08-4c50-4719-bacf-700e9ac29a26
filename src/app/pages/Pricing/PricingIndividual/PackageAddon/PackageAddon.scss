.pricing-package-addon {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  width: 1080px;
  max-width: 100%;
  justify-content: center;

  .pricing-package-addon__item {
    width: 532px;
    max-width: 100%;
    padding: 24px;
    border-radius: 24px;
    box-shadow: 0px 4px 20px 0px #00000026;
    justify-content: space-between;
    display: flex;
    align-items: center;


    .pricing-package-base__addon__content {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .addon__content__name {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        text-align: left;
      }

      .item__content__info {
        display: flex;
        gap: 8px;
        align-items: center;

        .item__content__info__price {
          font-size: 24px;
          font-weight: 600;
          line-height: 29.05px;
          text-align: left;
          display: flex;
          gap: 6px;
          align-items: baseline;

          del {
            font-weight: 600;
            font-size: 16px;
            line-height: 19.36px;
            letter-spacing: 0px;
            color: #FF0307;
          }

          .currency {
            font-family: Inter;
            font-size: 16px;
            font-weight: 600;
            line-height: 19.36px;
          }
        }
      }
    }
  }
}