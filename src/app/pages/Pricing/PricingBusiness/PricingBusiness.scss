.pricing-business {
  display: flex;
  flex-direction: column;
  padding: 24px;
  gap: 24px;
  border-radius: 24px;
  box-shadow: 0px 4px 20px 0px #00000026;
  align-items: center;
  width: 496px;
  max-width: calc(100%);

  .pricing-business__description {
    max-width: 428px;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0px;
    text-align: center;
  }

  .business__form {
    width: 100%;

    .ant-form-item {
      &:focus-visible {
        outline: none;
      }

      .ant-form-item-control-input-content {
        border-radius: 16px;
        background: #E7E5FF;
        box-shadow: 0px 4px 8px 0px #E7E5FF29;

        >input:-webkit-autofill {
          border-radius: 16px;
        }
      }

      .ant-input.ant-input-lg {
        padding: 15px 24px;
        background: none;
        border: none;
        box-shadow: unset;
        outline: unset;
        font-size: 16px;
        line-height: 24px;
      }

      input:-webkit-autofill {
        -webkit-box-shadow: 0 0 0px 1000px #E7E5FF inset !important;
      }

      .ant-input-affix-wrapper.ant-input-affix-wrapper-lg {
        background: none;
        border: none;
        box-shadow: unset;
        outline: unset;
        padding: 15px 24px;

        .ant-input.ant-input-lg {
          padding: 0;
        }
      }

      .ant-form-item-explain-error {
        padding: 6px 16px;
        border-radius: 8px;
        margin: 4px 0 0 0;
        background: #FFDADA;
        color: #FF0307;
        font-size: 14px;
        line-height: 20px;
      }
    }
  }

  .pricing-business__btn {
    padding: 8px 24px;
    border-radius: 12px;
    width: 200px;

    background-color: #3A18CE !important;
    color: #FFF !important;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
  }
}