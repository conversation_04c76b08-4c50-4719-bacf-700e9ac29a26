import { PRICING_TYPE } from "@constant";
import AntButton from "@src/app/component/AntButton";
import { useTranslation } from "react-i18next";

import "./PricingBusiness.scss";
import { AntForm } from "@src/app/component/AntForm";
import RULE from "@rule";
import { Form, Input } from "antd";
import { toast } from "@src/app/component/ToastProvider";

const PricingBusiness = ({ pricingType }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  const handleSubmit = async (data) => {

  }

  if (pricingType !== PRICING_TYPE.BUSINESS) return null;

  return (
    <div className="pricing-business">
      <div className="pricing-business__description">{t("SUPPORT_BUSINESS_DESCRIPTION")}</div>
      <AntForm
        className="business__form"
        form={form}
        requiredMark={false}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{ email: "", password: "" }}
      >
        <AntForm.Item
          name="fullName"
          rules={[RULE.REQUIRED]}
        >
          <Input size="large" placeholder={t("CONTACT_NAME")} />
        </AntForm.Item>
        <AntForm.Item
          name="phone"
          rules={[RULE.REQUIRED, RULE.PHONE]}
          validateFirst
        >
          <Input size="large" placeholder={t("PHONE_BUSINESS")} />
        </AntForm.Item>
        <AntForm.Item
          name="email"
          rules={[RULE.REQUIRED, RULE.EMAIL]}
          validateFirst
        >
          <Input size="large" placeholder={t("EMAIL")} />
        </AntForm.Item>

        <AntForm.Item
          name="company"
        >
          <Input size="large" placeholder={t("BUSINESS_NAME")} />
        </AntForm.Item>

      </AntForm>
      <AntButton size="large" onClick={form.submit} className="pricing-business__btn">{t("SEND").toUpperCase()}</AntButton>
    </div>
  );
};

export default PricingBusiness;
