import { useTranslation } from "react-i18next";

import PAY_SUCCESS_ICON from "@src/asset/icon/pay/pay-success.svg";
import PAY_FAIL_ICON from "@src/asset/icon/pay/pay-false.svg";

import { VNPAY_RESPONSE_ERROR } from "@constant";

export default function PaymentComplete({ transactionState }) {
  const { t } = useTranslation();
  const { isSuccess, messageLang, transactionId } = transactionState;

  const isCancel = messageLang === VNPAY_RESPONSE_ERROR['24'];
  return (
    // <div className="payment__content__payment-complete">
    //   <img src={SALY_ICON} alt='' />
    //   {isSuccess ? <div>
    //     <div>{t('PAYMENT_SUCCESS')}</div>
    //     <div>{t('THANKS_FOR_COMPANION')}</div>
    //   </div> :
    //     <div>
    //       <div className="payment-complete__notification__fail">{t(messageLang)}</div>
    //       {!isCancel && <div>{t('APOLOGIZE_FOR_ANY_INCONVENIENCE')}</div>}
    //       <div>{t('CONTACT_HOTLINE_FOR_SUPPORT')}</div>
    //       <div>{`${t('YOUR_TRANSACTION_ID')}: ${transactionId}`}</div>
    //     </div>}
    // </div>
    <div className="payment__content__payment-complete">
      <img src={isSuccess ? PAY_SUCCESS_ICON : PAY_FAIL_ICON} alt='' />
      {isSuccess ? <div>
        <div>{t('PAYMENT_SUCCESS')}</div>
      </div> :
        <div>
          <div>{t('PAYMENT_FAIL')}</div>
          <div>
            {t('PLEASE_CONTACT')}
            <span className="payment-complete__hotline">0977.666.555</span>
            {t('FOR_SUPPORT')}
          </div>
        </div>}
    </div>
  )
}