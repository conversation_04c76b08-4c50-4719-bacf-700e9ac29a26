import Lottie from "lottie-react";
import * as loadingAnimation from "@src/asset/animations/blue-spin-loading.json";
import { useTranslation } from "react-i18next";

export default function PaymentProcessing({ transactionState }) {
  const { t } = useTranslation();
  return (
    <div className="payment__content__processing">
      <Lottie
        className="loading-spin"
        animationData={loadingAnimation}
        loop={true}
      />
      <div>
        <div>
          {t('PAYMENT_PROCESSING_NOTIFICATION')}
        </div>
        <div>
          {t("PLEASE_WAIT_A_MOMENT")}!
        </div>
      </div>
    </div>
  )
}