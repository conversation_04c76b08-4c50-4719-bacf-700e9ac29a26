import { useMemo, useState } from "react";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";

import { BUTTON, USER_TYPE } from "@constant"

import AntButton from "@src/app/component/AntButton";;

import PaymenPackagetInfo from "./PaymenPackagetInfo";
import PaymentDiscount from "./PaymentDiscount";
import PaymentCalculate from "./PaymentCalculate";
import FormOfPayment from "./FormOfPayment";

import { getLinkVnpay, buyMoreAddOnPackage } from "@src/app/services/Payment";
import RecurringPayment from "./RecurringPayment";

function PaymentContent({ user, ...props }) {

  const {
    packageData,
  } = props;
  const navigate = useNavigate();
  const { t } = useTranslation();
  const location = useLocation();
  const { state } = location;
  const { id } = useParams();
  const isRenew = id === user.subscription?.packageId?._id;
  const isStudent = user.type === USER_TYPE.STUDENT;

  const [applyDiscounts, setApplyDiscounts] = useState([]);
  const [isRecurring, setRecurring] = useState(false);

  const priceData = useMemo(() => {
    const { quantity, unitPrice } = state || {};
    if (isStudent && !quantity) return state;  //student + base package
    if (packageData) {
      if (quantity) return ({ ...packageData.prices[0], ...!isStudent && { quantity } }); // addon package
      return packageData?.prices?.find(price => price.unitName === unitPrice?.toLowerCase()); // teacher + base package
    }
    return null;
  }, [packageData, state]);

  const onCancel = () => {
    navigate(-1);
  }

  const onSubmit = async () => {
    const dataGetLink = {
      packageId: packageData._id,
      userId: user._id,
      discountIds: applyDiscounts?.map(discount => discount._id),
      price: priceData,
      isRenew: isRenew
    }
    const response = packageData.type === 'addon' ? await buyMoreAddOnPackage(dataGetLink) : await getLinkVnpay(dataGetLink);;
    if (response) {
      window.location.href = response.paymentUrl?.paymentUrl;
    }
  }
  const featureValue = Object.values(packageData?.features || {})[0];

  return <div className="payment__content__payment-package">
    <PaymenPackagetInfo packageData={packageData} {...state} isRenew={isRenew} />
    <PaymentDiscount setApplyDiscounts={setApplyDiscounts} applyDiscounts={applyDiscounts} />
    <PaymentCalculate
      priceData={priceData}
      applyDiscounts={applyDiscounts}
      featureValue={featureValue} />
    <RecurringPayment isRecurring={isRecurring} setRecurring={setRecurring} />
    <FormOfPayment />
    <div className="payment-package-actions">
      <AntButton onClick={onCancel} type={BUTTON.WHITE} size="large">{t("CANCEL")}</AntButton>
      <AntButton type={BUTTON.DEEP_NAVY} size="large" onClick={onSubmit} >{t("CONTINUE")}</AntButton>
    </div>
  </div>
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default (connect(mapStateToProps)(PaymentContent));