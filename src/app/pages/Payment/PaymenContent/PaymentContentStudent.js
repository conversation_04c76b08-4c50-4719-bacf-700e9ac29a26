import { useEffect, useMemo, useState } from "react";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";

import { BUTTON, PAYMENT_STATUS, USER_TYPE } from "@constant"

import AntButton from "@src/app/component/AntButton";;

import PaymentPackageInfoStudent from "./PaymenPackagetInfo/PaymentPackageInfoStudent";
import PaymentDiscountStudent from "./PaymentDiscount/PaymentDiscountStudent";
import PaymentCalculate from "./PaymentCalculate";
import FormOfPayment from "./FormOfPayment";

import RecurringPayment from "./RecurringPayment";

function PaymentContentStudent({ user, ...props }) {

  const {
    packageData,
    setActiveStep
  } = props;
  const navigate = useNavigate();
  const { t } = useTranslation();
  const location = useLocation();
  const { state } = location;

  const [applyDiscounts, setApplyDiscounts] = useState([]);
  const [isRecurring, setRecurring] = useState(false);
  const [isFreePayment, setIsFreePayment] = useState(false);

  const onCancel = () => {
    navigate(-1);
  }

  const onSubmit = async () => {
    const dataGetLink = {
      packageId: packageData._id,
      userId: user._id,
      discountIds: applyDiscounts?.map(discount => discount._id),
      price: state,
      priceIndex: state?.priceIndex
    }

  }
  const featureValue = Object.values(packageData?.features || {})[0];

  return <div className="payment__content__payment-package">
    <PaymentPackageInfoStudent packageData={packageData} {...state} />
    <PaymentDiscountStudent setApplyDiscounts={setApplyDiscounts} applyDiscounts={applyDiscounts} />
    <PaymentCalculate
      priceData={state}
      applyDiscounts={applyDiscounts}
      featureValue={featureValue}
      setIsFreePayment={setIsFreePayment} />
    {!isFreePayment && <>
      <RecurringPayment isRecurring={isRecurring} setRecurring={setRecurring} />
      <FormOfPayment />
    </>}
    <div className="payment-package-actions">
      <AntButton onClick={onCancel} type={BUTTON.WHITE} size="large">{t("CANCEL")}</AntButton>
      <AntButton type={BUTTON.DEEP_NAVY} size="large" onClick={onSubmit} >{t("CONTINUE")}</AntButton>
    </div>
  </div>
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default (connect(mapStateToProps)(PaymentContentStudent));
