import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import dayjs from "dayjs";

import { convertDateToText, renderTextTime } from "@common/functionCommons";
import { getSubscriptionDateInfo } from "@src/app/services/Payment";
import { getAllFeature } from "@services/Package";

import { CONSTANT, PACKAGE_TERM, USER_TYPE } from "@constant";
import TICKET_ICON from "@src/asset/icon/ticket/ticket-navy.svg";

function PaymenPackagetInfo({ user, ...props }) {
  const { packageData, unitPrice, quantity, isRenew } = props; // for teacher
  const { intervalCount, unitName } = props; //for student
  const { t, i18n } = useTranslation();
  const { subscription } = user;

  const isAddon = packageData?.type === CONSTANT.ADDON;
  const isStudent = user.type === USER_TYPE.STUDENT;
  const packageDuration = isAddon ? ''
    : isStudent ? renderTextTime(intervalCount, t(unitName?.toUpperCase()), i18n.language)
      : unitPrice === CONSTANT.MONTH ? t('MONTHLY') : t('YEARLY');

  const [packageDateInfo, setPackageDateInfo] = useState({
    startDate: dayjs(),
    endDate: dayjs().add(intervalCount || 1, unitPrice?.toLowerCase() || unitName)
  });
  const [features, setFeatures] = useState([]);

  useEffect(() => {
    if (isRenew) {
      getDateInfo();
    }
  }, [isRenew, unitPrice]);

  useEffect(() => {
    getFeaturesData();
  }, []);

  const getFeaturesData = async () => {
    const featureResponse = await getAllFeature();
    if (featureResponse) setFeatures(featureResponse);
  }

  const getDateInfo = async () => {
    const response = await getSubscriptionDateInfo({ subscriptionId: subscription?._id, unitPrice: unitPrice.toLowerCase() });
    if (response) setPackageDateInfo(response);
  }

  const featureUnit = useMemo(() => {
    if (packageData && features.length) {
      const featureId = Object.keys(packageData?.features)[0];
      return features.find((feature) => feature?._id === featureId)?.unit;
    }
    return null;
  }, [packageData, features]);

  function renderDescription() {
    if (isAddon) return t("PAYMENT_ADD_ON_PACKAGE_DESCRIPTION").format(quantity, featureUnit);
    return (<>
      {t('SERVICE_PACKAGE_START_FROM')}
      <span className="payment-package-info__date">{convertDateToText(packageDateInfo?.startDate)}</span>
      {t('TO_DATE')}
      <span className="payment-package-info__date">{convertDateToText(packageDateInfo?.endDate)}</span>
      {` (${packageDateInfo.endDate.diff(packageDateInfo.startDate, 'day')} ${t("USING_DATE")})`}
    </>)
  }

  return <div className="payment-package-item">
    <div className="payment-package-info">
      <div className="payment-package-item__label">
        <img src={TICKET_ICON} alt="" />
        {t('PAYNMENT_SERVICE_PACKAGE')}
      </div>
      <div className="payment-package-info__name">
        {packageData?.name}
        {!!packageDuration && <span className="payment-package-info__package-duaration">{packageDuration}</span>}
      </div>
      <div className="payment-package-info__description">{renderDescription()}</div>
    </div>
  </div>
}

const mapStateToProps = (state) => {
  const { user } = state.auth;
  return { user };
};

const connector = connect(mapStateToProps, {});

export default connector(PaymenPackagetInfo);