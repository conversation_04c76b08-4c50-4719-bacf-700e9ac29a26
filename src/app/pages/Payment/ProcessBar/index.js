import { Timeline, Avatar } from 'antd';
import { useTranslation } from "react-i18next";

import CheckGreen from '@component/SvgIcons/Check/CheckGreen';
import clsx from 'clsx';

export default function ProcessBar({ activeStep }) {
  const { t } = useTranslation();
  const renderStep = (item, lastKey) => {
    const { key, label } = item;

    const isDone = key < activeStep;
    const isActive = key === activeStep;

    return <div className={clsx('process-bar__step', { 'process-bar__step-active': isActive, 'process-bar__step-done': isDone })} key={key}>
      <div className='step-content'>
        <Avatar className='step-content__icon'>
          {isDone ? <CheckGreen /> : key}
        </Avatar>
        <div className='step-content__label'>{label}</div>
      </div>
      {key !== lastKey && <div className='step-line'></div>}
    </div >
  }

  const items = [
    {
      label: t('SELECT_PACKAGE'),
      key: 1,
    },
    {
      label: t('PAYMENT_METHOD'),
      key: 2,
    },
    {
      label: t('PAY'),
      key: 3,
    },
    {
      label: t('PROCESSING'),
      key: 4,
    },
    {
      label: t('COMPLETED'),
      key: 5,
    },
  ]
  return (
    <div className="payment__process-bar">
      {items.map(item => renderStep(item, items.length))}
    </div>
  )
}