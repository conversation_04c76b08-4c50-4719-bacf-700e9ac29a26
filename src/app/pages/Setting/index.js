import { useEffect, useRef } from "react";
import { connect } from "react-redux";
import { Col, Form, Input, Row, Select, Space } from "antd";
import { useTranslation } from "react-i18next";
import { usePageViewTracker } from "@src/ga";

import { toast } from "@component/ToastProvider";

import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";
import { BUTTON, CATEGORIES, CONSTANT } from "@constant";

import Copy from "@component/SvgIcons/Copy";
import html2canvas from "html2canvas";


function Setting() {
  usePageViewTracker("Settings");
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const elementRef = useRef(null);
  
  useEffect(() => {
    form.setFieldsValue({ tags: "123", test: "123" });
  }, []);
  
  function handleShow(type) {
    const config = { description: t("LOGIN_SESSION_EXPIRED"), key: "LOGIN_SESSION_EXPIRED" };
    if (type === CONSTANT.ERROR)
      toast.error(config);
    if (type === CONSTANT.SUCCESS)
      toast.success("LOGIN_SESSION_EXPIRED");
    if (type === CONSTANT.DENIED)
      toast.denied("PERMISSION_DENIED", { unique: true });
  }
  
  function handleDestroy() {
    const exist = toast.destroy("LOGIN_SESSION_EXPIRED");
    console.log("exist", exist);
  }
  
  function onFinish(values) {
    console.log("values", values);
  }
  
  async function onCaptureScreen() {
    const headerHeight = document.getElementById("header").offsetHeight;
    const layoutPaddingHeight = 32 * 2;
    const pageHeight = elementRef.current.offsetHeight + headerHeight + layoutPaddingHeight;
    const pageWidth = document.body.clientWidth;
    const image = await html2canvas(document.body, {
      windowHeight: pageHeight,
      windowWidth: pageWidth,
      allowTaint: true,
      useCORS: true,
    });
    const dataURL = image.toDataURL();
    const link = document.createElement("a");
    link.href = dataURL;
    link.download = "page-screenshot.png";
    link.click();
  }
  
  const buttonList = [];
  
  for (let i = 0; i < Object.entries(BUTTON).length; i += 3) {
    let chunk = Object.entries(BUTTON).slice(i, i + 3);
    buttonList.push(chunk);
  }
  
  
  return <div ref={elementRef}>
    
    <Space direction="vertical">
      
      <Space direction="horizontal">
        <AntButton size="large" type={BUTTON.GHOST_BLUE} icon={<Copy />}>GHOST_BLUE</AntButton>
        <AntButton size="large" type={BUTTON.LIGHT_BLUE} icon={<Copy />}>LIGHT_BLUE</AntButton>
        <AntButton size="large" type={BUTTON.DEEP_BLUE} icon={<Copy />}>DEEP_BLUE</AntButton>
      </Space>
      
      <Space direction="horizontal">
        <AntButton size="large" type={BUTTON.GHOST_NAVY} icon={<Copy />}>GHOST_NAVY</AntButton>
        <AntButton size="large" type={BUTTON.LIGHT_NAVY} icon={<Copy />}>LIGHT_NAVY</AntButton>
        <AntButton size="large" type={BUTTON.DEEP_NAVY} icon={<Copy />}>DEEP_NAVY</AntButton>
      </Space>
      
      <Space direction="vertical">
        
        {buttonList.map((list, index) => {
          return <Space key={index}>
            {list.map(([key, value]) => {
              return <AntButton size="large" key={key} type={value} icon={<Copy />}>{key}</AntButton>;
            })}
          </Space>;
        })}
      </Space>
      
      {/*<div>
       {Object.entries(BUTTON).map(([key, value]) => {
       return <AntButton size="large" key={key} type={value} icon={<Copy />}>{key}</AntButton>;
       })}
       
       <AntButton
       size="large"
       type={BUTTON.WHITE}
       icon={<Copy />}
       bordered
       >
       WHITE
       </AntButton>
       
       </div>*/}
      
      <AntButton size="compact" icon={<Copy />} />
      <AntButton size="xsmall" icon={<Copy />} />
      <AntButton size="mini" icon={<Copy />} />
      <AntButton size="tiny" icon={<Copy />} />
      
      <AntButton size="large" icon={<Copy />}>compact</AntButton>
      <AntButton icon={<Copy />}>compact</AntButton>
      <AntButton size="small" icon={<Copy />}>compact</AntButton>
      <AntButton size="compact" icon={<Copy />}>compact</AntButton>
      <AntButton size="xsmall" icon={<Copy />}>xsmall</AntButton>
      <AntButton size="mini" icon={<Copy />}>mini</AntButton>
      <AntButton size="tiny" icon={<Copy />}>tiny</AntButton>
      
      
      <Row>
        <Col span={8}>
          <AntForm
            form={form}
            layout="vertical"
            onFinish={onFinish}
            //onFieldsChange={onFieldsChange}
            //onValuesChange={onValuesChange}
          >
            
            <AntForm.TagItem
              name="tags" label="tags"
              max={10}
              placeholder="Tags Mode"
            />
            
            <AntForm.TagItem
              name="tags1" label="tags1"
            />
            
            <AntButton
              size="large"
              type={BUTTON.DEEP_NAVY}
              htmlType="submit"
            >
              {t("SUBMIT")}
            </AntButton>
            
            <AntButton
              size="large"
              type={BUTTON.DEEP_NAVY}
              onClick={() => {
                form.setFieldsValue({
                  tags: "123",
                  test: "123",
                });
              }}
            >
              {t("FILL")}
            </AntButton>
          
          </AntForm>
        </Col>
      </Row>
      
      
      <Row>
        <Col span={8}>
          <Select
            mode="tags"
            style={{ width: "100%" }}
            placeholder="Tags Mode"
            //onChange={handleChange}
            //options={options}
            showSearch={false}
          />
        </Col>
      </Row>
      
      <Input size="small" value="123456" />
      <Input value="123456" />
      <Input size="large" value="123456" />
      <Input size="large" placeholder="123456" />
      
      <Row>
        <Col span={8}>
          <Select size="small" options={CATEGORIES} mode="multiple" allowClear style={{ width: "100%" }} />
        </Col>
      </Row>
      <Row>
        <Col span={8}>
          <Select options={CATEGORIES} mode="multiple" showSearch allowClear style={{ width: "100%" }} />
        </Col>
      </Row>
      <Row>
        <Col span={8}>
          <Select size="large" options={CATEGORIES} mode="multiple" placeholder="Listening" showSearch
                  style={{ width: "100%" }} />
        </Col>
      </Row>
      <Row>
        <Col span={8}>
          <Select options={CATEGORIES} placeholder="123456" showSearch style={{ width: "100%" }} />
        </Col>
      </Row>
    
    
    </Space>
    
    
    {/*<Button ghost>ghost</Button>*/}
    {/*<Button ghost className="ant-btn-warning-ghost">warning</Button>*/}
    {/*<Button>2</Button>*/}
    {/*<Button>3</Button>*/}
    {/*<Button>4</Button>*/}
    {/*<Button>5</Button>*/}
    {/*<Button>6</Button>*/}
    {/*<Button>7</Button>*/}
  
  </div>;
}

function mapStateToProps(store) {
  const { theme } = store.app;
  return { theme };
}

export default connect(mapStateToProps)(Setting);
