import React, { useEffect, useState } from "react";
import { Card, Col, Form, Input, InputNumber, Row, Select, Switch, Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { InfoCircleOutlined } from "@ant-design/icons";
import { getDetailSetting, updateSetting } from "@services/Settings";

import "./Setting.scss";
import AntButton from "@component/AntButton";
import { BUTTON } from "@constant";
import { toast } from "@component/ToastProvider";
import { connect } from "react-redux";
import { confirm } from "@component/ConfirmProvider";
import Loading from "@component/Loading";


const Settings = ({  ...props }) => {
  const { t } = useTranslation();
  const [settingData, setSettingData] = useState({});
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getSetting();
  }, []);

  async function getSetting() {
    setLoading(true);
    const apiResponse = await getDetailSetting();
    if (apiResponse) {
      setSettingData(apiResponse);
      form.setFieldsValue(apiResponse);
    }
    setLoading(false);
  }

  const onFinish = async (values) => {
    // Xác nhận khi thay đổi các cấu hình quan trọng
    if (values.apiKeyOpenAI !== settingData.apiKeyOpenAI ||
        values.speechKey !== settingData.speechKey) {
      const confirmed = await confirm({
        title: t("CONFIRM_UPDATE_SETTINGS"),
        content: t("CONFIRM_UPDATE_SENSITIVE_SETTINGS"),
        okText: t("UPDATE"),
        cancelText: t("CANCEL")
      });

      if (!confirmed) return;
    }

    setLoading(true);
    const updatedValues = {
      ...values,
      ...(values.apiKeyOpenAI === settingData.apiKeyOpenAI && { apiKeyOpenAI: undefined }),
      ...(values.speechKey === settingData.speechKey && { speechKey: undefined }),
    };

    const apiResponse = await updateSetting(updatedValues);
    if (apiResponse) {
      toast.success(t("UPDATE_SETTING_SUCCESS"));
      getSetting();
    } else {
      toast.error(t("UPDATE_SETTING_ERROR"));
    }
    setLoading(false);
  };

  return (
    <Loading active={loading} transparent>
      <div className="form-setting">
        <div className="form-setting-header">
          <h1 className="settings-title">{t("SYSTEM_SETTINGS")}</h1>
          <p className="settings-description">{t("SYSTEM_SETTINGS_DESCRIPTION")}</p>
        </div>

        <Form
          onFinish={onFinish}
          layout="vertical"
          form={form}
          size={"large"}
          className="settings-form"
        >
          {/* API Integration Settings */}
          <Card
            className="settings-card"
            title={t("API_INTEGRATION")}
            headStyle={{ fontWeight: 600 }}
          >
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  label={
                    <span>
                      {t("API_KEY_OPENAI")}
                      <Tooltip title={t("API_KEY_OPENAI_DESC")}>
                        <InfoCircleOutlined className="info-icon" />
                      </Tooltip>
                    </span>
                  }
                  name="apiKeyOpenAI"
                  rules={[{ required: true, message: t("API_KEY_OPENAI_REQUIRED") }]}
                >
                  <Input placeholder={t("ENTER_API_KEY_OPENAI")} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label={
                    <span>
                      {t("SPEECH_KEY")}
                      <Tooltip title={t("SPEECH_KEY_DESC")}>
                        <InfoCircleOutlined className="info-icon" />
                      </Tooltip>
                    </span>
                  }
                  name="speechKey"
                >
                  <Input placeholder={t("ENTER_SPEECH_KEY")} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label={
                    <span>
                      {t("SERVICE_REGION")}
                      <Tooltip title={t("SERVICE_REGION_DESC")}>
                        <InfoCircleOutlined className="info-icon" />
                      </Tooltip>
                    </span>
                  }
                  name="serviceRegion"
                >
                  <Input placeholder={t("ENTER_SERVICE_REGION")} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label={
                    <span>
                      {t("AZURE_ENDPOINT")}
                      <Tooltip title={t("AZURE_ENDPOINT_DESC")}>
                        <InfoCircleOutlined className="info-icon" />
                      </Tooltip>
                    </span>
                  }
                  name="azureEndpoint"
                >
                  <Input placeholder={t("ENTER_AZURE_ENDPOINT")} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label={
                    <span>
                      {t("GOOGLE_API_KEY")}
                      <Tooltip title={t("GOOGLE_API_KEY_DESC")}>
                        <InfoCircleOutlined className="info-icon" />
                      </Tooltip>
                    </span>
                  }
                  name="googleApiKey"
                >
                  <Input placeholder={t("ENTER_GOOGLE_API_KEY")} />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* User Limits Settings */}
          <Card
            className="settings-card"
            title={t("USER_LIMITS")}
            headStyle={{ fontWeight: 600 }}
          >
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  label={
                    <span>
                      {t("USER_LIMIT")}
                      <Tooltip title={t("USER_LIMIT_DESC")}>
                        <InfoCircleOutlined className="info-icon" />
                      </Tooltip>
                    </span>
                  }
                  name="limitedNumberUser"
                  rules={[{ required: true, message: t("USER_LIMIT_REQUIRED") }]}
                >
                  <InputNumber
                    placeholder={t("ENTER_USER_LIMIT")}
                    min={0}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label={
                    <span>
                      {t("FREE_SUBMISSION")}
                      <Tooltip title={t("FREE_SUBMISSION_DESC")}>
                        <InfoCircleOutlined className="info-icon" />
                      </Tooltip>
                    </span>
                  }
                  name="userFreeSubmit"
                >
                  <InputNumber
                    placeholder={t("ENTER_FREE_SUBMISSION")}
                    min={0}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label={
                    <span>
                      {t("PAID_SUBMISSION")}
                      <Tooltip title={t("PAID_SUBMISSION_DESC")}>
                        <InfoCircleOutlined className="info-icon" />
                      </Tooltip>
                    </span>
                  }
                  name="userPaidSubmit"
                >
                  <InputNumber
                    placeholder={t("ENTER_PAID_SUBMISSION")}
                    min={0}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          <div className="settings-actions">
            <AntButton
              htmlType="submit"
              type={BUTTON.DEEP_NAVY}
              loading={loading}
              className="settings-save-button"
            >
              {t("SAVE")}
            </AntButton>
          </div>
        </Form>
      </div>
    </Loading>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {
};

export default connect(mapStateToProps, mapDispatchToProps)(Settings);
