.form-setting {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  background-color: var(--background-light-background-2);
  border-radius: 8px;
  font-family: Segoe UI;

  .form-setting-header {
    margin-bottom: 16px;

    .settings-title {
      font-weight: 700;
      font-size: 20px;
      line-height: 24px;
      color: var(--typo-colours-primary-black);
      margin-bottom: 8px;
    }

    .settings-description {
      font-size: 14px;
      line-height: 20px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }
  }

  .settings-form {
    .ant-form-item {
      margin-bottom: 24px;

      .ant-form-item-label > label {
        display: flex;
        align-items: center;

        .info-icon {
          margin-left: 6px;
          color: var(--typo-colours-secondary-grey);
          font-size: 14px;
        }
      }
    }

    .settings-card {
      border-radius: 8px;
      box-shadow: var(--shadow-level-1);
      margin-bottom: 24px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: var(--shadow-level-2);
      }

      .ant-card-head {
        border-bottom: 1px solid var(--background-light-background-grey);

        .ant-card-head-title {
          font-size: 16px;
          color: var(--typo-colours-primary-black);
        }
      }

      .ant-card-body {
        padding: 24px;
      }
    }

    .settings-actions {
      display: flex;
      justify-content: center;
      margin-top: 32px;

      .settings-save-button {
        min-width: 120px;
        height: 40px;
        font-weight: 600;
      }
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .settings-card {
      .ant-card-body {
        padding: 16px;
      }
    }
  }
}