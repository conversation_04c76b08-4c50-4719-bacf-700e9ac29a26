.tool-studio {
  .tool-studio-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .tool-studio-header__title {
      font-size: 16px;
      font-weight: 700;
    }
  }

  .tool-studio-menu {
    color: rgba(0, 0, 0, 0.88);

    .ant-menu-item {
      padding-inline: 24px;

      &.ant-menu-item-active {
        color: rgba(0, 0, 0, 0.88) !important;

        &::after {
          border-bottom-width: 2px;
          border-bottom-color: #2196f3;
          inset-inline: 24px;
        }
      }

      &.ant-menu-item-selected {
        color: #2196f3 !important;

        &::after {
          border-bottom-width: 2px;
          border-bottom-color: #2196f3;
          inset-inline: 24px;
        }
      }
    }
  }

  .save-button {
    text-align: center;
    padding-top: 10px;
  }

  .form-filter {
    margin-top: -8px;

    .ant-form-item {
      margin-bottom: 0;

      .ant-input {
        padding: 5px 11px;
      }

      .ant-select {
        height: 32px;
      }
    }

    .search-button {
      display: flex;
      align-items: end;
      padding-left: 10px;

      button {
        padding: 5px 11px;
      }
    }
  }

  .ant-pagination {
    align-items: center;
  }
  .ant-pagination-item{
    color: #2196f3 !important;
  }
}
