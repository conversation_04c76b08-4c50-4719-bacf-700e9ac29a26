:root {
  --project-content__value-input-bg: #FFF;
  --account__content-item-color: #000;
  --divider-bg: #E6E6E6;
}

#account-student {
  display: flex;
  flex-direction: column;
  gap: 16px;

  padding: 32px 0px;
  border-radius: 24px;
  background: radial-gradient(18.71% 33.59% at 50% 8.03%, #F4F3FF 0.01%, #FFFFFF 100%);
  box-shadow: 0px 4px 20px 0px #0000001A;
  margin-top: 16px;

  .account-student__header {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: auto;
    gap: 8px;
    max-width: 744px;
    width: 100%;

    .account-student__header__content {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      line-height: 28px;
      background: linear-gradient(to right, #0c4da2, #9c3de4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .account__content {
    max-width: 744px;
    width: 100%;
    display: flex;
    padding: 40px;
    background: radial-gradient(18.71% 33.59% at 50% 8.03%, #F4F3FF 0.01%, #FFFFFF 100%);
    box-shadow: 0px 4px 20px 0px #0000001A;
    border-radius: 12px;
    flex-direction: column;
    align-items: flex-start;
    margin: auto;

    @media screen and (max-width: 900px) {
      width: 450px;
    }

    .account__content__item {
      display: flex;
      flex-wrap: wrap;
      font-size: 16px;
      width: 100%;
      line-height: 24px;

      &:not(:last-child) {
        &::after {
          content: '';
          width: 100%;
          height: 1px;
          background: var(--divider-bg);
          margin: 16px 0;
        }
      }

      .item__label {
        font-weight: 500;
        width: 240px;
        // color: var(--primary-colours-blue-navy);
        color: #0c4da2;
        padding-right: 40px;

        @media screen and (max-width: 900px) {
          width: 120px;
        }
      }

      .item__value {
        display: flex;
        font-weight: 400;
        word-break: break-word;

        .edit-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          cursor: pointer;

          svg {
            width: 18px;
            height: 18px;

            path {
              stroke-width: 1;
            }
          }
        }

        &.item__value-name {
          gap: 16px;
        }

        &.password {
          flex-direction: column;
          align-items: flex-start;

          .link-change-password {
            font-size: 14px;
            line-height: 20px;
            color: var(--typo-colours-support-blue);
            cursor: pointer;
          }
        }

        &.item__value-workspace {
          display: flex;
          flex-direction: column;
          gap: 4px;


          .description-input {
            display: flex;
            max-width: 100%;
            gap: 8px;
            align-items: flex-start;

            &.description-input-nodata {
              cursor: pointer;
              display: flex;

              color: var(--typo-colours-support-blue);

              svg path {
                stroke: var(--typo-colours-support-blue);
              }
            }

            .description-input__value {
              display: flex;
              align-items: center;
              height: 18px;


            }

          }
        }

        &.item__value-packages {
          display: flex;
          flex-direction: column;
          gap: 24px;

          .item__value-packages__item {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .package-name {
              display: flex;
              gap: 16px;

              .package-name__text {
                font-size: 22px;
                font-weight: 600;
                line-height: 30px;
                text-align: left;
                text-underline-position: from-font;
                text-decoration-skip-ink: none;
                text-transform: uppercase;
              }

              .btn-upgrade {
                padding: 4px 16px;
                border-radius: 4px;
                font-family: Inter;
                font-size: 16px;
                font-weight: 600;
                line-height: 24px;
                text-align: right;
                text-underline-position: from-font;
                text-decoration-skip-ink: none;
                text-transform: uppercase;
              }
            }

            .package-term {
              display: flex;
              flex-direction: column;
              gap: 8px;

              ul {
                margin: 0;
                padding-inline-start: 26px;
              }

            }

            .package-quota {
              display: flex;
              gap: 4px;
              padding: 0 12px;

              .package-quota__item {
                padding: 2px 8px;
                border-radius: 4px;
                background: #E7E5FF;
                color: #3A18CE
              }
            }
          }
        }

        .link-payment-history {
          color: var(--typo-colours-support-blue);
        }
      }
    }

    .item-editing-input {
      display: flex;
      flex-direction: row;
      gap: 18px;
    }
  }

  .btn-upgrade {
    text-align: center;

    button {
      padding: 10px 32px 10px 32px;
      border-radius: 8px;
      font-weight: 600;
      height: auto;
    }
  }
}

.chart-remaning-container {
  display: flex;
  flex-direction: row;
  gap: 24px;
  font-family: Segoe UI;

  .chart-remaning-item {
    display: flex;

    flex-direction: column;
    width: 50%;
    padding: 24px;
    box-shadow: var(--shadow-level-2);
    border-radius: 8px;
    gap: 32px;
    background-color: var(--background-light-background-2);
  }

  .chart-remaning-info {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .chart-remaning-info__left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .chart-remaning-info__right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
  }

  .chart-remaning-info__title {
    font-size: 16px;
    line-height: 20px;
    font-weight: 600;
  }

  .chart-remaning-info__description {
    font-weight: 400;
    font-size: 14px;
    line-height: 17.5px;
    color: var(--typo-colours-support-blue-light)
  }

  .chart-remaning-info__avalibled {
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
  }

  .chart-remaning-info__exp {
    display: flex;
    flex-direction: row;
  }

  .chart-remaning-info__exp {
    font-weight: 400;
    font-size: 14px;
    line-height: 17.5px;
    color: var(--typo-colours-support-blue-light)
  }

  .chart-remaning-info__expTitle {
    color: var(--support-colours-red)
  }

  .chart-remaning-info__exp-link {
    color: var(--typo-colours-support-blue);
    cursor: pointer;
  }

}