@import "src/app/styles/scroll";

.exchange-container {
  display: flex;
  min-width: fit-content;
  width: 100%;
  gap: 16px;
  padding: 16px;
  border-radius: 16px;
  background: radial-gradient(27.17% 27.17% at 48.69% 0%, #F4F3FF 0%, #FFFFFF 100%);

  .transaction-list {
    display: flex;
    flex-grow: 1;

    @extend .scrollbar;
    @extend .scrollbar-show;

    max-height: calc(100vh - 136px);

    .ant-table-wrapper {
      width: 100%;
    }

    .ant-table {
      border-radius: 8px;
      background: unset;

      table {
        width: 100%;
      }
    }

    .ant-table-thead {
      position: relative;
      gap: 4px;


      th::before {
        display: none;
      }

      .ant-table-cell {
        background: unset;
        padding: 16px;
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0px;
        color: var(--primary-colours-blue-navy);

      }
    }

    .ant-table-row {
      cursor: pointer;

      &:hover {
        .ant-table-cell {
          background: unset !important;
        }
      }

      &.transaction-selected {
        .ant-table-cell {
          background: var(--puple-backgound) !important;

          .package-info__unit-price {
            background-color: #3A18CE;
            color: #FFFFFF;
          }
        }
      }

      .ant-table-cell {
        background: unset;
        padding: 16px;

        .package-info {
          display: flex;
          gap: 8px;
          align-items: center;

          &.package-info__active::before {
            content: '';
            display: block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #26D06D;
          }
        }
      }

    }

    .transaction-status-done {
      color: var(--typo-colours-support-blue)
    }

    .transaction-status-error {
      color: var(--support-colours-red-dark)
    }

    .transaction-status-cancel {
      color: var(--typo-colours-support-yellow)
    }

    .transaction-status-processing {
      color: var(--support-colours-green-dark)
    }
  }

  .transaction-detail {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 16px;
    border-radius: 16px;
    box-shadow: 0px 4px 20px 0px #0000001A;
    max-width: 360px;
    height: fit-content;

    .transaction-detail__package-info {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .package-info__name {
        display: flex;
        gap: 8px;
        align-items: center;
        font-weight: 600;
        font-size: 18px;
        line-height: 24px;
        letter-spacing: 0px;

        .package-info__unit-price {
          font-weight: 400;
          background-color: #3A18CE;
          color: #FFFFFF;
        }
      }

      .package-info__term {
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0px;
      }
    }

    .transaction-detail__item {
      display: flex;
      flex-direction: column;
      gap: 8px;
      font-size: 14px;

      .content__item__title {
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0px;
        color: var(--primary-colours-blue-navy);
      }

      .payment-info {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .payment-info__item {
          display: flex;
          justify-content: space-between;

          &.payment-info__discount {
            padding-left: 16px;
            color: var(--typo-colours-support-blue-light);

            .payment-info__item__label {
              font-weight: 400;
            }
          }
        }
      }

      .hotline-support {
        color: #26D06D;
      }
    }

    .resume-payment-btn{
      display: flex;
      justify-content: center;
    }
  }

  .package-info__unit-price {
    padding: 2px 8px;
    border-radius: 4px;
    background-color: #E7E5FF;
    color: #3A18CE;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0px;
  }

  .automatic-payment-ratio {
    &:not(.ant-radio-wrapper-disabled) {
      .ant-radio-inner {
        border-color: var(--typo-colours-primary-black);
      }
    }

    &.ant-radio-wrapper-checked {
      .ant-radio-inner {
        border-color: var(--support-colours-green-dark);
        background-color: #FFFFFF;

        &:after {
          background-color: var(--support-colours-green-dark);
        }
      }
    }
  }
}