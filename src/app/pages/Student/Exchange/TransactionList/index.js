import { Radio, Table } from "antd";
import { useTranslation } from 'react-i18next';
import clsx from "clsx";
import dayjs from "dayjs";

import { CONSTANT, PACKAGE_TYPE, TRANSACTION_STATUS } from "@constant";

import { formatDate, renderMoney } from "@src/common/functionCommons";

const TransactionList = ({ ...props }) => {
  const { t } = useTranslation();
  const { transactionData, transactionActive, onChangeTransaction } = props;

  const renderTransactionStatus = (record) => {
    const { state, responseCode, vnpExpireDate } = record;
    let statusLang = '';
    switch (state) {
      case TRANSACTION_STATUS.DONE: statusLang = "SUCCESSFUL"; break;
      case TRANSACTION_STATUS.ERROR: if (responseCode === "24") {
        statusLang = "CANCELED";
      } else {
        statusLang = "FAILED";
      } break;
      case TRANSACTION_STATUS.PROCESSING: if (dayjs().isAfter(dayjs(vnpExpireDate))) {
        statusLang = "EXPIRED";
      } else {
        statusLang = "PROCESSING";
      }; break;
      default: statusLang = "PROCESSING";
    }
    return <span>{t(statusLang)}</span>
  }

  const columns = [
    {
      title: t("PACKAGE_NAME"),
      key: "PACKAGE_NAME",
      render: (text, record) => {
        const packageName = record.subscriptionId?.packageId?.name;
        return <div className={clsx("package-info", { "package-info__active": record?.subscriptionId?.status === CONSTANT.ACTIVE })}>
          {packageName}
          {record?.packageId?.type === PACKAGE_TYPE.BASE.value && <span className="package-info__unit-price">{`${record?.subscriptionId?.intervalCount} ${record?.unitPrice}`}</span>}
        </div>
      },
    },
    {
      title: t('TOTAL_PAYMENT'),
      key: "TOTAL_PAYMENT",
      dataIndex: "cost",
      render: (value) => {
        return renderMoney(Math.ceil(value));
      },
    },
    {
      title: t('DATE_PAYMENT'),
      key: "DATE_PAYMENT",
      render: (text, record) => {
        return formatDate(record?.createdAt);
      },
    },
    {
      title: t('STATUS'),
      key: "STATUS",
      render: (value, record) => {
        return renderTransactionStatus(record);
      },
    },
    {
      title: t('AUTOMATIC_PAYMENT'),
      key: "AUTOMATIC_PAYMENT",
      align: "center",
      render: (value, record) => {
        return <Radio className="automatic-payment-ratio" checked={record?.isRecurring} />;
      },
    },
  ];

  return <div className={clsx("transaction-list", !transactionData?.length && "width-full")}>
    <Table
      columns={columns}
      dataSource={transactionData}
      pagination={false}
      rowClassName={record => record?._id === transactionActive?._id ? "transaction-selected" : ""}
      onRow={record => {
        return {
          onClick: () => {
            onChangeTransaction(record);
          },
        };
      }}
    />
  </div>
};

export default TransactionList;