import React from "react";
import {<PERSON>rowserRouter, Navigate, Route, Routes} from "react-router-dom";
import {connect, shallowEqual, useSelector} from "react-redux";

import App from "@app/App";
import ExtraRouting from "@app/routing/ExtraRouting";
import PrivateRoutes from "@app/routing/PrivateRoutes";
import Logout from "@app/auth/Logout";
import ResetPassword from "@app/auth/components/ResetPassword";
import AuthPage, {AuthLayout} from "@app/auth/AuthPage";
import LoginGoogle from "@app/auth/components/LoginGoogle";
import ConfirmRegister from "@src/app/auth/components/Invitation/ConfirmRegister";
import ConfirmInvitation from "@src/app/auth/components/Invitation/ConfirmInvitation";
import RejectInvitation from "@src/app/auth/components/Invitation/RejectInvitation";
import ActivateAccount from "@src/app/auth/components/ActivateAccount";

import {LINK} from "@link";
import {CONSTANT, USER_TYPE} from "@constant";
import SelectUserType from "../pages/SelectUserType";


const AppRoutes = ({user}) => {
  const isAuthorized = useSelector(({auth}) => auth.user !== CONSTANT.INITIAL ? auth.user : false, shallowEqual);

  function renderLoginRoute() {
    const isAuthRoute = window.location.pathname.includes('auth');
    if (!isAuthRoute) {
      localStorage.setItem('redirectUrl', window.location.pathname);
    } else {
      localStorage.removeItem('redirectUrl');
    }

    return (
      <>
        <Route path="auth/login-google" element={<LoginGoogle/>}/>
        <Route path="auth/*" element={<AuthPage/>}/>
        <Route path="*" element={<Navigate to="/auth" replace/>}/>
      </>
    );
  }

  function renderMainRoute() {
    // if (!user.persona?.length) {
    //   return <>
    //     <Route path="*" element={<Navigate to="/" />} />
    //     <Route path="/" element={<SuggestiveQuestion />} />
    //   </>;
    // }

    if (!user?.type) {
      return <>
        <Route element={<AuthLayout/>}>
          <Route path="*" element={<Navigate to="/"/>}/>
          <Route path="/" element={<SelectUserType/>}/>
        </Route>
      </>;
    }

    const defaultRoute = user?.type === USER_TYPE.STUDENT ? LINK.COURSES : LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT;

    return <>
      <Route path="/*" element={<PrivateRoutes/>}/>
      {/* navigate from SuggestiveQuestion */}
      <Route index element={<Navigate to={defaultRoute}/>}/>
    </>;
  }

  return (
    <BrowserRouter basename="/">
      <ExtraRouting/>
      <Routes>
        <Route element={<App/>}>
          {/*<Route path="error/*" element={<ErrorsPage/>}/>*/}
          <Route path="logout" element={<Logout/>}/>
          <Route path="reset-password" element={<AuthLayout/>}>
            <Route path="" element={<ResetPassword/>}/>
          </Route>
          <Route path={LINK.CONFIRM_REGISTER} element={<AuthLayout/>}>
            <Route path="" element={<ConfirmRegister/>}/>
          </Route>
          <Route path={LINK.CONFIRM_INVITATION} element={<ConfirmInvitation/>}/>
          <Route path={LINK.REJECT_INVITATION} element={<RejectInvitation/>}/>
          <Route path={LINK.ACTIVATE_ACCOUNT} element={<ActivateAccount/>}/>

          {isAuthorized
            ? renderMainRoute()
            : renderLoginRoute()}
        </Route>
      </Routes>
    </BrowserRouter>
  );
};

function mapStateToProps(store) {
  const {user} = store.auth;
  return {user};
}

export default connect(mapStateToProps)(AppRoutes);
