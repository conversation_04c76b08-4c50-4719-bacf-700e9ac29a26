.scrollbar {
  scroll-behavior: smooth;
  overflow: overlay;
  @supports (-moz-appearance:none) {
    overflow: scroll;
    scrollbar-width: thin;
  }

  &.show-scrollbar::-webkit-scrollbar,
  &:hover::-webkit-scrollbar {
    display: block;
  }

  &::-webkit-scrollbar {
    display: none;
    width: 6px;
    height: 6px;
    margin-right: 12px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #aaa;
    border-radius: 6px;

    &:hover {
      background-color: #999;
    }
  }
}

// Thêm mixin scrollbar để có thể dùng @include
@mixin scrollbar {
  scroll-behavior: smooth;
  overflow: overlay;
  @supports (-moz-appearance:none) {
    overflow: scroll;
    scrollbar-width: thin;
  }

  &:hover::-webkit-scrollbar {
    display: block;
  }

  &::-webkit-scrollbar {
    display: none;
    width: 6px;
    height: 6px;
    margin-right: 12px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #aaa;
    border-radius: 6px;

    &:hover {
      background-color: #999;
    }
  }
}

.scrollbar-show::-webkit-scrollbar {
  display: block;
}

.table-scrollbar .ant-table-body {
  @extend .scrollbar;
  @extend .scrollbar-show;
}

.ant-modal-wrap {
  @extend .scrollbar;
  @extend .scrollbar-show;


  &::-webkit-scrollbar-thumb {
    background-color: #d2caca;
    border-radius: 6px;

    &:hover {
      background-color: #cec9c9;
    }
  }
}
