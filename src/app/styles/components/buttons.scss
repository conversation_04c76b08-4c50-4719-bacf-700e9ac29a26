// Global Button Styles for Professional Look
// Ensures all buttons have consistent borders and hover effects

// Base button styling
.ant-btn {
  // Ensure all buttons have borders by default
  &:not(.ant-btn-link):not(.ant-btn-text) {
    //border: 1px solid #d9d9d9 !important;
    transition: all 0.3s ease;

    &:hover {
      border-color: #40a9ff !important;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &:focus {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &:active {
      transform: translateY(0);
    }
  }

  // Default button styling
  &.ant-btn-default {
    background-color: #fff;
    color: #595959;

    &:hover {
      background-color: #f5f5f5;
      color: #262626;
    }

    &:focus {
      background-color: #fff;
      color: #262626;
    }
  }

  // Primary button styling
  &.ant-btn-primary {
    background-color: #1890ff;
    border-color: #1890ff !important;
    color: #fff;

    &:hover {
      background-color: #40a9ff;
      border-color: #40a9ff !important;
    }

    &:focus {
      background-color: #096dd9;
      border-color: #096dd9 !important;
    }
  }

  // Danger button styling
  &.ant-btn-dangerous {
    background-color: #fff;
    border-color: #ff4d4f !important;
    color: #ff4d4f;

    &:hover {
      background-color: #fff2f0;
      border-color: #ff7875 !important;
      color: #ff7875;
    }

    &:focus {
      background-color: #fff2f0;
      border-color: #cf1322 !important;
      color: #cf1322;
    }
  }

  // Ghost button styling
  &.ant-btn-ghost {
    background-color: transparent;
    border-color: #d9d9d9 !important;
    color: #595959;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
      border-color: #40a9ff !important;
      color: #40a9ff;
    }

    &:focus {
      background-color: rgba(0, 0, 0, 0.04);
      border-color: #1890ff !important;
      color: #1890ff;
    }
  }
}

// Action button styles for tables
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;

  .action-btn {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    border: 1px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 16px;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-2px) scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      &::before {
        opacity: 1;
      }
    }

    &:active {
      transform: translateY(-1px) scale(1.02);
      transition: all 0.1s ease;
    }

    .anticon {
      z-index: 1;
      transition: transform 0.2s ease;
    }

    &:hover .anticon {
      transform: scale(1.1);
    }

    // Statistics button
    &.action-btn-statistics {
      background: linear-gradient(135deg, #f0f5ff 0%, #e6f7ff 100%);
      color: #1890ff;
      border-color: #d6e4ff;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);

      &:hover {
        background: linear-gradient(135deg, #d6e4ff 0%, #bae7ff 100%);
        color: #096dd9;
        border-color: #91d5ff;
        box-shadow: 0 6px 16px rgba(24, 144, 255, 0.25);
      }
    }

    // Edit button
    &.action-btn-edit {
      background: linear-gradient(135deg, #f6ffed 0%, #f0fff0 100%);
      color: #52c41a;
      border-color: #d9f7be;
      box-shadow: 0 2px 4px rgba(82, 196, 26, 0.1);

      &:hover {
        background: linear-gradient(135deg, #d9f7be 0%, #b7eb8f 100%);
        color: #389e0d;
        border-color: #95de64;
        box-shadow: 0 6px 16px rgba(82, 196, 26, 0.25);
      }
    }

    // Delete button
    &.action-btn-delete {
      background: linear-gradient(135deg, #fff2f0 0%, #fff1f0 100%);
      color: #ff4d4f;
      border-color: #ffccc7;
      box-shadow: 0 2px 4px rgba(255, 77, 79, 0.1);

      &:hover {
        background: linear-gradient(135deg, #ffccc7 0%, #ffa39e 100%);
        color: #cf1322;
        border-color: #ff7875;
        box-shadow: 0 6px 16px rgba(255, 77, 79, 0.25);
      }
    }

    // View button
    &.action-btn-view {
      background-color: #f9f0ff;
      color: #722ed1;
      border-color: #efdbff;

      &:hover {
        background-color: #f4e6ff;
        color: #531dab;
        border-color: #d3adf7;
        box-shadow: 0 2px 4px rgba(114, 46, 209, 0.2);
      }
    }

    // Info button
    &.action-btn-info {
      background-color: #e6f7ff;
      color: #1890ff;
      border-color: #91d5ff;

      &:hover {
        background-color: #bae7ff;
        color: #096dd9;
        border-color: #69c0ff;
        box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
      }
    }

    // Warning button
    &.action-btn-warning {
      background-color: #fffbe6;
      color: #faad14;
      border-color: #ffe58f;

      &:hover {
        background-color: #fff7e6;
        color: #d48806;
        border-color: #ffd666;
        box-shadow: 0 2px 4px rgba(250, 173, 20, 0.2);
      }
    }
  }
}

// Form filter button styles
.form-filter {
  .filter-buttons,
  .search-buttons {
    .ant-btn {
      min-width: 100px;
      height: 40px;
      border-radius: 6px;
      font-weight: 500;
    }
  }
}

// Large button styles for headers
.header-buttons {
  .ant-btn {
    height: 40px;
    border-radius: 6px;
    font-weight: 500;
    min-width: 100px;
  }
}

// Responsive button adjustments
@media (max-width: 768px) {
  .action-buttons {
    .action-btn {
      width: 28px;
      height: 28px;
      font-size: 12px;
    }
  }

  .form-filter {
    .filter-buttons,
    .search-buttons {
      .ant-btn {
        min-width: 80px;
        height: 36px;
      }
    }
  }
}

@media (max-width: 576px) {
  .form-filter {
    .filter-buttons,
    .search-buttons {
      flex-direction: column;
      gap: 8px;

      .ant-btn {
        width: 100%;
        min-width: auto;
      }
    }
  }
}
