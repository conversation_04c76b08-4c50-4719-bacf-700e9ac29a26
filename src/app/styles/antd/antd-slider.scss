.ant-slider.ant-slider-horizontal {
  padding: 0;
  height: 24px;
  margin: 0;

  .ant-slider-handle {
    inset-block-start: unset;
    width: 16px;

    &:after {
      background-color: transparent;
      border-radius: unset;
      background-repeat: no-repeat;
      width: 16px;
    }

    &:before {
      width: 16px;
    }

    &.ant-slider-handle-1 {
      transform: translateX(-100%) !important;

      &:after {
        background-image: url('/src/asset/icon/slider/slider-left-light.svg');
      }
    }

    &.ant-slider-handle-2 {
      transform: unset !important;

      &:after {
        background-image: url('/src/asset/icon/slider/slider-right-light.svg');
      }
    }
  }
}