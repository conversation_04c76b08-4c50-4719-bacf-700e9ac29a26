@import "./antd-modal.scss";
@import "./antd-slider.scss";
@import "./antd-dropdown-menu";
@import "./antd-collapse.scss";
@import "./antd-form.scss";
@import "./antd-breadcrumb.scss";
@import "./antd-select.scss";
@import "./antd-segmented.scss";
@import "./antd-popover.scss";
@import "../components/buttons.scss";

// Global Admin Filter Buttons Styling
.admin-filter-buttons,
.search-buttons,
.filter-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;

  .ant-btn {
    min-width: 120px;
    height: 42px;
    border-radius: 8px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border: 1px solid #d9d9d9 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
      border-color: #40a9ff !important;

      &::before {
        opacity: 1;
      }
    }

    &:focus {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
    }

    &:active {
      transform: translateY(-1px);
      transition: all 0.1s ease;
    }

    // Default/Clear button styling
    &.ant-btn-default {
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      color: #595959;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      &:hover {
        background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
        color: #262626;
      }
    }

    // Primary/Search button styling
    &.ant-btn-primary {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      border-color: #1890ff !important;
      color: #fff;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);

      &:hover {
        background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
        border-color: #096dd9 !important;
        box-shadow: 0 6px 16px rgba(24, 144, 255, 0.3);
      }
    }

    .anticon {
      font-size: 16px;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    justify-content: stretch;

    .ant-btn {
      flex: 1;
      min-width: auto;
    }
  }

  @media (max-width: 576px) {
    flex-direction: column;
    gap: 8px;

    .ant-btn {
      width: 100%;
      min-width: auto;
    }
  }
}


// textarea count
.ant-input-textarea-show-count .ant-input-data-count {
  bottom: -28px;
  line-height: 20px;
}
