.ant-dropdown .ant-dropdown-menu {
  overflow: hidden;
  padding-left: 0 !important;
  padding-right: 0 !important;

  * {
    transition: stroke 0s, color 0s, background-color var(--transition-timing) !important;
  }

  &.ant-dropdown-menu-vertical {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .ant-dropdown-menu-item {
    &.ant-dropdown-menu-item-selected, &:active {
      color: var(--white);
      background: var(--primary-colours-blue-navy);

      > svg path {
        stroke: var(--white);
      }
    }

    &.ant-dropdown-menu-item-selected.ant-dropdown-menu-item-active {
      color: var(--white);
      background: var(--primary-colours-blue-navy-dark);

      > svg path {
        stroke: var(--white);
      }
    }


    > svg {
      margin-right: 8px;

      path {
        stroke: var(--primary-colours-blue-navy);
      }
    }

    .ant-dropdown-menu-title-content > a {
      transition-duration: 0s;
    }
  }
}