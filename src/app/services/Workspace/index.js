import axios from "axios";
import { API } from "@api";
import { updateBase, getAllBase, getDetailBase } from "@services/Base";
import { genQueryParam } from "@src/common/functionCommons";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";

export function getAllWorkspace(query, loading) {
  return getAllBase(API.WORKSPACE, query, null, loading);
}

export function updateWorkspace(data) {
  return updateBase(API.WORKSPACE+"/{0}", data);
}

export function getWorkspaceById(id, loading) {
  return getDetailBase(API.WORKSPACE_ID, id, ["organizationId"]);
}

export async function getAvailableWorkspace(loading) {
  const config = { loading };
  return axios.get(API.WORKSPACE_AVAILABLE, config)
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      // renderMessageError(err);
      throw err;
    });
}

export async function getWorkspaceBelongs(id, query, loading) {
  const config = { loading };
  const queryParams = genQueryParam(query);
  return axios.get(`${API.WORKSPACE_BELONGS.format(id)}?${queryParams}`, config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      console.log("err", err);
      return null;
    });
}
