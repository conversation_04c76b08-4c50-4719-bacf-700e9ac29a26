import { API } from '@src/constants/api';
import axios from 'axios';
import {
  getAllBase,
  getAllPaginationBaseList,
  getAllPaginationBase,
  getDetailBaseWithParams,
  createBase,
  updateBase,
  deleteBase
} from '../Base';

/**
 * Fetch all courses with pagination
 * @param {Object} query - Query parameters for filtering
 * @param {Object} paging - Paging parameters for filtering
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @returns {Promise} - Promise resolving to the courses data
 */
export const getAllCourses = (query = {}, paging = { page: 1, limit: 12 }, populateOpts = [], loading = true) => {
  return getAllPaginationBaseList(API.COURSES, paging, query, ["name"], populateOpts, loading);
};

export function getPaginationCourses(paging, query) {
  return getAllPaginationBase(API.COURSES, paging, query, ["name"]);
}

/**
 * Fetch details for a specific course
 * @param {String} id - The ID of the course
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the course details
 */
export const getCourseDetails = (id, populateOpts = [], loading = true, toastError = false) => {
  return getDetailBaseWithParams(
    API.COURSE_DETAILS,
    id,
    {},
    populateOpts,
    loading,
    toastError,
  );
};

export const getByCourse = (id) => {
  return axios.get(API.COURSES_DETAILS.format(id))
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch(err => {
      console.log("err", err);
      return null;
    });
};

/**
 * Create a new course
 * @param {Object} data - The course data
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the created course
 */
export const createCourse = (data, populateOpts = [], loading = true, toastError = true) => {
  return createBase(API.COURSES, data, populateOpts, loading, toastError);
};

/**
 * Update a course
 * @param {Object} data - The course data with _id
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} showNoti - Whether to show notification
 * @returns {Promise} - Promise resolving to the updated course
 */
export const updateCourse = (data, populateOpts = [], loading = true, showNoti = true) => {
  return updateBase(API.COURSE_DETAILS, data, populateOpts, loading, showNoti);
};

/**
 * Delete a course
 * @param {String} id - The ID of the course to delete
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the deletion result
 */
export const deleteCourse = (id, loading = false, toastError = true) => {
  return deleteBase(API.COURSE_DETAILS, id, loading, toastError);
};

/**
 * Fetch all tasks for a specific course
 * @param {String} courseId - The ID of the course
 * @param {Object} query - Query parameters for filtering
 * @param {Object} paging - Paging parameters for filtering
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @returns {Promise} - Promise resolving to the tasks data
 */
export const getCourseTasks = (courseId, query = {}, paging = { page: 1, limit: 50 }, populateOpts = [], loading = true) => {
  const config = { loading };
  return axios.get(API.COURSE_TASKS.format(courseId), config)
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch(err => {
      console.log("err", err);
      return null;
    });
};
