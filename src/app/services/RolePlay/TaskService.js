import {API} from '@src/constants/api';
import {
  getAllBase,
  getAllPaginationBaseList,
  getDetailBaseWithParams,
  createBase,
  updateBase,
  deleteBase,
} from '../Base';

/**
 * Fetch all AI Personas with pagination
 * @param {Object} query - Query parameters for filtering
 * @param {Object} paging - Paging parameters for filtering
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @returns {Promise} - Promise resolving to the personas data
 */
export const getAllRoleplayTaskss = (query = {}, paging = {page:1, limit:12}, populateOpts = [], loading = true) => {
  return getAllPaginationBaseList(API.ROLE_PLAY_TASKS, paging, query, populateOpts, loading);
};

/**
 * Fetch all Roleplay Tasks without pagination
 * @param {Object} query - Query parameters for filtering
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @returns {Promise} - Promise resolving to the tasks data
 */
export const getAllRoleplayTasksWithoutPagination = (query = {}, populateOpts = [], loading = true) => {
  return getAllBase(API.ROLE_PLAY_TASKS, query, populateOpts, loading);
};

/**
 * Fetch details for a specific AI Persona
 * @param {String} id - The ID of the persona
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the persona details
 */
export const getRoleplayTasksDetails = (id, populateOpts = ['avatarId'], loading = true, toastError = false) => {
  return getDetailBaseWithParams(
    API.ROLE_PLAY_TASKS_DETAILS,
    id,
    {},
    populateOpts,
    loading,
    toastError,
  );
};

/**
 * Create a new AI Persona
 * @param {Object} data - The persona data
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the created persona
 */
export const createRoleplayTask = (data, populateOpts = ['avatarId'], loading = true, toastError = true) => {
  return createBase(API.ROLE_PLAY_TASKS, data, populateOpts, loading, toastError);
};

/**
 * Update an AI Persona
 * @param {Object} data - The persona data with _id
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} showNoti - Whether to show notification
 * @returns {Promise} - Promise resolving to the updated persona
 */
export const updateRoleplayTask = (data, populateOpts = ['avatarId'], loading = true, showNoti = true) => {
  return updateBase(API.ROLE_PLAY_TASKS_DETAILS, data, populateOpts, loading, showNoti);
};

/**
 * Delete an AI Persona
 * @param {String} id - The ID of the persona to delete
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the deletion result
 */
export const deleteRoleplayTask = (id, loading = true, toastError = true) => {
  return deleteBase(API.ROLE_PLAY_TASKS_DETAILS, id, loading, toastError);
};

/**
 * Create tasks from prompt using AI
 * @param {String} courseId - The ID of the course
 * @param {String} prompt - User prompt for AI generation
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the generated tasks data
 */
export const createTasksFromPrompt = (courseId, prompt, loading = true, toastError = true) => {
  const params = {
    courseId,
    prompt
  };
  return createBase(`${API.ROLE_PLAY_TASKS}/createFromPrompt`, params, [], loading, toastError);
};
