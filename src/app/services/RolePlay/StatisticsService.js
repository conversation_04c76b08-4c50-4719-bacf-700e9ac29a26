import axios from 'axios';
import { API } from '@api';

/**
 * Service để xử lý các API calls liên quan đến thống kê roleplay
 */

/**
 * L<PERSON>y thống kê chi tiết cho một khóa học cụ thể
 * @param {string} courseId - ID của khóa học
 * @param {Object} params - <PERSON><PERSON><PERSON> tham số query
 * @param {string} params.startDate - Ng<PERSON>y bắt đầu filter (ISO format)
 * @param {string} params.endDate - <PERSON><PERSON>y kết thúc filter (ISO format)
 * @param {number} params.topStudentsLimit - <PERSON><PERSON> lượng top học viên (default: 10)
 * @returns {Promise} - Promise chứa dữ liệu thống kê khóa học
 */
export const getCourseStatistics = async (courseId, params = {}) => {
  try {
    const queryParams = {
      startDate: params.startDate,
      endDate: params.endDate,
      topStudentsLimit: params.topStudentsLimit || 10,
      ...params
    };

    // Loại bỏ các tham số undefined/null
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === undefined || queryParams[key] === null) {
        delete queryParams[key];
      }
    });

    const response = await axios.get(API.ROLEPLAY_STATISTICS_COURSE.format(courseId), {
      params: queryParams
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching course statistics:", error);
    throw error;
  }
};

/**
 * Lấy thống kê chi tiết cho một học viên cụ thể
 * @param {string} studentId - ID của học viên
 * @param {Object} params - Các tham số query
 * @param {string} params.startDate - Ngày bắt đầu filter (ISO format)
 * @param {string} params.endDate - Ngày kết thúc filter (ISO format)
 * @returns {Promise} - Promise chứa dữ liệu thống kê học viên
 */
export const getStudentStatistics = async (studentId, params = {}) => {
  try {
    const queryParams = {
      startDate: params.startDate,
      endDate: params.endDate,
      ...params
    };

    // Loại bỏ các tham số undefined/null
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === undefined || queryParams[key] === null) {
        delete queryParams[key];
      }
    });

    const response = await axios.get(API.ROLEPLAY_STATISTICS_STUDENT.format(studentId), {
      params: queryParams
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching student statistics:", error);
    throw error;
  }
};

/**
 * Lấy thống kê tổng thể của nền tảng
 * @param {Object} params - Các tham số query
 * @param {string} params.startDate - Ngày bắt đầu filter (ISO format)
 * @param {string} params.endDate - Ngày kết thúc filter (ISO format)
 * @returns {Promise} - Promise chứa dữ liệu thống kê tổng thể
 */
export const getOverallStatistics = async (params = {}) => {
  try {
    const queryParams = {
      startDate: params.startDate,
      endDate: params.endDate,
      ...params
    };

    // Loại bỏ các tham số undefined/null
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === undefined || queryParams[key] === null) {
        delete queryParams[key];
      }
    });

    const response = await axios.get(API.ROLEPLAY_STATISTICS_OVERALL, {
      params: queryParams
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching overall statistics:", error);
    throw error;
  }
};

/**
 * Lấy thống kê tóm tắt cho dashboard
 * @param {Object} params - Các tham số query
 * @returns {Promise} - Promise chứa dữ liệu thống kê tóm tắt
 */
export const getDashboardStatistics = async (params = {}) => {
  try {
    const response = await axios.get(API.ROLEPLAY_STATISTICS_DASHBOARD, {
      params
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching dashboard statistics:", error);
    throw error;
  }
};

/**
 * Export thống kê ra file Excel/CSV
 * @param {string} type - Loại thống kê ('course', 'student', 'overall')
 * @param {string} id - ID của course hoặc student (nếu cần)
 * @param {Object} params - Các tham số query
 * @param {string} format - Định dạng file ('excel', 'csv')
 * @returns {Promise} - Promise chứa file blob
 */
export const exportStatistics = async (type, id = null, params = {}, format = 'excel') => {
  try {
    let url = API.ROLEPLAY_STATISTICS_EXPORT.format(type);
    if (id) {
      url += `/${id}`;
    }

    const queryParams = {
      format,
      ...params
    };

    const response = await axios.get(url, {
      params: queryParams,
      responseType: 'blob'
    });

    return response.data;
  } catch (error) {
    console.error("Error exporting statistics:", error);
    throw error;
  }
};

/**
 * Lấy thống kê theo khoảng thời gian (time series)
 * @param {Object} params - Các tham số query
 * @param {string} params.startDate - Ngày bắt đầu
 * @param {string} params.endDate - Ngày kết thúc
 * @param {string} params.interval - Khoảng thời gian ('day', 'week', 'month')
 * @param {string} params.metric - Metric cần thống kê ('sessions', 'completions', 'scores')
 * @returns {Promise} - Promise chứa dữ liệu time series
 */
export const getTimeSeriesStatistics = async (params = {}) => {
  try {
    const response = await axios.get(API.ROLEPLAY_STATISTICS_TIMESERIES, {
      params
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching time series statistics:", error);
    throw error;
  }
};

/**
 * Lấy thống kê so sánh giữa các khóa học
 * @param {Array} courseIds - Danh sách ID các khóa học cần so sánh
 * @param {Object} params - Các tham số query
 * @returns {Promise} - Promise chứa dữ liệu so sánh
 */
export const getCoursesComparison = async (courseIds, params = {}) => {
  try {
    const response = await axios.post(API.ROLEPLAY_STATISTICS_COMPARISON, {
      courseIds,
      ...params
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching courses comparison:", error);
    throw error;
  }
};

/**
 * Lấy thống kê hiệu suất theo khu vực địa lý
 * @param {Object} params - Các tham số query
 * @returns {Promise} - Promise chứa dữ liệu thống kê theo khu vực
 */
export const getGeographicalStatistics = async (params = {}) => {
  try {
    const response = await axios.get(API.ROLEPLAY_STATISTICS_GEOGRAPHICAL, {
      params
    });

    return response.data;
  } catch (error) {
    console.error("Error fetching geographical statistics:", error);
    throw error;
  }
};

// Default export object chứa tất cả các functions
const StatisticsService = {
  getCourseStatistics,
  getStudentStatistics,
  getOverallStatistics,
  getDashboardStatistics,
  exportStatistics,
  getTimeSeriesStatistics,
  getCoursesComparison,
  getGeographicalStatistics
};

export default StatisticsService;
