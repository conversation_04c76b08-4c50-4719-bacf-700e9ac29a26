import axios from "axios";
import { createBase, deleteBase, getAllBase, getDetailBase, updateBase } from "@services/Base";
import { API } from "@api";
import { convertObjectToParam } from "@common/functionCommons";

// <PERSON><PERSON><PERSON> t<PERSON><PERSON> cả references
export function getAllReferences(query) {
  return getAllBase(API.REFERENCES, query);
}

// L<PERSON>y chi tiết reference
export function getReferenceDetails(id, populateOpts = []) {
  return getDetailBase(API.REFERENCE_DETAILS.format(id), null, populateOpts);
}

// Tạo mới reference
export function createReference(data) {
  return createBase(API.REFERENCES, data, [], false, true);
}

// Cập nhật reference
export function updateReference(data) {
  return updateBase(API.REFERENCE_DETAILS, data, [], false, true);
}

// Xóa reference
export function deleteReference(id) {
  return deleteBase(API.REFERENCE_DETAILS, id, false, true);
}

// Upload file reference
export function uploadReferenceFile(file, fileInfo, config = {}) {
  config.headers = { "content-type": "multipart/form-data" };
  const formData = new FormData();
  Object.entries(fileInfo).forEach(([key, value]) => {
    formData.append(key, value);
  });
  formData.append("fileType", "file");
  formData.append("file", file);

  return axios.post(API.REFERENCES_UPLOAD, formData, config)
              .then(response => ({ success: true, data: response.data }))
              .catch((error) => error.response.data);
}

// Tạo reference từ URL
export function createReferenceUrl(data) {
  return axios.post(API.REFERENCES_URL, data)
              .then(response => ({
                success: response.status === 200,
                data: response.data,
              }))
              .catch((err) => err?.response?.data);
}
