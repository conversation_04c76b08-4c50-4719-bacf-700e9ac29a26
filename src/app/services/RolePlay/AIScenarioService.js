import {API} from '@src/constants/api';
import {
  getAllBase,
  getAllPaginationBaseList,
  getDetailBaseWithParams,
  createBase,
  updateBase,
  deleteBase,
} from '../Base';

/**
 * Fetch all AI Scenarios with pagination
 * @param {Object} query - Query parameters for filtering
 * @param {Object} paging - Paging parameters for filtering
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @returns {Promise} - Promise resolving to the scenarios data
 */
export const getAllAIScenarios = (query = {}, paging = {page:1, limit:12}, populateOpts = [], loading = true) => {
  return getAllPaginationBaseList(API.AI_SCENARIOS, paging, query, populateOpts, loading);
};

/**
 * Fetch all AI Scenarios without pagination
 * @param {Object} query - Query parameters for filtering
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @returns {Promise} - Promise resolving to the scenarios data
 */
export const getAllAIScenariosWithoutPagination = (query = {}, populateOpts = [], loading = true) => {
  return getAllBase(API.AI_SCENARIOS, query, populateOpts, loading);
};

/**
 * Fetch scenarios for a specific course
 * @param {String} courseId - The ID of the course
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @returns {Promise} - Promise resolving to the scenarios data
 */
export const getScenariosByCourse = (courseId, populateOpts = ['aiPersonaId', 'roleplayInstructionId', 'taskIds'], loading = true) => {
  return getDetailBaseWithParams(
    API.AI_SCENARIOS_BY_COURSE,
    courseId,
    {},
    populateOpts,
    loading,
    false
  );
};

/**
 * Fetch default scenario for a specific course
 * @param {String} courseId - The ID of the course
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @returns {Promise} - Promise resolving to the default scenario
 */
export const getDefaultScenarioByCourse = (courseId, populateOpts = ['aiPersonaId', 'roleplayInstructionId', 'taskIds'], loading = true) => {
  return getDetailBaseWithParams(
    API.AI_SCENARIOS_DEFAULT_BY_COURSE,
    courseId,
    {},
    populateOpts,
    loading,
    false
  );
};

/**
 * Fetch details for a specific AI Scenario
 * @param {String} id - The ID of the scenario
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the scenario details
 */
export const getAIScenarioDetails = (id, populateOpts = ['aiPersonaId', 'roleplayInstructionId', 'taskIds'], loading = true, toastError = false) => {
  return getDetailBaseWithParams(
    API.AI_SCENARIO_DETAILS,
    id,
    {},
    populateOpts,
    loading,
    toastError,
  );
};

/**
 * Create a new AI Scenario
 * @param {Object} data - The scenario data
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the created scenario
 */
export const createAIScenario = (data, populateOpts = ['aiPersonaId', 'roleplayInstructionId', 'taskIds'], loading = true, toastError = true) => {
  return createBase(API.AI_SCENARIOS, data, populateOpts, loading, toastError);
};

/**
 * Update an AI Scenario
 * @param {Object} data - The scenario data with _id
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} showNoti - Whether to show notification
 * @returns {Promise} - Promise resolving to the updated scenario
 */
export const updateAIScenario = (data, populateOpts = ['aiPersonaId', 'roleplayInstructionId', 'taskIds'], loading = true, showNoti = true) => {
  return updateBase(API.AI_SCENARIO_DETAILS, data, populateOpts, loading, showNoti);
};

/**
 * Delete an AI Scenario (soft delete)
 * @param {String} id - The ID of the scenario to delete
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} showNoti - Whether to show notification
 * @returns {Promise} - Promise resolving to the deletion result
 */
export const deleteAIScenario = (id, loading = true, showNoti = true) => {
  return deleteBase(API.AI_SCENARIO_DETAILS, id, loading, showNoti);
};

/**
 * Set a scenario as default for its course
 * @param {String} id - The ID of the scenario to set as default
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the update result
 */
export const setScenarioAsDefault = (id, loading = true, toastError = true) => {
  return updateBase(API.AI_SCENARIO_SET_DEFAULT.format(id), {_id: id}, [], loading, toastError);
};
