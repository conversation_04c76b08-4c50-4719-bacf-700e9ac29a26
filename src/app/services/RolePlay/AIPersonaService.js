import {API} from '@src/constants/api';
import {
  getAllBase,
  getAllPaginationBaseList,
  getDetailBaseWithParams,
  createBase,
  updateBase,
  deleteBase,
} from '../Base';

/**
 * Fetch all AI Personas with pagination
 * @param {Object} query - Query parameters for filtering
 * @param {Object} paging - Paging parameters for filtering
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @returns {Promise} - Promise resolving to the personas data
 */
export const getAllAIPersonas = (query = {}, paging = {page:1, limit:12}, populateOpts = [], loading = true) => {
  return getAllPaginationBaseList(API.ROLE_PLAY_PERSONAS, paging, query, populateOpts, loading);
};

/**
 * Fetch all AI Personas
 * @param {Object} query - Query parameters for filtering
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @returns {Promise} - Promise resolving to the personas data
 */
export const getAllAIPersonasWithoutPagination = (query = {}, populateOpts = [], loading = true) => {
  return getAllBase(API.ROLE_PLAY_PERSONAS, query, populateOpts, loading);
};

/**
 * Fetch details for a specific AI Persona
 * @param {String} id - The ID of the persona
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the persona details
 */
export const getAIPersonaDetails = (id, populateOpts = [], loading = true, toastError = false) => {
  return getDetailBaseWithParams(
    API.ROLE_PLAY_PERSONA_DETAILS,
    id,
    {},
    populateOpts,
    loading,
    toastError,
  );
};

export const getAIScenariosDetails = (id, populateOpts = [], loading = true, toastError = false) => {
  return getDetailBaseWithParams(
    API.ROLE_PLAY_SCENARIOS_DETAILS,
    id,
    {},
    populateOpts,
    loading,
    toastError,
  );
};
/**
 * Create a new AI Persona
 * @param {Object} data - The persona data
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the created persona
 */
export const createAIPersona = (data, populateOpts = ['avatarId'], loading = true, toastError = true) => {
  return createBase(API.ROLE_PLAY_PERSONAS, data, populateOpts, loading, toastError);
};

/**
 * Update an AI Persona
 * @param {Object} data - The persona data with _id
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} showNoti - Whether to show notification
 * @returns {Promise} - Promise resolving to the updated persona
 */
export const updateAIPersona = (data, populateOpts = ['avatarId'], loading = true, showNoti = true) => {
  return updateBase(API.ROLE_PLAY_PERSONA_DETAILS, data, populateOpts, loading, showNoti);
};

/**
 * Delete an AI Persona
 * @param {String} id - The ID of the persona to delete
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the deletion result
 */
export const deleteAIPersona = (id, loading = false, toastError = true) => {
  return deleteBase(API.ROLE_PLAY_PERSONA_DETAILS, id, loading, toastError);
};

/**
 * Create an AI Persona from course context using AI
 * @param {String} courseId - The ID of the course
 * @param {String} userPrompt - User prompt for AI generation
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the generated persona data
 */
export const createAIPersonaFromCourseContext = (courseId, userPrompt, loading = true, toastError = true) => {
  const params = {
    courseId,
    userPrompt
  };
  return createBase(`${API.ROLE_PLAY_PERSONAS}/createFromCourseContext`, params, [], loading, toastError);
};
