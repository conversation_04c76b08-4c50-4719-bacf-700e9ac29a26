import { API } from '@src/constants/api';
import { getDetailBase } from '@services/Base';
import axios from 'axios';

/**
 * Fetch all role play sessions for a student by courseId.
 * @param {string} courseId - The ID of the course.
 * @param {object} queryParams - Additional query parameters (e.g., paging, sorting, filtering).
 * @param {boolean} loading - Whether to show loading state (custom Axios interceptor config).
 * @returns {Promise<object|null>} - Promise resolving to the API response data or null on error.
 */
export const getRolePlaySessionsByStudent = (courseId, queryParams = {}, loading = true) => {
  const apiEndpoint = API.ROLEPLAY_SESSIONS_BY_STUDENT;

  if (!apiEndpoint) {
    // eslint-disable-next-line no-console
    console.error(
      'API.ROLEPLAY_SESSIONS_BY_STUDENT is not defined in src/constants/api.js. Please define it first. Example: ROLEPLAY_SESSIONS_BY_STUDENT: \'/api/roleplaysessions/by-student\''
    );
    return Promise.resolve(null);
  }

  const params = {
    ...queryParams,
    courseId,
  };

  const config = {
    params,
    loading,
  };

  return axios.get(apiEndpoint, config)
    .then(response => {
      if (response && response.status === 200 && response.data) {
        return response.data;
      }
      // eslint-disable-next-line no-console
      console.warn('Received non-200 status or no data for getRolePlaySessionsByStudent:', response);
      return null;
    })
    .catch(err => {
      // eslint-disable-next-line no-console
      console.error('Error fetching role play sessions by student:', err);
      return null;
    });
};

export function getRolePlaySessionDetail(id) {
  return getDetailBase(API.ROLEPLAY_SESSION_ID, id, ['taskId,courseId,courseId.aiPersonaId,analysisId'], false, false);
}

/**
 * Fetch analysis results for a specific session.
 * @param {string} sessionId - The ID of the session to get analysis for.
 * @param {boolean} loading - Whether to show loading state (custom Axios interceptor config).
 * @returns {Promise<object|null>} - Promise resolving to the analysis data or null on error.
 */
export const getAnalysisForSession = (sessionId, loading = true) => {
  if (!sessionId) {
    console.error('sessionId is required for getAnalysisForSession');
    return Promise.resolve(null);
  }

  const params = { sessionId };
  const config = { params, loading, showError: false };

  return axios.get('/api/roleplay.analysises/getAnalysisForSession', config)
    .then(response => {
      if (response && response.status === 200 && response.data) {
        return response.data;
      }
      console.warn('Received non-200 status or no data for getAnalysisForSession:', response);
      return null;
    })
    .catch(err => {
      console.error('Error fetching analysis for session:', err);
      return null;
    });
};
