import axios from 'axios';
import {createBase, deleteBase, getAllBase, getDetailBase, getAllPaginationBase, updateBase} from '@services/Base';
import {API} from '@api';
import {convertObjectToParam} from '@common/functionCommons';

export function getAllInstruction(query) {
  return getAllBase(API.ROLEPLAY_INSTRUCTION, query);
}

export function getInstructionPagination(paging, query, searchFields = ['name'], populateOpts = []) {
  query.sort = query.sort || '-createdAt';
  return getAllPaginationBase(API.ROLEPLAY_INSTRUCTION, paging, query, searchFields, populateOpts);
}

export function createInstruction(data) {
  return createBase(API.ROLEPLAY_INSTRUCTION, data);
}

export function deleteInstruction(id) {
  return deleteBase(API.ROLEPLAY_INSTRUCTION_ID, id);
}

export function getInstructionDetail(id) {
  return getDetailBase(API.ROLEPLAY_INSTRUCTION_ID, id);
}

export function updateInstruction(id, data) {
  return updateBase(API.ROLEPLAY_INSTRUCTION_ID, {...data, _id: id});
}
