import axios from "axios";

import { genPopulateParam, genQueryParam, genSearchFieldParam } from "@src/common/functionCommons";
import { convertSnakeCaseToCamelCase } from "@src/common/dataConverter";

export function joinParams(arrParam) {
  if (!Array.isArray(arrParam)) return "";
  return arrParam.filter(x => !!x).join("&");
}

export function createBase(api, data, populateOpts = [], loading = true, toastError = false, params) {
  const config = { loading, toastError, params };
  const populateParams = genPopulateParam(populateOpts);
  return axios.post(`${api}` + "?" + populateParams, (data), config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function getBase(api, query = {}, populateOpts = [], loading = true, searchFields) {
  const config = { loading };

  const arrParams = [
    genQueryParam(query),
    genPopulateParam(populateOpts),
    genSearchFieldParam(searchFields),
  ];

  return axios.get(`${api}?${joinParams(arrParams)}`, config)
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function getAllBase(api, query = {}, populateOpts = [], loading = true, searchFields) {
  const config = { loading };

  const arrParams = [
    genQueryParam(query),
    genPopulateParam(populateOpts),
    genSearchFieldParam(searchFields),
  ];

  return axios.get(`${api}/findAll?${joinParams(arrParams)}`, config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function getAllManager(api, query, populateOpts = [], loading = true) {
  const config = { loading };

  const arrParams = [
    genQueryParam(query),
    genPopulateParam(populateOpts),
  ];

  return axios.get(`${api}/manager?${joinParams(arrParams)}`, config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function getAllPaginationBase(api, paging, query, searchFields = [], populateOpts = [], loading = true) {
  const currentPage = paging?.page || 1;
  const pageSize = paging?.pageSize || paging?.limit || 0;

  const config = { loading };

  const arrParams = [
    `page=${currentPage}`,
    `pageSize=${pageSize}`,
    genQueryParam(query),
    genPopulateParam(populateOpts),
    genSearchFieldParam(searchFields),
  ];

  return axios.get(`${api}?${joinParams(arrParams)}`, config)
    .then(response => {
      if (response.status === 200 && Array.isArray(response?.data?.rows)) {
        if (pageSize) {
          return convertSnakeCaseToCamelCase(response.data);
        } else if (Array.isArray(response.data.rows)) {
          return convertSnakeCaseToCamelCase(response.data.rows);
        } else {
          return [];
        }
      }
      return null;
    })
    .catch((err) => {
      return null;
    });
}


export function getAllPaginationBaseList(api, paging, query, searchFields = [], populateOpts = [], loading = true) {
  const currentPage = paging?.page || 1;
  const pageSize = paging?.pageSize || paging?.limit || 0;

  const config = { loading };

  const arrParams = [
    `page=${currentPage}`,
    `limit=${pageSize}`,
    genQueryParam(query),
    genPopulateParam(populateOpts),
    genSearchFieldParam(searchFields),
  ];

  return axios.get(`${api}?${joinParams(arrParams)}`, config)
    .then(response => {
      if (response.status === 200 && Array.isArray(response?.data?.rows)) {
        if (pageSize) {
          return convertSnakeCaseToCamelCase(response.data);
        } else if (Array.isArray(response.data.rows)) {
          return convertSnakeCaseToCamelCase(response.data.rows);
        } else {
          return [];
        }
      }
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function getDetailBase(api, id, populateOpts = [], loading = true, toastError = false) {
  const config = { loading, toastError };
  const populateParams = genPopulateParam(populateOpts);
  return axios.get(api.format(id) + "?" + populateParams, config)
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch(err => {
      console.log("err", err);
      return null;
    });
}

export function getDetailBaseWithParams(api, id, params, populateOpts = [], loading = true, toastError = false) {
  const config = { loading, toastError };
  const arrParams = [
    genQueryParam(params),
    genPopulateParam(populateOpts),
  ];
  return axios.get(api.format(id) + "?" + joinParams(arrParams), config)
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch(err => {
      console.log("err", err);
      return null;
    });
}

export function updateBase(api, { _id, ...data }, populateOpts = [], loading = true, showNoti = false, params = {}) {
  const config = { loading, hideNoti: !showNoti, ...params };
  const populateParams = genPopulateParam(populateOpts);
  return axios.put(api.format(_id) + "?" + populateParams, (data), config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function deleteBase(api, id, loading = true, toastError = false, params) {
  const config = { loading, toastError, params };
  return axios.delete(api.format(id), config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function deleteManyBase(api, query, loading = true, toastError = false, params) {
  const config = { loading, toastError, params };

  return axios.delete(`${api}/deleteMany?${genQueryParam(query)}`, config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function postBase(api, data, loading = true, toastError = false, params) {
  const config = { loading, toastError, params };

  return axios.post(`${api}`, data, config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function streamBase(api, accessToken) {

  return fetch(api, {
    method: "get", // HTTP POST to send query to server
    //headers: {
    //  Accept: "application/json, text/plain, */*", // indicates which files we are able to understand
    //  "Content-Type": "application/json", // indicates what the server actually sent
    //  "Authorization": `Bearer ${accessToken}`, // indicates what the server actually sent
    //},
  }).catch((error) => {
    // catches error case and if fetch itself rejects
    error.response = {
      status: 0,
      statusText: "Cannot connect. Please make sure you are connected to internet.",
    };
    throw error;
  });
}

export function getParamsBase(api, params = {}, loading = true) {
  const config = { loading };

  return axios.get(api, { params, ...config })
    .then(response => response.data || null)
    .catch(err => null);
}
