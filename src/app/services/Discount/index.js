import axios from "axios";
import { API } from "@api";
import { getAllPaginationBase, createBase, updateBase, deleteBase } from "../Base";


export async function checkDiscountCode(data) {
  return axios.get(API.DISCOUNT_CHECK, { params: data })
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch(err => {
      console.log("err", err);
      return null;
    });
}

export const getAllDiscount = (paging, query) => {
  query.sort = query.sort || "-createdAt";
  return getAllPaginationBase(API.DISCOUNT, paging, query);
}

export const createDiscount = (data) => {
  return createBase(API.DISCOUNT, data);
}

export const updateDiscount = (data) => {
  return updateBase(API.DISCOUNT_ID, data);
}

export const deleteDiscount = (id) => {
  return deleteBase(API.DISCOUNT_ID, id);
}