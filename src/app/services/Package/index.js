import { getAllBase, getBase, getDetailBase, createBase, updateBase, deleteBase } from "@services/Base";
import { API } from "@api";
import axios from "axios";
import { genQueryParam } from "@common/functionCommons";

export function getAllPackage(query = {}) {
  const queryParams = genQueryParam(query)
  const config = { loading: false };
  
  return axios.get(`${API.PACKAGE}/findAll?${queryParams}`, config)
              .then(response => {
                if (response.status === 200) return response?.data;
                return null;
              })
              .catch((err) => {
                return null;
              });
}

export function getAllFeature(query) {
  return getAllBase(API.FEATURES, query);
}

export function getPackageForCustomer(customerId, status) {
  return getBase(API.SUBSCRIPTION, { customerId, status });
}

export function getPackageDetail(id) {
  return getDetailBase(API.PACKAGE_ID, id);
}

export function createPackage(data) {
  return createBase(API.PACKAGE, data, [], false, true);
}

export function updatePackage(data) {
  return updateBase(API.PACKAGE_ID, data, [], false, true);
}

export function deletePackage(id) {
  return deleteBase(API.PACKAGE_ID, id, false, true);
}