import { API } from "@api";
import {
  createBase,
  deleteBase,
  getAllBase,
  updateBase,
  getDetailBase,
  getAllPaginationBase,
  getBase,
} from "@services/Base";
import axios from "axios";

import { joinParams } from "@services/Base";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";
import { genPopulateParam, genQueryParam } from "@common/functionCommons";

export async function getAllTool(query, populateOpts = [], loading) {
  const arrParams = [
    genQueryParam(query),
    genPopulateParam(populateOpts),
  ];
  const config = { loading };
  
  return axios.get(`${API.ALL_TOOL}?${joinParams(arrParams)}`, config)
              .then(response => {
                if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
                return null;
              })
              .catch((err) => {
                return null;
              });
}

export async function getAllToolAvailable(query, populateOpts = [], loading) {
  const arrParams = [
    genQueryParam(query),
    genPopulateParam(populateOpts),
  ];
  const config = { loading };
  
  return axios.get(`${API.TOOL_AVAILABLE}?${joinParams(arrParams)}`, config)
              .then(response => {
                if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
                return null;
              })
              .catch((err) => {
                return null;
              });
}

export function getAllGroupTool(query, loading) {
  return getAllBase(API.TOOL_GROUP, query, null, loading);
}

export function getGroupTool(query, loading) {
  // return getAllBase(API.TOOL_GROUP, query,null, loading,["groupName"]);
  return getAllBase(API.TOOL_GROUP, query, null, loading, []);
}

export function getGroupToolById(id) {
  return getDetailBase(API.TOOL_GROUP_ID, id);
}

export function createGroupTool(data) {
  return createBase(API.TOOL_GROUP, data);
}

export function editGroupTool(data) {
  return updateBase(API.TOOL_GROUP_ID, data);
}

export function deleteGroupTool(id, toastError = false) {
  return deleteBase(API.TOOL_GROUP_ID, id, toastError);
}

export function createTool(data, toastError = false) {
  return createBase(API.TOOL, data, [], false, toastError);
}

export function getToolDetail(id) {
  return getDetailBase(API.TOOL_ID, id);
}

export function updateTool(data, toastError = false) {
  return updateBase(API.TOOL_ID, data, [], false, toastError);
}

export function deleteTool(id, toastError = false) {
  return deleteBase(API.TOOL_ID, id, false, toastError);
}

export async function getPaginationTool(paging, query, populateOpts = [], loading) {
  query.sort = query.sort ? query.sort : "-createdAt";
  return getAllPaginationBase(API.TOOL, paging, query, ["name"]);
}

export async function getWelcomeTool(query, populateOpts = [], loading) {
  return getBase(API.TOOL_WELCOME, query, populateOpts, loading);
}

export async function favoriteTool(toolId, toastError = false, loading) {
  const config = { loading };
  const data = { toolId };
  return axios
    .post(API.TOOL_FAVORITE, (data), config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function unFavoriteTool(toolId, toastError = false) {
  return deleteBase(API.TOOL_UNFAVORITE, toolId, false, toastError);
}

export const copyTool = (data, toastError = false) => {
  return createBase(`${API.TOOL}/copy`, data, [], false, toastError);
};


export const getMarkExamTools = () => {
  return getBase(API.TOOL_MARK_TEST);
};

export const getToolMostUsed = (type) => {
  return getBase(API.TOOL_MOST_USED, {type});
};