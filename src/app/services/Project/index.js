import { API } from "@api";
import {
  getAllBase,
  getAllPaginationBase,
  getDetailBase,
  createBase,
  deleteBase,
  updateBase,
  joinParams,
  getBase,
} from "@services/Base";
import axios from "axios";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";
import { genPopulateParam, genQueryParam, genSearchFieldParam } from "@common/functionCommons";

export function createProject(data) {
  return createBase(API.PROJECT, data);
}

export function createProjectFromTool(data) {
  return createBase(API.PROJECT_CREATE_FROM_TOOL, data);
}

export function getAllProjects(query, loading) {
  return getAllBase(API.PROJECT, query, ["ownerId", "imageId"], loading);
}

export function getProjectByStudent(paging, query, requestConfig = {}) {
  // return getAllPaginationBase('/api/projects/getAllProjectByStudent', paging, query, null, null, loading);
  const currentPage = paging?.page || 1;
  const pageSize = paging?.pageSize || paging?.limit || 0;

  const arrParams = [
    `page=${currentPage}`,
    `pageSize=${pageSize}`,
    genQueryParam(query),
  ];

  return axios.get(`/api/projects/getAllProjectByStudent?${joinParams(arrParams)}`, requestConfig)
    .then(response => {
      if (response.status === 200 && Array.isArray(response?.data?.rows)) {
        if (pageSize) {
          return convertSnakeCaseToCamelCase(response.data);
        } else if (Array.isArray(response.data.rows)) {
          return convertSnakeCaseToCamelCase(response.data.rows);
        } else {
          return [];
        }
      }
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function getProjectStudentTagCategory(query){
  return getBase(API.PROJECT_STUDENT_TAG_CATEGORY, query);
}

export async function getProjectDetail(id, populateOpts = [], loading = true) {
  const config = { loading };
  const populateParams = genPopulateParam(populateOpts);
  return axios.get(API.PROJECT_DETAIL.format(id) + "?" + populateParams, config)
              .then(response => {
                if (response.status === 200) return { code: 200, data: response?.data };
                return null;
              })
              .catch(err => {
                console.log("err", err);
                return err.response?.data;
              });
}

export function copyProject(data, toastError = false, params) {
  return createBase(API.PROJECT_COPY, data, ["imageId", "ownerId"], false, toastError, params);
}

export function deleteProject(id, toastError = true, params) {
  return deleteBase(API.PROJECT_ID, id, false, toastError, params);
}

export function updateProject(data, showNoti = false, params) {
  return updateBase(API.PROJECT_ID, data, [], false, showNoti, params);
}

export function submitInputDataStream(projectId, data, loading) {
  const config = { loading };
  return axios.put(API.PROJECT_SUBMIT_INPUT_DATA_STREAM.format(projectId), (data), config)
              .then(response => {
                if (response.status === 200) return response?.data;
                return null;
              })
              .catch((err) => {
                // renderMessageError(err);
                throw err;
              });
}

export async function getRecentProjects(query, loading) {
  const config = { loading };
  const arrParams = [
    genQueryParam(query),
  ];
  return axios
    .get(`${API.RECENTS}?${joinParams(arrParams)}`, config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function moveProject(data, params) {
  return updateBase(API.PROJECT_MOVE, data, [], false, true, params);
}

export function createContentBlock(projectId, data) {
  return updateBase(API.PROJECT_CREATE_CONTENT.format(projectId), data);
}

export function insertContentBlock(projectId, data) {
  return updateBase(API.PROJECT_INSERT_CONTENT.format(projectId), data);
}

export function createContentFromTemplate(data) {
  return updateBase(API.PROJECT_CREATE_CONTENT_FROM_TEMPLATE, data);
}

export function getProjectPermission(id, loading = true, toastError = false) {
  const config = { loading, toastError };
  return axios.get(API.PROJECT_PERMISSION.format(id) + "?", config)
              .then(response => {
                if (response.status === 200) return { code: 200, permission: response?.data };
                return null;
              })
              .catch(err => {
                console.log("err", err);
                return { code: err.response?.status, permission: null };
              });
}

export function uploadImageProject(templateId, file, config = {}) {
  config.headers = { "content-type": "multipart/form-data" };
  const formData = new FormData();
  formData.append("projectId", templateId);
  formData.append("file", file);
  return axios.post(API.UPLOAD_IMAGE_PROJECT, formData, config)
              .then(response => {
                if (response.status === 200) {
                  return convertSnakeCaseToCamelCase(response.data);
                }
                return null;
              })
              .catch(err => {
                console.log("err", err);
                return null;
              });
}

export function removeProjectThumbnail(id, toastError = true, params) {
  return deleteBase(API.REMOVE_PROJECT_THUMBNAIL, id, false, toastError, params);
}