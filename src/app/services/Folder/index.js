import axios from "axios";
import { API } from "@api";
import { createBase, deleteBase, getAllBase, updateBase, getAllManager, getDetailBase, getBase } from "@services/Base";
import { convertSnakeCaseToCamelCase } from "@src/common/dataConverter";
import { genPopulateParam } from "@src/common/functionCommons";


export function getAllFolder(query, loading) {
  return getAllBase(API.FOLDER, query, null, loading);
}

export function getAllExamSchoolFolder() {
  return getBase(API.EXAM_SCHOOL_FOLDER);
}

export function getManagerFolder(query, loading) {
  return getAllManager(API.FOLDER, query, null, loading);
}

export async function getFolderById(id) {
  return axios.get(API.FOLDER_ID.format(id))
              .then(response => {
                if (response.status === 200) return response?.data;
                return null;
              })
              .catch(err => {
                console.log("err", err);
                return err.response?.data;
              });
}

export async function getFolderDetail(id, populateOpts = [], loading = true) {
  const config = { loading };
  const populateParams = genPopulateParam(populateOpts);
  return axios.get(API.FOLDER_DETAIL_ID.format(id) + "?" + populateParams, config)
              .then(response => {
                if (response.status === 200) return { code: 200, data: response?.data };
                return null;
              })
              .catch(err => {
                console.log("err", err);
                return err.response?.data;
              });
}

export async function copyFolder(data, toastError = false, params) {
  return createBase(API.FOLDER_COPY, data, [], false, toastError, params);
}

export function deleteFolder(id, toastError = false, params) {
  return deleteBase(API.FOLDER_ID, id, false, toastError, params);
}

export function updateFolder(data, toastError = false, params) {
  return updateBase(API.FOLDER_ID, data, [], false, toastError, params);
}

export function createFolder(data) {
  return createBase(API.FOLDER, data);
}


export function getUserFolderPermission(id, loading = true, toastError = false) {
  const config = { loading, toastError };
  return axios.get(API.FOLDER_PERMISSION.format(id) + "?", config)
              .then(response => {
                if (response.status === 200) return { code: 200, permission: response?.data };
                return null;
              })
              .catch(err => {
                console.log("err", err);
                return { code: err.response?.status, permission: null };
              });
}

export function getAvailableFolder(workspaceId, loading) {
  const config = { loading, params: { workspaceId } };
  return axios.get(API.FOLDER_AVAILABLE, config)
              .then(response => {
                if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
                return null;
              })
              .catch(err => {
                // renderMessageError(err);
                return null;
              });
}