import { API } from "@api";

import { convertSnakeCaseToCamelCase } from "@common/dataConverter";
import { GENERAL_ACCESS_TYPE } from "@constant";
import { createBase, deleteBase, getAllBase, updateBase, getAllManager, getDetailBase } from "@services/Base";

export function getAllGeneralAccess(query, loading) {
  return getAllBase(API.GENERAL_ACCESS, query, ['organizationId'], loading);
}

export function updateGeneralAccess(data, typeAccess = GENERAL_ACCESS_TYPE.RESTRICTED, params) {
  let api = API.GENERAL_ACCESS_RESTRICTED;
  if (typeAccess === GENERAL_ACCESS_TYPE.ORGANIZATIONAL) {
    api = API.GENERAL_ACCESS_ORGANIZATIONAL;
  } else if (typeAccess === GENERAL_ACCESS_TYPE.ANYONE_WITH_LINK) {
    api = API.GENERAL_ACCESS_LINK;
  }
  return createBase(api, data, [], false, true, params);
}