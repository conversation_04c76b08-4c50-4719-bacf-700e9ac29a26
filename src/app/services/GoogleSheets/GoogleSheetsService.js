import axios from 'axios';

// Google Sheets API configuration
const GOOGLE_SHEETS_API_KEY = 'AIzaSyB0L51DyuN7RN7_270zC9Gr2Dj30YZjEkM';

// Available CORS proxies (fallback options)
const CORS_PROXIES = [
  'https://api.allorigins.win/get?url=',
  'https://corsproxy.io/?',
  'https://cors-anywhere.herokuapp.com/',
  'https://api.codetabs.com/v1/proxy?quest=',
];

/**
 * Import data from Google Sheets - Pure Frontend Solution
 * @param {String} sheetUrl - The Google Sheets URL
 * @param {Boolean} loading - Whether to show loading state
 * @returns {Promise} - Promise resolving to the sheet data
 */
export const importFromGoogleSheets = async (sheetUrl, loading = true) => {
  // Validate URL first
  if (!validateGoogleSheetsUrl(sheetUrl)) {
    throw new Error('URL Google Sheets không hợp lệ. Vui lòng kiểm tra lại định dạng URL.');
  }

  const spreadsheetId = extractSpreadsheetId(sheetUrl);
  console.log(`Processing Google Sheets ID: ${spreadsheetId}`);
  // Try multiple approaches to fetch data
  try {
    const result = await tryGoogleSheetsAPI(spreadsheetId);
    if (result && result.success) {
      return result;
    }
  } catch (error) {
    console.log(`Approach failed:`, error.message);

  }
};

/**
 * Try Google Sheets API v4 directly
 */
const tryGoogleSheetsAPI = async (spreadsheetId) => {
  const apiUrl = `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/A:Z?key=${GOOGLE_SHEETS_API_KEY}`;

  const response = await fetch(apiUrl, {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`API Error: ${response.status}`);
  }

  const data = await response.json();
  if (!data.values || data.values.length === 0) {
    throw new Error('No data found');
  }

  return {
    success: true,
    data: data.values,
    message: '✅ Đã lấy dữ liệu thành công từ Google Sheets API',
  };
};


/**
 * Validate Google Sheets URL format
 * @param {String} url - The URL to validate
 * @returns {Boolean} - Whether the URL is valid
 */
export const validateGoogleSheetsUrl = (url) => {
  if (!url || typeof url !== 'string') return false;

  // Check if URL contains Google Sheets domain and spreadsheet ID pattern
  const googleSheetsPattern = /^https:\/\/docs\.google\.com\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/;
  return googleSheetsPattern.test(url);
};

/**
 * Extract spreadsheet ID from Google Sheets URL
 * @param {String} url - The Google Sheets URL
 * @returns {String|null} - The spreadsheet ID or null if invalid
 */
export const extractSpreadsheetId = (url) => {
  if (!validateGoogleSheetsUrl(url)) return null;

  const match = url.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
  return match ? match[1] : null;
};

/**
 * Convert Google Sheets data to course format
 * @param {Array} sheetsData - Raw data from Google Sheets
 * @returns {Array} - Formatted course data
 */
export const convertSheetsDataToCourseFormat = (sheetsData) => {
  if (!Array.isArray(sheetsData) || sheetsData.length === 0) return [];

  // If data is already in object format (from backend), return as is with temp IDs
  if (typeof sheetsData[0] === 'object' && !Array.isArray(sheetsData[0])) {
    return sheetsData.map((item, index) => ({
      ...item,
      _tempId: `sheet_${index}`,
    }));
  }

  // Assume first row is headers
  const headers = sheetsData[0];
  const dataRows = sheetsData.slice(1);

  return dataRows.map((row, index) => {
    const courseData = {};

    headers.forEach((header, headerIndex) => {
      const value = row[headerIndex] || '';

      // Map common headers to course fields
      switch (header.toLowerCase().trim()) {
        case 'name':
        case 'course name':
        case 'tên khóa học':
          courseData.name = value;
          break;
        case 'description':
        case 'mô tả':
          courseData.description = value;
          break;
        case 'duration':
        case 'thời lượng':
        case 'estimated time':
        case 'estimatedcalltimeinminutes':
          courseData.estimatedCallTimeInMinutes = parseInt(value) || 0;
          break;
        case 'type':
        case 'simulation type':
        case 'loại mô phỏng':
          courseData.simulationType = value;
          break;
        case 'status':
        case 'trạng thái':
          courseData.status = value.toLowerCase();
          break;
        default:
          // Store other fields as custom properties
          courseData[header] = value;
      }
    });

    // Add unique identifier for selection
    courseData._tempId = `sheet_${index}`;

    return courseData;
  });
};

