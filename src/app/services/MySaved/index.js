import axios from "axios";
import { API } from "@api";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";
import { createBase } from "@services/Base";
import { genQueryParam } from "@src/common/functionCommons";

export function getAllMySaved(query, loading) {
  const config = { loading };
  const queryParams = genQueryParam(query);
  return axios
    .get(`${API.MY_SAVED}?${queryParams}`, config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      //renderMessageError(err);
      return null;
    });
}

export const saved = (projectId, folderId) => {
  return createBase(API.SAVED, { projectId, folderId });
};
export const unsaved = (projectId, folderId) => {
  return createBase(API.UNSAVED, { projectId, folderId });
};


export function toStar(data) {
  return createBase(API.SAVED, data);
}

export function unStar(data) {
  return createBase(API.UNSAVED, data);
}