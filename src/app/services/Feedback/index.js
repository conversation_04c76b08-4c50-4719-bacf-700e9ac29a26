import { createBase, getBase, getAllBase, joinParams } from "@services/Base";
import { API } from "@api";
import axios from "axios";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";
import { genPopulateParam, genQueryParam } from "@common/functionCommons";

export function submitFeedback(data) {
  return createBase(API.FEEDBACK, data);
}

export function getGroupFeedBack() {
  return getBase(API.GROUP_FEEDBACK);
}

export function getAllGroupFeedback() {
  return getAllBase(API.GROUP_FEEDBACK);
}

export function getFeedback(query, populateOpts = [], loading) {
  const config = { loading };
  const arrParams = [
    genQueryParam(query),
    genPopulateParam(populateOpts),
  ];
  
  return axios.get(`${API.FEEDBACK}?${joinParams(arrParams)}`, config)
              .then(response => {
                if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
                return null;
              })
              .catch((err) => {
                return null;
              });
}
