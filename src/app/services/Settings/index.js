import { API } from "@api";
import axios from "axios";
import { genPopulateParam } from "@common/functionCommons";
import { convertSnakeCaseToCamelCase } from "@common/dataConverter";
import { getBase } from "../Base";


export function getDetailSetting() {
  const config = { loading: true };
  return axios
    .get(`${API.SETTING}/getOne`, config)
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export function updateSetting(data) {
  const config = { loading: true };
  return axios
    .put(API.SETTING + "/manager", (data), config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      // throw err;
      return null;
    });
}


export function getToolStudent() {
  return getBase(API.GET_TOOL_STUDENT);
}

export function getVideoTutorial() {
  return getBase(API.GET_VIDEO_TUTORIAL);
}

export function getChatBotInfo() {
  return getBase(API.GET_CHAT_BOT_INFO);
}
