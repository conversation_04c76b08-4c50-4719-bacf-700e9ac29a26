import { createBase } from "@services/Base";
import { API } from "@api";
import { useAnalyticsEventTracker } from "@src/ga";
import axios from "axios";

export function getLinkVnpay(data, toastError = false) {
  return createBase(API.GET_LINK_VNPAY, data, [], false, toastError);
}

export function streamTransactionPayment(transactionId) {
  return new EventSource(
    API.TRANSACTION_PAYMENT_STREAM.format(transactionId),
  );
}

export async function getSubscriptionDateInfo(data) {
  return axios.get(API.SUBSCRIPTION_DATE_INFO, { params: data })
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch(err => {
      console.log("err", err);
      return null;
    });
}

export function buyMoreAddOnPackage(data, toastError = false) {
  return createBase(API.BUY_MORE_ADDON_PACKAGE, data, [], false, toastError);
}

export async function getPaymentHistory(query) {
  const config = { loading: false };
  return axios.get(`${API.PAYMENT_HISTORY}?${query}`, config)
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      return null;
    });
}
