import { API } from "@api";
import {
  getDetailBase,
  getAllBase, getBase,
  getAllPaginationBase,
} from "@services/Base";
import axios from "axios";

export async function getAllTransactionByUser(loading) {
  const config = { loading };
  return axios.get(`${API.PAYMENT_HISTORY}`, config)
    .then(response => {
      if (response.status === 200) return response?.data;
    })
    .catch((err) => {
      return null;
    })
}

export async function getAllTransactionByUserID(id) {
  return getDetailBase(API.PAYMENT_HISTORY_USER_ID, id)
}

export function getTransactionDetail(id) {
  return getDetailBase(API.TRANSACTION_ID, id,);
}

export async function getAllTransactions(loading = true) {
  const config = { loading };
  const populateOpts = ["userId", "subscriptionId.customerId", "subscriptionId.packageId"];

  return axios.get(`${API.ALL_TRANSACTIONS}/findAll?populate=${populateOpts.join(",")}`, config)
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      console.error("Error fetching all transactions:", err);
      return null;
    });
}

export async function getPaginationTransactions(paging, query, searchFields = [], populateOpts = [ "subscriptionId.customerId", "subscriptionId.packageId"], loading = true) {
  query.sort = query.sort || "-createdAt";
  return getAllPaginationBase(API.ALL_TRANSACTIONS, paging, query, searchFields, populateOpts, loading);
}
