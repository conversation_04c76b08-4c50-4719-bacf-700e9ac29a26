import { LINK } from "@link";

/**
 * <PERSON><PERSON><PERSON> nghĩa các admin routes và quyền truy cập
 */
export const ADMIN_ROUTES = {
  // Routes chỉ dành cho System Admin
  SYSTEM_ADMIN_ONLY: [
    LINK.ADMIN.SETTING,
    LINK.ADMIN.ROLE_PLAY_INSTRUCTION_MANAGEMENT,
    LINK.ADMIN.ROLE_PLAY_STATISTICS_OVERVIEW,
    // Thêm các routes khác chỉ dành cho System Admin
  ],

  // Routes dành cho Regular Admin (role = "admin" nhưng không phải System Admin)
  REGULAR_ADMIN_ALLOWED: [
    LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT,
    LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT,
    LINK.ADMIN.ROLE_PLAY_COURSE_STATISTICS,
  ],

  // Routes dành cho Teacher (type = "teacher")
  TEACHER_ALLOWED: [
    LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT,
    LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT,
    LINK.ADMIN.ROLE_PLAY_COURSE_STATISTICS,
  ]
};

/**
 * Kiểm tra xem user có quyền truy cập admin interface không
 * @param {Object} user - User object
 * @returns {boolean} - Có quyền truy cập hay không
 */
export const hasAdminAccess = (user) => {
  if (!user) return false;

  // System Admin có quyền truy cập tất cả
  if (user.isSystemAdmin) return true;

  // Teacher có quyền truy cập
  if (user.type === "teacher") return true;

  // Regular Admin có quyền truy cập hạn chế
  if (user.role === "admin") return true;

  return false;
};

/**
 * Kiểm tra xem user có quyền truy cập route cụ thể không
 * @param {Object} user - User object
 * @param {string} route - Route path
 * @returns {boolean} - Có quyền truy cập hay không
 */
export const hasRouteAccess = (user, route) => {
  if (!user || !route) return false;

  // System Admin có quyền truy cập tất cả routes
  if (user.isSystemAdmin) return true;

  // Teacher có quyền truy cập các routes được phép
  if (user.type === "teacher") {
    return ADMIN_ROUTES.TEACHER_ALLOWED.includes(route);
  }

  // Regular Admin chỉ có quyền truy cập các routes cụ thể
  if (user.role === "admin" && !user.isSystemAdmin) {
    return ADMIN_ROUTES.REGULAR_ADMIN_ALLOWED.includes(route);
  }

  return false;
};

/**
 * Lấy danh sách routes mà user có quyền truy cập
 * @param {Object} user - User object
 * @returns {Array} - Danh sách routes được phép
 */
export const getAllowedRoutes = (user) => {
  if (!user) return [];

  // System Admin có quyền truy cập tất cả
  if (user.isSystemAdmin) {
    return [
      ...ADMIN_ROUTES.SYSTEM_ADMIN_ONLY,
      ...ADMIN_ROUTES.REGULAR_ADMIN_ALLOWED,
    ];
  }

  // Teacher có quyền truy cập các routes được phép
  if (user.type === "teacher") {
    return ADMIN_ROUTES.TEACHER_ALLOWED;
  }

  // Regular Admin chỉ có quyền truy cập các routes cụ thể
  if (user.role === "admin" && !user.isSystemAdmin) {
    return ADMIN_ROUTES.REGULAR_ADMIN_ALLOWED;
  }

  return [];
};

/**
 * Kiểm tra xem user có phải System Admin không
 * @param {Object} user - User object
 * @returns {boolean}
 */
export const isSystemAdmin = (user) => {
  return user?.isSystemAdmin === true;
};

/**
 * Kiểm tra xem user có phải Regular Admin không (có role admin nhưng không phải System Admin)
 * @param {Object} user - User object
 * @returns {boolean}
 */
export const isRegularAdmin = (user) => {
  return user?.role === "admin" && !user?.isSystemAdmin;
};

/**
 * Kiểm tra xem user có phải Teacher không
 * @param {Object} user - User object
 * @returns {boolean}
 */
export const isTeacher = (user) => {
  return user?.type === "teacher";
};
