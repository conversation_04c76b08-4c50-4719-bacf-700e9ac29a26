import React from 'react';
import * as _redux from './setup';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { I18nextProvider } from 'react-i18next';

import AppRoutes from '@app/routing/AppRoutes';

import store from './setup/redux/Store';
import i18n from '@src/translations/i18n';
import axios from 'axios';

import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
dayjs.extend(relativeTime);

import '!file-loader?name=[name].[ext]!@src/asset/favicon/favicon.ico';

import '@app/styles/fonts.css';
import '@app/styles/tailwind.css';
import '@app/styles/antd/custom-antd.scss';
//import '@app/styles/main.scss';
import '@app/styles/layout.scss';
import '@app/styles/scroll.scss';
import { initGA } from "@src/ga";

_redux.setupAxios(axios, store);

initGA()

const AppDOM = <Provider store={store}>
  <I18nextProvider i18n={i18n}>
    <AppRoutes />
  </I18nextProvider>
</Provider>;


const root = createRoot(document.getElementById('root'));
root.render(window.self === window.top ? AppDOM : 'Clickjacking');


if (process.env.NODE_ENV === 'production') {
  const runtime = require('@lcdp/offline-plugin/runtime'); // eslint-disable-line global-require
  runtime.install({
    onUpdating: () => {
      console.log('SW Event:', 'onUpdating');
    },
    onUpdateReady: () => {
      console.log('SW Event:', 'onUpdateReady');
      // Tells to new SW to take control immediately
      runtime.applyUpdate();
    },
    onUpdated: () => {
      console.log('SW Event:', 'onUpdated');
      // Reload the webpage to load into the new version
      window.location.reload();
    },
    onUpdateFailed: () => {
      console.log('SW Event:', 'onUpdateFailed');
    },
  });
}
