import { API } from "@api";
import { CONSTANT } from "@src/constants/constant";

import { getNewToken } from "@services/Auth";

import * as app from "@src/ducks/app.duck";

let isAlreadyFetchingAccessToken = false;
let subscribers = [];

function onAccessTokenFetched(accessToken) {
  subscribers = subscribers.filter(callback => callback(accessToken));
}

function addSubscriber(callback) {
  subscribers.push(callback);
}

export default function setupAxios(axios, store) {
  axios.defaults.headers.Accept = "application/json";
  const { dispatch } = store;
  let countApiRequest = 0;
  let countApiResponse = 0;
  axios.interceptors.request.use(
    (config) => {
      //handle upload file: append file size to formData
      if (config.headers["Content-Type"] === "multipart/form-data" && config.data instanceof FormData) {
        const formData = new FormData();
        const originalFormData = config.data;
        for (const [key, value] of originalFormData.entries()) {
          if (value instanceof File) {
            formData.append('fileSize', value.size);
          }
          formData.append(key, value);
        }
        config.data = formData;
      }
      if (!config.hasOwnProperty("loading")) {
        config.loading = true;
      }
      if (config.loading) {
        countApiRequest++;
      }
      const {
        app: { isLoading },
      } = store.getState();

      const language = localStorage.getItem("i18nextLng");

      config.headers["accept-language"] = language;
      config.headers.i18nextlng = language;

      if (!isLoading && countApiRequest !== countApiResponse) {
        dispatch(app.actions.toggleLoading(true));
      }

      return config;
    },
    (err) => Promise.reject(err),
  );

  try {
    axios.interceptors.response.use(res => {
      if (res.config.loading) {
        countApiResponse++;
      }
      if (countApiRequest === countApiResponse) {
        dispatch(app.actions.toggleLoading(false));
      }
      return res;
    }, async (error) => {
      if (error?.config?.loading) {
        countApiResponse++;
      }
      if (countApiRequest === countApiResponse) {
        dispatch(app.actions.toggleLoading(false));
      }


      // handle refresh token
      if (error.response?.status === 401 && error.config?.url !== API.USER_REFRESH_TOKEN) {
        const originalRequest = error.config;
        if (originalRequest._retry) {
          // If you retry once, do not refresh the token again
          return Promise.reject(error);
        }
        originalRequest._retry = true;

        if (!isAlreadyFetchingAccessToken) {
          isAlreadyFetchingAccessToken = true;

          const apiResponse = await getNewToken();

          isAlreadyFetchingAccessToken = false;
          if (!!apiResponse.success) {
            onAccessTokenFetched();
            return axios(originalRequest);
          }
        }

        return new Promise((resolve) => {
          addSubscriber((_) => {
            resolve(axios(originalRequest));
          });
        });
      } else {
        return Promise.reject(error);
      }

      // end handle refresh token


    });
  } catch (error) {
    if (error?.config?.loading) {
      countApiResponse++;
    }
    if (countApiRequest === countApiResponse) {
      dispatch(app.actions.toggleLoading(false));
    }
  }
}