import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { connect } from "react-redux";

import { hasRouteAccess, hasAdminAccess } from "@src/utils/adminPermissions";
import { LINK } from "@link";
import NeedAccess from "@src/app/component/NeedAccess";

/**
 * Component bảo vệ admin routes dựa trên permissions
 * @param {Object} props - Props
 * @param {Object} props.user - User object từ Redux store
 * @param {React.ReactNode} props.children - Child components
 * @param {string} props.requiredRoute - Route cần kiểm tra quyền (optional)
 * @param {boolean} props.showNeedAccess - Hiển thị NeedAccess component thay vì redirect (default: true)
 * @param {string} props.redirectTo - Đường dẫn redirect khi không có quyền (default: "/")
 */
const AdminPermissionGuard = ({ 
  user, 
  children, 
  requiredRoute = null,
  showNeedAccess = true,
  redirectTo = "/"
}) => {
  const location = useLocation();
  const currentRoute = requiredRoute || location.pathname;

  // Kiểm tra quyền truy cập admin interface tổng quát
  if (!hasAdminAccess(user)) {
    return showNeedAccess ? <NeedAccess /> : <Navigate to={redirectTo} replace />;
  }

  // Kiểm tra quyền truy cập route cụ thể
  if (!hasRouteAccess(user, currentRoute)) {
    return showNeedAccess ? <NeedAccess /> : <Navigate to={redirectTo} replace />;
  }

  // Nếu có quyền, render children
  return children;
};

/**
 * HOC để wrap component với AdminPermissionGuard
 * @param {React.Component} WrappedComponent - Component cần bảo vệ
 * @param {Object} options - Tùy chọn
 * @param {string} options.requiredRoute - Route cần kiểm tra quyền
 * @param {boolean} options.showNeedAccess - Hiển thị NeedAccess component
 * @param {string} options.redirectTo - Đường dẫn redirect
 */
export const withAdminPermission = (WrappedComponent, options = {}) => {
  const WithAdminPermissionComponent = (props) => {
    return (
      <AdminPermissionGuard {...options}>
        <WrappedComponent {...props} />
      </AdminPermissionGuard>
    );
  };

  WithAdminPermissionComponent.displayName = `withAdminPermission(${WrappedComponent.displayName || WrappedComponent.name})`;
  
  return connect(mapStateToProps)(WithAdminPermissionComponent);
};

/**
 * Hook để kiểm tra permissions trong functional components
 * @param {string} route - Route cần kiểm tra (optional)
 * @returns {Object} - Object chứa thông tin permissions
 */
export const useAdminPermissions = (route = null) => {
  const location = useLocation();
  const currentRoute = route || location.pathname;
  
  // Lấy user từ Redux store (cần implement useSelector)
  // Tạm thời return mock data, sẽ cập nhật sau
  const user = null; // TODO: Implement useSelector to get user from store
  
  return {
    hasAccess: hasAdminAccess(user),
    hasRouteAccess: hasRouteAccess(user, currentRoute),
    isSystemAdmin: user?.isSystemAdmin || false,
    isRegularAdmin: user?.role === "admin" && !user?.isSystemAdmin,
    isTeacher: user?.type === "teacher",
    currentRoute
  };
};

// Map Redux state to props
function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(AdminPermissionGuard);
