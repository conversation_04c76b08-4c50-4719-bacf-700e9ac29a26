{"name": "webpack5-demo", "version": "1.0.0", "description": "", "repository": {"type": "git", "url": "https://github.com/anhlt2710/webpack5-demo"}, "scripts": {"build:clean": "rimraf ./build", "start": "node server", "build": "cross-env NODE_ENV=production webpack --config webpack/webpack.prod.babel.js --color --progress", "start:prod": "cross-env NODE_ENV=production node server", "start:production": "npm run build:clean && npm run build && npm run start:prod", "lint": "eslint src"}, "author": "AnhLT", "license": "", "devDependencies": {"@babel/core": "^7.23.2", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-react-constant-elements": "^7.22.5", "@babel/plugin-transform-react-inline-elements": "^7.22.5", "@babel/preset-env": "^7.23.2", "@babel/preset-react": "^7.22.15", "@babel/register": "^7.22.15", "@eslint/js": "^9.17.0", "@lcdp/offline-plugin": "^5.1.1", "@types/sortablejs": "^1.15.7", "babel-loader": "^9.1.3", "babel-plugin-import": "^1.13.8", "babel-plugin-lodash": "^3.3.4", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "compression": "^1.7.4", "compression-webpack-plugin": "^10.0.0", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "dotenv-webpack": "^8.0.1", "esbuild-loader": "^4.2.2", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.3", "file-loader": "^6.2.0", "globals": "^15.14.0", "html-loader": "^4.2.0", "html-webpack-plugin": "^5.5.3", "http-proxy-middleware": "^2.0.6", "less": "^4.2.0", "less-loader": "^11.1.3", "memfs": "^4.6.0", "postcss": "^8.4.31", "postcss-import": "^15.1.0", "postcss-loader": "^7.3.3", "postcss-preset-env": "^9.2.0", "process": "^0.11.10", "react-app-polyfill": "^3.0.0", "resolve-url-loader": "^5.0.0", "rimraf": "^5.0.5", "sass": "^1.69.3", "sass-loader": "^13.3.2", "string-width": "^6.1.0", "style-loader": "^3.3.3", "svg-url-loader": "^8.0.0", "terser-webpack-plugin": "^5.3.9", "url-loader": "^4.1.1", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-middleware": "^6.1.1", "webpack-hot-middleware": "^2.25.4"}, "dependencies": {"@ant-design/charts": "v1", "@ant-design/icons": "^5.2.6", "@ckeditor/ckeditor5-react": "^9.1.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@reduxjs/toolkit": "^1.9.7", "antd": "5.18.3", "axios": "^1.7.2", "ckeditor5": "^43.1.0", "clsx": "^2.0.0", "dayjs": "^1.11.10", "dom-to-image-more": "^3.3.0", "express": "^4.18.2", "html-to-image": "^1.11.11", "html-to-pdf": "^0.1.11", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.1", "i18next": "^23.5.1", "i18next-browser-languagedetector": "^7.1.0", "ip": "^1.1.8", "js-cookie": "^3.0.5", "js-file-download": "^0.4.12", "jspdf": "^2.5.2", "jwt-decode": "^3.1.2", "lodash": "4.17.21", "lottie-react": "^2.4.0", "minimist": "^1.2.8", "moment": "^2.29.4", "path-browserify": "^1.0.1", "pdf-lib": "^1.17.1", "prop-types": "^15.8.1", "query-string": "^8.1.0", "rc-slider": "10.6.2", "react": "^18.2.0", "react-audio-visualize": "^1.2.0", "react-dom": "^18.2.0", "react-dropzone": "14.2.3", "react-ga4": "^2.1.0", "react-i18next": "^13.3.0", "react-image-crop": "^11.0.1", "react-loading": "^2.0.3", "react-markdown": "10.0.0", "react-pdf": "^7.5.1", "react-player": "^2.15.1", "react-qr-code": "^2.0.12", "react-redux": "^8.1.3", "react-router-dom": "^6.17.0", "react-sortablejs": "^6.1.4", "react-virtualized": "9.22.4", "redux": "^4.2.1", "redux-saga": "^1.2.3", "remark-gfm": "3.0.1", "socket.io-client": "^4.8.1", "sortablejs": "^1.15.2", "tailwindcss": "^3.3.3", "util": "^0.12.5", "uuid": "^11.0.3"}}