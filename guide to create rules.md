Mastering Cursor Rules: A Developer's Guide to Smart AI Integration
#
cursor
#
rules
#
rails
What are Cursor Rules?
Cursor Rules are configuration files that define how AI should interact with your codebase. They provide context-aware assistance by setting guidelines, constraints, and behavioral patterns for AI interactions.


2. Project-Wide Rules (.cursorrules)
A single file in your project root that serves as the primary guidebook for project-specific conventions:

# Framework Standards
- Follow Rails architectural patterns
- Use service objects for business logic
- Reference patterns (@service-objects.md)

# Quality Controls
- Follow RuboCop guidelines
- Enforce test coverage minimums
- Ban SQL queries in views
3. Pattern-Specific Rules (.cursor/rules/*.mdc)
Introduced in Cursor v0.45, these Markdown-based Domain Configuration files target specific file patterns:

---
Description: Rails Controller Standards
Globs: app/controllers/**/*.rb
---

# Guidelines
- Keep controllers skinny
- Use before_action for repeating logic
- Follow RESTful conventions
Note: In the current version, those rules are added to prompt only in Agent mode. Chat and Normal compose are still using .cursorrules

Agentic Approach: The Game Changer
Modern Cursor Rules shifted from passive rule listing to active agent instruction. Example:

You are instructa, a senior Rails developer with superpowers! ⚡

# Agent Behavior
- Read Roadmap.md first
- Plan database schema changes
- Use ViewComponents for complex UI
- Write system tests for critical paths

# Code Standards
- Follow Rails conventions
- Use concerns for shared logic
- Tests must pass before merge
Pro Tips
1. Reference Architecture Using "@ syntax"
Instead of writing lengthy explanations, reference your documentation:

# Bad
- Controllers should use service objects for complex business logic...

# Good
- Follow service object patterns defined in @docs/architecture/services.md
- See implementation examples in @docs/examples/service_objects/
2. Strategic Glob Patterns
Create focused, hierarchical patterns:

# Too broad
Globs: **/*.rb

# Better
Globs:
  app/services/**/*.rb
  app/models/**/*.rb
  !app/models/legacy/**/*.rb  # Exclude legacy
3. Combining Rules
Create composable rule sets:

# .cursor/rules/base_ruby.mdc
Description: Base Ruby standards

# .cursor/rules/rails_controllers.mdc
@base_ruby.mdc
Description: Controller-specific rules
Globs: app/controllers/**/*.rb
4. Maintainable Organization
Structure rules by domain:

.cursor/rules/
  ├── rails8.mdc
  ├── models/
  │   ├── active_record.mdc
  │   └── postgresql.mdc
  ├── controllers/
  │   ├── api.mdc
  │   └── web.mdc
  └── views/
      ├── erb.mdc
      └── components.mdc
