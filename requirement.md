# Hướng Dẫn Phát Triển Chức Năng Hiển Thị Thông Báo Sắp Hết Lượt Sử Dụng Gói Dịch Vụ

## 1. <PERSON><PERSON>ả Chức Năng
Chức năng này hiển thị thông báo khi khách hàng (KH) sắp hết lượt sử dụng của gói dịch vụ, dựa trên số lượt chấm còn lại hoặc số ngày còn lại của gói tháng.

### 1.1. <PERSON><PERSON><PERSON><PERSON> Kiện Hiển Thị Thông Báo
- **G<PERSON><PERSON> tháng**:
  - N<PERSON>u số lượt còn lại ≤ 3, hiển thị thông báo ngay.
  - Nếu số lượt > 3 nhưng gói tháng còn 3 ngày, hiển thị thông báo.
- **G<PERSON>i lượt**: <PERSON><PERSON><PERSON> thị thông báo khi còn 3 lư<PERSON><PERSON> chấm.
- **<PERSON><PERSON> gói tháng và gói lượt**:
  - <PERSON><PERSON><PERSON><PERSON> chấm trong gói tháng được trừ trước.
  - <PERSON><PERSON><PERSON> hết lượt trong gói tháng, kiểm tra tổng lượt còn lại của gói lượt.
  - Nếu tổng lượt còn lại ≤ 3, hiển thị thông báo ngay.
  - Nếu còn > 3 lượt nhưng gói tháng còn 3 ngày, hiển thị thông báo.
- **Tần suất**: Pop-up hiển thị 1 lần/ngày khi thỏa mãn điều kiện.
- **Nút Renew**: Luôn hiển thị trên pop-up để KH gia hạn.

---

## 2. Phát Triển Backend

### 2.1. Database
Thêm các trường cần thiết vào bảng người dùng:
- `subscription_end_date`: Ngày hết hạn gói tháng.
- `remaining_credits`: Số lượt còn lại.
- `last_popup_shown`: Lần cuối hiển thị pop-up.

### 2.2. API
#### 2.2.1. API Kiểm Tra Trạng Thái Gói Dịch Vụ
- **Endpoint**: `GET /api/user/package-status`
- **Logic**:
  1. Kiểm tra điều kiện hiển thị thông báo theo quy tắc đã đề ra.
  2. Trả về trạng thái có hiển thị pop-up hay không.

#### 2.2.2. API Cập Nhật Lần Cuối Hiển Thị Pop-up
- **Endpoint**: `POST /api/user/update-popup-status`
- **Logic**:
  1. Ghi nhận thời điểm pop-up được hiển thị.

---

## 3. Phát Triển Frontend

### 3.1. Cập Nhật Giao Diện Người Dùng
- **Thông tin tài khoản**: Hiển thị nút "Renew" khi gói sắp hết.
- **Pop-up thông báo**:
  - Nội dung động dựa trên điều kiện của gói.
  - Nút "Renew" dẫn đến trang gia hạn.
  
### 3.2. Gọi API Kiểm Tra Gói Dịch Vụ
- Khi người dùng truy cập ứng dụng:
  1. Gọi `GET /api/user/me`.
  2. Kiểm tra các điều kiện "renewPackage",
    "renewSpeaking",
    "renewWriting" để hiển thị pop-up, nếu một trong các trường thông tin trên là true hiển thị pop-up thông báo.
  3. Chỉ hiển thị thông báo mỗi ngày 1 lần duy nhất khi người dùng lần đầu truy cập API /me

### 3.3. Tích Hợp Pop-up
- **Khi thỏa mãn điều kiện**, hiển thị pop-up từ giao diện Figma.
- Pop-up chỉ hiển thị **1 lần/ngày**.
- Khi bấm "Renew", chuyển hướng đến trang các gói giá /pricing.

---

## 4. Triển Khai & Kiểm Thử

### 4.1. Kiểm Thử Backend
- Kiểm tra API trả về đúng điều kiện hiển thị.
- Kiểm tra lưu lịch sử hiển thị pop-up.

### 4.2. Kiểm Thử Frontend
- Kiểm tra hiển thị pop-up theo đúng điều kiện.
- Kiểm tra nút "Renew" hoạt động đúng.
- Kiểm tra pop-up không hiển thị quá 1 lần/ngày.

### 4.3. Kiểm Thử Tổng Quan
- Tạo nhiều tài khoản với các tình huống khác nhau để kiểm tra.
- Kiểm tra hiển thị chính xác theo từng loại gói dịch vụ.

---

## 5. Link Giao Diện Figma
- **Thông tin tài khoản**: [Figma](https://www.figma.com/design/9R0RqB7FcKdqVhxOMtP2w2/Reseach-Clickee?node-id=4760-1915&t=tVOZUjnlyV6YJ2mJ-4)
- **Pop-up thông báo**: [Figma](https://www.figma.com/design/9R0RqB7FcKdqVhxOMtP2w2/Reseach-Clickee?node-id=5255-164&t=tVOZUjnlyV6YJ2mJ-4)

